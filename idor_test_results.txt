=== IDOR Test: GET /api/users/{id} - User by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.621180s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.634121s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.645584s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.624989s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.661284s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.740899s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.690661s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.625296s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.648755s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.625401s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.624335s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.630597s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.674718s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.634462s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.615048s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.668781s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.723925s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/attachmentCategory/{id} - Attachment Category by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.707063s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.644725s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.696951s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.656824s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 4.730652s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.629198s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.686374s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.698014s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.668019s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.667449s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.634570s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.817883s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.644861s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.626012s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.622756s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.656580s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.683395s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/citckeywords/{id} - CITC Keywords by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.651854s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.739808s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.690481s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.656084s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.808043s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.691643s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.643151s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.643391s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.700477s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.627287s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.654295s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.707142s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.752200s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.635571s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.633737s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.671338s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.665000s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/contract-requests/{id} - Contract Request by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.684421s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.654284s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.732486s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.695174s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.663285s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.640812s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.659255s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.654480s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.646193s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.664974s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.623701s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.664658s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.638327s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.630579s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.653629s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.611506s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.615811s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/sender-requests/{id} - Sender Request by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.618526s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.653058s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.635431s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.686553s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.658511s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.663237s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.698090s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.646114s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.705917s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.646393s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.689737s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.716079s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.700587s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.611636s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.640389s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.710240s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.647282s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/shortcodes/getById - Short Code by ID (parameter) ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.643374s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.697571s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.753179s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.677901s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.650601s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.653074s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.647129s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.652151s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.614671s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.612072s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.684728s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.638205s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.677958s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.656193s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.628512s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.671492s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.665218s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/senders/{id} - Sender by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.676029s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.696732s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.689578s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.633760s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.635901s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.651750s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.727047s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.664221s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.644045s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.705962s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.620611s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.660775s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.632223s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.655531s
ID: test | HTTP: 200 | Size: 8035b | Time: 0.659355s
ID: null | HTTP: 200 | Size: 8035b | Time: 0.647796s
ID: undefined | HTTP: 200 | Size: 8035b | Time: 0.745571s

Summary: 17 potentially vulnerable IDs found: 1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined

=== IDOR Test: GET /api/certificate/{id} - Certificate by ID ===
ID: 1 | HTTP: 200 | Size: 8035b | Time: 0.806699s
ID: 2 | HTTP: 200 | Size: 8035b | Time: 0.632694s
ID: 3 | HTTP: 200 | Size: 8035b | Time: 0.898997s
ID: 5 | HTTP: 200 | Size: 8035b | Time: 0.618995s
ID: 10 | HTTP: 200 | Size: 8035b | Time: 0.702412s
ID: 100 | HTTP: 200 | Size: 8035b | Time: 0.724692s
ID: 999 | HTTP: 200 | Size: 8035b | Time: 0.679086s
ID: 1000 | HTTP: 200 | Size: 8035b | Time: 0.673573s
ID: 9999 | HTTP: 200 | Size: 8035b | Time: 0.669911s
ID: 99999 | HTTP: 200 | Size: 8035b | Time: 0.624688s
ID: -1 | HTTP: 200 | Size: 8035b | Time: 0.651422s
ID: 0 | HTTP: 200 | Size: 8035b | Time: 0.753415s
ID: admin | HTTP: 200 | Size: 8035b | Time: 0.645690s
ID: user | HTTP: 200 | Size: 8035b | Time: 0.667548s
