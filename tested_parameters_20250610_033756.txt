# Comprehensive Injection Scanner - Tested Parameters
# Scan completed: 2025-06-10 03:37:56
# Total parameters tested: 33

2025-06-10 03:33:56 - GET https://careers.zain.sa/.well-known/sgcaptcha/?r=%2F - query parameter: r=/
2025-06-10 03:33:56 - GET https://digital-new-app.sa.zain.com/TSbd/0851fd02ccab20003c8e3b92af90945b4a21d08ce96023c0718bc462dfd957bb06e1f02f4a9f8a74?type=2 - query parameter: type=2
2025-06-10 03:33:56 - GET https://accounts.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2 - query parameter: type=2
2025-06-10 03:33:56 - GET https://connect.sa.zain.com/TSbd/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=2 - query parameter: type=2
2025-06-10 03:33:56 - POST https://add.sa.zain.com/report-problem - form parameter: _token=test_value
2025-06-10 03:33:56 - POST https://add.sa.zain.com/report-problem - form parameter: api_url=test_value
2025-06-10 03:33:56 - POST https://add.sa.zain.com/report-problem - form parameter: subscriber=test_value
2025-06-10 03:33:56 - POST https://add.sa.zain.com/report-problem - form parameter: description=test_value
2025-06-10 03:33:56 - POST https://add.sa.zain.com/report-problem - form parameter: images=test_value
2025-06-10 03:33:56 - GET https://iaccess.sa.zain.com/TSPD/0851fd02ccab2000a32d463430c269d006d4030de0d549e2d7cc96f8e5dd6d378369335f32b50386?type=5 - query parameter: type=5
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F&amp - query parameter: url=https://api.sim.sa.zain.com/
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F& - query parameter: url=https://api.sim.sa.zain.com/
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F&format=xml - query parameter: url=https://api.sim.sa.zain.com/
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F&format=xml - query parameter: format=xml
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fapi.sim.sa.zain.com%2F - query parameter: url=https://api.sim.sa.zain.com/
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: log=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: pwd=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: rememberme=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: wp-submit=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: redirect_to=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php - form parameter: testcookie=test_value
2025-06-10 03:33:56 - GET https://api.sim.sa.zain.com/wp-login.php?action=lostpassword - query parameter: action=lostpassword
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php?action=lostpassword - form parameter: user_login=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php?action=lostpassword - form parameter: redirect_to=test_value
2025-06-10 03:33:56 - POST https://api.sim.sa.zain.com/wp-login.php?action=lostpassword - form parameter: wp-submit=test_value
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/AlertsDashboard?status=3&tenant= - query parameter: status=3
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/AlertsDashboard?status=2&tenant= - query parameter: status=2
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/AlertsDashboard?status=1&tenant= - query parameter: status=1
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/SenderRequestStatusDashboard?status=4&dateFrom= - query parameter: status=4
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/SenderRequestStatusDashboard?status=0&dateFrom= - query parameter: status=0
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/SenderRequestStatusDashboard?status=1&dateFrom= - query parameter: status=1
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/SenderRequestStatusDashboard?status=3&dateFrom= - query parameter: status=3
2025-06-10 03:33:56 - GET https://smsidmanagment.sa.zain.com/SenderRequestStatusDashboard?status=2&dateFrom= - query parameter: status=2
