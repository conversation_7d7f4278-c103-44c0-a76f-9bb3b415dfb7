#!/bin/bash

# SMS ID Management API Testing Script
# Domain: https://smsidmanagment.sa.zain.com/
# Usage: ./test_api_endpoints.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

# Output file for results
OUTPUT_FILE="api_test_results.txt"
VULNERABLE_FILE="vulnerable_endpoints.txt"

# Clear previous results
> "$OUTPUT_FILE"
> "$VULNERABLE_FILE"

echo -e "${BLUE}=== SMS ID Management API Security Testing ===${NC}"
echo -e "${YELLOW}Testing endpoints for: $BASE_URL${NC}"
echo ""

# Function to test endpoint
test_endpoint() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    
    echo -e "${BLUE}Testing: $method $endpoint${NC}"
    echo "Testing: $method $endpoint - $description" >> "$OUTPUT_FILE"
    
    # Prepare curl command
    local curl_cmd="curl -s -w 'HTTP_CODE:%{http_code}|SIZE:%{size_download}|TIME:%{time_total}' -X $method"
    
    # Add common headers
    curl_cmd="$curl_cmd -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'"
    curl_cmd="$curl_cmd -H 'Accept: application/json, text/plain, */*'"
    curl_cmd="$curl_cmd -H 'Accept-Language: en-US,en;q=0.9'"
    curl_cmd="$curl_cmd -H 'Cache-Control: no-cache'"
    curl_cmd="$curl_cmd -H 'Pragma: no-cache'"
    
    # Add data for POST/PUT requests
    if [[ "$method" == "POST" || "$method" == "PUT" ]] && [[ -n "$data" ]]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
        curl_cmd="$curl_cmd -d '$data'"
    fi
    
    # Add URL
    curl_cmd="$curl_cmd '$BASE_URL$endpoint'"
    
    # Execute curl command
    local response=$(eval $curl_cmd 2>/dev/null)
    
    # Extract HTTP code, size, and time
    local http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    local size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    local time=$(echo "$response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    
    # Remove status info from response
    local clean_response=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*|SIZE:[0-9]*|TIME:[0-9.]*//')
    
    # Color code based on HTTP status
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        echo -e "${GREEN}✓ HTTP $http_code - Size: ${size}b - Time: ${time}s${NC}"
        echo "✓ HTTP $http_code - Size: ${size}b - Time: ${time}s" >> "$OUTPUT_FILE"
        echo "$method $endpoint - HTTP $http_code" >> "$VULNERABLE_FILE"
    elif [[ "$http_code" =~ ^3[0-9][0-9]$ ]]; then
        echo -e "${YELLOW}→ HTTP $http_code - Size: ${size}b - Time: ${time}s${NC}"
        echo "→ HTTP $http_code - Size: ${size}b - Time: ${time}s" >> "$OUTPUT_FILE"
    elif [[ "$http_code" =~ ^4[0-9][0-9]$ ]]; then
        echo -e "${YELLOW}⚠ HTTP $http_code - Size: ${size}b - Time: ${time}s${NC}"
        echo "⚠ HTTP $http_code - Size: ${size}b - Time: ${time}s" >> "$OUTPUT_FILE"
    elif [[ "$http_code" =~ ^5[0-9][0-9]$ ]]; then
        echo -e "${RED}✗ HTTP $http_code - Size: ${size}b - Time: ${time}s${NC}"
        echo "✗ HTTP $http_code - Size: ${size}b - Time: ${time}s" >> "$OUTPUT_FILE"
        echo "$method $endpoint - HTTP $http_code (SERVER ERROR)" >> "$VULNERABLE_FILE"
    else
        echo -e "${RED}✗ Connection failed or timeout${NC}"
        echo "✗ Connection failed or timeout" >> "$OUTPUT_FILE"
    fi
    
    # Log response if interesting
    if [[ ${#clean_response} -gt 10 ]]; then
        echo "Response preview: ${clean_response:0:200}..." >> "$OUTPUT_FILE"
    fi
    
    echo "" >> "$OUTPUT_FILE"
    echo ""
    
    # Small delay to avoid overwhelming the server
    sleep 0.5
}

echo -e "${YELLOW}=== Testing GET Endpoints ===${NC}"

# GET Endpoints
test_endpoint "GET" "/api/attachmentCategory?name=test&pageIndex=1&pageSize=10" "" "Attachment Categories"
test_endpoint "GET" "/api/attachmentCategory/1" "" "Attachment Category by ID"
test_endpoint "GET" "/api/attachmentCategory/getall" "" "All Attachment Categories"
test_endpoint "GET" "/api/senders/getSuggestedNames?clientName=test&senderType=1" "" "Suggested Sender Names"
test_endpoint "GET" "/api/mci-controller/verifyCR?unifiedNumber=**********" "" "Verify CR Number"
test_endpoint "GET" "/api/kpi-dashboard/get-alert-days-configuration?type=1" "" "Alert Days Configuration"
test_endpoint "GET" "/api/citckeywords?name=test&pageIndex=1&pageSize=10" "" "CITC Keywords"
test_endpoint "GET" "/api/citckeywords/1" "" "CITC Keyword by ID"
test_endpoint "GET" "/api/citckeywords/getall" "" "All CITC Keywords"
test_endpoint "GET" "/api/payment-transaction/getallbalanceinvoices?invoiceNumber=&providerName=&paymentDateFrom=&paymentDateTo=&pageIndex=1&pageSize=10" "" "Balance Invoices"
test_endpoint "GET" "/api/payment-transaction/downloadinvoice?invoiceIdentifier=test&isBalanceInvoice=true" "" "Download Invoice"
test_endpoint "GET" "/api/contract-requests/get-contract-options" "" "Contract Options"
test_endpoint "GET" "/api/providers/is-provider-locked" "" "Provider Lock Status"
test_endpoint "GET" "/api/providers/is-provider-expired" "" "Provider Expiry Status"
test_endpoint "GET" "/api/contract-requests/contract-dashboard-boxes" "" "Contract Dashboard"
test_endpoint "GET" "/api/contract-requests/1" "" "Contract Request by ID"
test_endpoint "GET" "/api/contract-requests/attachments/1" "" "Contract Attachments"
test_endpoint "GET" "/api/contract-requests/get-request-approval-history/1" "" "Request Approval History"
test_endpoint "GET" "/api/senders/get-sender-for-contract-request?clientName=test&crNumber=123&customerType=1&enterpriseUnifiedNumber=123" "" "Sender for Contract"
test_endpoint "GET" "/api/report/connectivity-activation-report?requestId=1&senderName=test&pageIndex=1&pageSize=10" "" "Connectivity Report"
test_endpoint "GET" "/api/sender-conectivity-detail?senderId=1" "" "Sender Connectivity Details"
test_endpoint "GET" "/api/senders/1" "" "Sender by ID"
test_endpoint "GET" "/api/providers/is-provider-dissconected" "" "Provider Disconnection Status"
test_endpoint "GET" "/api/sender-conectivity-detail-log?connectivityDetailsId=1" "" "Connectivity Detail Log"
test_endpoint "GET" "/api/sender-conectivity-detail/connectivity-details-configration" "" "Connectivity Configuration"
test_endpoint "GET" "/api/change-sender-type-requests" "" "Change Sender Type Requests"
test_endpoint "GET" "/api/RejectionReason/get-by-request-type?requestType=1" "" "Rejection Reasons"
test_endpoint "GET" "/api/senders" "" "All Senders"
test_endpoint "GET" "/api/complaint/get-details/1" "" "Complaint Details"
test_endpoint "GET" "/api/complaint/attachments/1" "" "Complaint Attachments"
test_endpoint "GET" "/api/sender-request-support-page/get-sender-support-details?senderName=test" "" "Sender Support Details"
test_endpoint "GET" "/api/sender-requests/1" "" "Sender Request by ID"
test_endpoint "GET" "/api/sender-requests/attachments/1" "" "Sender Request Attachments"
test_endpoint "GET" "/api/shortcodes?operatorId=1&name=test&pageIndex=1&pageSize=10" "" "Short Codes"
test_endpoint "GET" "/api/shortcodes/getall" "" "All Short Codes"
test_endpoint "GET" "/api/shortcodes/getById?id=1" "" "Short Code by ID"
test_endpoint "GET" "/api/users?username=test&email=<EMAIL>&mobileNumber=123456789&pageIndex=1&pageSize=10" "" "Users"
test_endpoint "GET" "/api/users/1" "" "User by ID"
test_endpoint "GET" "/api/certificate/1" "" "Certificate by ID"
test_endpoint "GET" "/api/UserInfo" "" "User Information"
test_endpoint "GET" "/config.json" "" "Configuration File"

echo -e "${YELLOW}=== Testing POST Endpoints ===${NC}"

# POST Endpoints with sample JSON data
test_endpoint "POST" "/api/providerrequest" '{"name":"test","crNumber":"123","accreditationNumber":"456"}' "Provider Request"
test_endpoint "POST" "/api/sender-requests" '{"senderName":"test","requestType":1}' "Sender Request"
test_endpoint "POST" "/api/kpi-dashboard/alerts-list" '{"pageIndex":1,"pageSize":10}' "KPI Alerts List"
test_endpoint "POST" "/api/attachmentCategory" '{"name":"test"}' "Create Attachment Category"
test_endpoint "POST" "/api/citckeywords" '{"name":"test"}' "Create CITC Keyword"
test_endpoint "POST" "/api/contract-requests/get-contract-requests-sql" '{"pageIndex":1,"pageSize":10}' "Contract Requests SQL"
test_endpoint "POST" "/api/contract-requests" '{"senderIds":[1],"contractType":1}' "Create Contract Request"
test_endpoint "POST" "/api/sender-conectivity-detail" '{"senderId":1,"details":"test"}' "Sender Connectivity Detail"
test_endpoint "POST" "/api/complaint/edit-complaint" '{"id":1,"description":"test"}' "Edit Complaint"
test_endpoint "POST" "/api/shortcodes" '{"name":"test","operatorId":1}' "Create Short Code"
test_endpoint "POST" "/api/users" '{"username":"test","email":"<EMAIL>"}' "Create User"

echo -e "${YELLOW}=== Testing PUT Endpoints ===${NC}"

# PUT Endpoints
test_endpoint "PUT" "/api/attachmentCategory" '{"id":1,"name":"updated"}' "Update Attachment Category"
test_endpoint "PUT" "/api/contract-requests/approve" '{"ids":[1],"comment":"approved"}' "Approve Contract Request"
test_endpoint "PUT" "/api/senders/deactivate" '{"ids":[1]}' "Deactivate Senders"
test_endpoint "PUT" "/api/citckeywords" '{"id":1,"name":"updated"}' "Update CITC Keyword"
test_endpoint "PUT" "/api/sender-requests" '{"id":1,"status":"updated"}' "Update Sender Request"
test_endpoint "PUT" "/api/senders" '{"id":1,"name":"updated"}' "Update Sender"
test_endpoint "PUT" "/api/shortcodes" '{"id":1,"name":"updated"}' "Update Short Code"
test_endpoint "PUT" "/api/users" '{"id":1,"username":"updated"}' "Update User"

echo -e "${YELLOW}=== Testing DELETE Endpoints ===${NC}"

# DELETE Endpoints
test_endpoint "DELETE" "/api/attachmentCategory?id=1" "" "Delete Attachment Category"
test_endpoint "DELETE" "/api/citckeywords?id=1" "" "Delete CITC Keyword"
test_endpoint "DELETE" "/api/contract-requests?id=1&comment=deleted" "" "Delete Contract Request"
test_endpoint "DELETE" "/api/shortcodes?id=1" "" "Delete Short Code"

echo -e "${YELLOW}=== Testing Common Vulnerabilities ===${NC}"

# Test for SQL Injection
test_endpoint "GET" "/api/users/1' OR '1'='1" "" "SQL Injection Test 1"
test_endpoint "GET" "/api/attachmentCategory/1; DROP TABLE users--" "" "SQL Injection Test 2"
test_endpoint "GET" "/api/citckeywords?name=test' UNION SELECT * FROM users--&pageIndex=1&pageSize=10" "" "SQL Injection Test 3"

# Test for XSS
test_endpoint "GET" "/api/users?username=<script>alert('XSS')</script>&email=test&mobileNumber=123&pageIndex=1&pageSize=10" "" "XSS Test 1"
test_endpoint "POST" "/api/attachmentCategory" '{"name":"<img src=x onerror=alert(1)>"}' "XSS Test 2"

# Test for Path Traversal
test_endpoint "GET" "/api/../../../etc/passwd" "" "Path Traversal Test 1"
test_endpoint "GET" "/api/certificate/../../../config.json" "" "Path Traversal Test 2"

# Test for IDOR (Insecure Direct Object Reference)
test_endpoint "GET" "/api/users/999999" "" "IDOR Test - High User ID"
test_endpoint "GET" "/api/contract-requests/999999" "" "IDOR Test - High Contract ID"
test_endpoint "GET" "/api/sender-requests/-1" "" "IDOR Test - Negative ID"

# Test for Information Disclosure
test_endpoint "GET" "/api/users" "" "Information Disclosure - All Users"
test_endpoint "GET" "/api/senders" "" "Information Disclosure - All Senders"
test_endpoint "GET" "/.env" "" "Environment File Test"
test_endpoint "GET" "/web.config" "" "Web Config Test"
test_endpoint "GET" "/api/config" "" "API Config Test"

echo -e "${GREEN}=== Testing Complete ===${NC}"
echo -e "${BLUE}Results saved to: $OUTPUT_FILE${NC}"
echo -e "${BLUE}Potentially vulnerable endpoints saved to: $VULNERABLE_FILE${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "Total endpoints tested: $(grep -c "Testing:" "$OUTPUT_FILE")"
echo "Successful responses (2xx): $(grep -c "✓ HTTP 2" "$OUTPUT_FILE")"
echo "Redirects (3xx): $(grep -c "→ HTTP 3" "$OUTPUT_FILE")"
echo "Client errors (4xx): $(grep -c "⚠ HTTP 4" "$OUTPUT_FILE")"
echo "Server errors (5xx): $(grep -c "✗ HTTP 5" "$OUTPUT_FILE")"
echo "Connection failures: $(grep -c "✗ Connection failed" "$OUTPUT_FILE")"
