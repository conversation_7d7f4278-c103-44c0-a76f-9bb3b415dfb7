              url = window.$config.HostName + 'api/providerrequest';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory?name=""' + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-requests/send-add-request-email';
              _context3.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/senders/getSuggestedNames?clientName=' + clientName + '&senderType=' + senderType;
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/mci-controller/verifyCR?unifiedNumber=' + unifiedNumber;
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/alerts-list';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/get-alert-days-configuration?type=' + type;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/export-alerts-list';
              _context3.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory?name=' + name + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              response = _context2.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory/getall';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory';
              _context4.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory';
              _context5.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/attachmentCategory';
              _context6.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + (isExport ? 'api/payment-transaction/exportgetallbalanceinvoices?' : 'api/payment-transaction/getallbalanceinvoices?') + 'invoiceNumber=' + invoiceNumber + '&providerName=' + providerName + '&paymentDateFrom=' + paymentDateFrom + '&paymentDateTo=' + paymentDateTo + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/downloadinvoice' + '?invoiceIdentifier=' + transactionReference + '&isBalanceInvoice=' + isBalanceInvoice;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/citckeywords?name=' + name + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/citckeywords/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              response = _context2.sent;
--
              url = window.$config.HostName + 'api/citckeywords/getall';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/citckeywords';
              _context4.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/citckeywords';
              _context5.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/citckeywords';
              _context6.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + 'api/contract-requests/get-contract-requests-sql';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              };
              _context2.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context2.sent;
--
              };
              _context3.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/contract-requests/get-contract-options';
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-locked';
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-expired';
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/contract-requests?id=' + id + '&comment=' + comment;
              _context7.next = 5;
              return this.axiosService.axiosInstance.delete(url);
            case 5:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/senders/deactivate';
              _context8.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context8.sent;
--
              };
              _context9.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context9.sent;
--
              url = window.$config.HostName + 'api/contract-requests/reject';
              _context0.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context0.sent;
--
              url = window.$config.HostName + 'api/contract-requests/contract-dashboard-boxes';
              _context1.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context1.sent;
--
              url = window.$config.HostName + 'api/contract-requests/' + id;
              _context10.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context10.sent;
--
              url = window.$config.HostName + 'api/contract-requests/send-contract-approve-email';
              _context11.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context11.sent;
--
              url = window.$config.HostName + 'api/sender-requests/delete-request-email';
              _context12.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context12.sent;
--
              url = window.$config.HostName + 'api/contract-requests/reject-contract-request-email';
              _context13.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context13.sent;
--
              url = window.$config.HostName + 'api/contract-requests/resend-contract-request-email';
              _context14.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context14.sent;
--
              url = window.$config.HostName + 'api/RejectionReason/get-by-request-type?requestType=' + requestType;
              _context15.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context15.sent;
--
              }
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/contract-requests/attachments/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/contract-requests/get-request-approval-history/' + id;
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/contract-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName, "api/senders/get-sender-for-contract-request?").concat(queryParams.toString());
              _context2.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context2.sent;
--
              url = "".concat(window.$config.HostName, "api/contract-requests/get-sender-for-contract-renew?").concat(queryParams.toString());
              _context3.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/contract-requests/send-add-contract-request-email';
              _context4.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/contract-requests/renew';
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/contract-requests/' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/contract-requests';
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context2.sent;
--
              url = "".concat(window.$config.HostName, "api/senders/get-sender-for-contract-request?").concat(queryParams.toString());
              _context3.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context3.sent;
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?").concat(params.toString());
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?senderId=").concat(encodeURIComponent(senderId));
              _context.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName, "api/senders/").concat(encodeURIComponent(senderId));
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-dissconected';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail-log?connectivityDetailsId=' + id;
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/connectivity-details-configration';
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail';
              _context6.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/mark-as-pending-addition?senderConectivityDetailId=' + id;
              _context7.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail';
              _context8.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  senderConectivityDetailId: senderConectivityDetailId
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?").concat(params.toString());
              _context.next = 7;
              return this.axiosService.axiosInstance.get(url);
            case 7:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/change-sender-type-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/change-sender-type-requests/approve';
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/senders/deactivate';
              _context3.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/change-sender-type-requests/resend';
              _context4.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/change-sender-type-requests/reject';
              _context5.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/change-sender-type-requests/' + id;
              _context6.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-sender-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              };
              _context2.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context2.sent;
--
              };
              _context3.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context3.sent;
--
              };
              _context4.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-requests-options';
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-report-options';
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-locked';
              _context7.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-expired';
              _context8.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context8.sent;
--
              url = window.$config.HostName + 'api/sender-requests?id=' + id + '&comment=' + comment;
              _context9.next = 5;
              return this.axiosService.axiosInstance.delete(url);
            case 5:
              result = _context9.sent;
--
              url = window.$config.HostName + 'api/senders/deactivate';
              _context0.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context0.sent;
--
              };
              _context1.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context1.sent;
--
              };
              _context10.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context10.sent;
--
              };
              _context11.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context11.sent;
--
              url = window.$config.HostName + 'api/sender-requests/reject';
              _context12.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context12.sent;
--
              url = window.$config.HostName + 'api/sender-requests/dashboardboxes';
              _context13.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context13.sent;
--
              url = window.$config.HostName + 'api/sender-requests/' + id;
              _context14.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context14.sent;
--
              url = window.$config.HostName + 'api/sender-requests/send-approve-email';
              _context15.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context15.sent;
--
              url = window.$config.HostName + 'api/sender-requests/delete-request-email';
              _context16.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context16.sent;
--
              url = window.$config.HostName + 'api/sender-requests/reject-request-email';
              _context17.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context17.sent;
--
              url = window.$config.HostName + 'api/sender-requests/resend-request-email';
              _context18.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context18.sent;
--
              url = window.$config.HostName + 'api/RejectionReason/get-by-request-type?requestType=' + requestType;
              _context19.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context19.sent;
--
              url = window.$config.HostName + 'api/senders/get-sender-status';
              _context20.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: {
                  senderName: senderName
--
              url = window.$config.HostName + 'api/sender-requests/get-extend-duration-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/senders/get-sender-for-renew-with-request';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/sender-requests/update-extend-duration-request';
              _context3.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-requests/renew';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?").concat(params.toString());
              _context2.next = 7;
              return this.axiosService.axiosInstance.get(url);
            case 7:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-senders-for-renew-by-senderIds';
              _context3.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context3.sent;
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?").concat(params.toString());
              _context4.next = 7;
              return this.axiosService.axiosInstance.get(url);
            case 7:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-requests/add-extend-duration-requests';
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/update-request-data';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/update-request-data/approve';
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/senders/deactivate';
              _context3.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/update-request-data/resend';
              _context4.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/update-request-data/reject';
              _context5.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/update-request-data/' + id;
              _context6.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/employee-statistics/get-boxes';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/employee-statistics/get-pie-chart';
              _context2.next = 5;
              return this.axiosService.axiosInstance.post(url, {
                intervalOption: intervalOption,
                requestType: requestType,
--
              url = window.$config.HostName + 'api/employee-statistics/get-bar-chart';
              _context3.next = 5;
              return this.axiosService.axiosInstance.post(url, {
                intervalOption: intervalOption,
                requestType: requestType,
--
              url = window.$config.HostName + 'api/employee-statistics/get-all-employees';
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/employee-statistics';
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/employee-statistics/export-employee-statistics';
              _context6.next = 3;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/payment-transaction/get-invoice-by-id?id=' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName).concat(baseURL, "?").concat(params.toString());
              _context.next = 7;
              return this.axiosService.axiosInstance.get(url);
            case 7:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/downloadinvoice' + '?invoiceIdentifier=' + transactionReference + '&isBalanceInvoice=' + isBalanceInvoice;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = 'api/login';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, {
                userName: userName,
                password: password,
--
              url = 'api/login/verfiyOTP';
              _context2.next = 6;
              return this.axiosService.axiosInstance.post(url, oTPVerificationData);
            case 6:
              result = _context2.sent;
--
              url = 'api/login/resendOTP';
              _context3.next = 6;
              return this.axiosService.axiosInstance.post(url, resendOtTPData);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/users/changepassword';
              _context4.next = 5;
              return this.axiosService.axiosInstance.put(url, {
                userId: id,
                oldPassword: currentPassword,
--
              url = window.$config.HostName + 'api/users/resendpasswordemail';
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url, {
                userName: userName,
                recapchaResponse: recapchaResponse
--
              url = window.$config.HostName + 'api/login';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, {
                userName: userName,
                password: password
--
              url = '/PayfortPayment/GetPayfortModel';
              _context.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/kpi-dashboard/chart-dashboard-boxes';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/get-tenants';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/chart-dashboard-status-boxes';
              _context4.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/all-requests-chart';
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/get-default-dates';
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/pending-approval-alerts';
              _context7.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/pending-activation-alerts';
              _context8.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context8.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/get-monitor-dashboard-options';
              _context9.next = 5;
              return this.axiosService.axiosInstance.post(url);
            case 5:
              result = _context9.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/pending-payment-alerts';
              _context0.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context0.sent;
--
              url = window.$config.HostName + 'api/payment-transaction';
              _context.next = 6;
              return this.axiosService.axiosInstance.post(url, ids);
            case 6:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/add-payment-transaction';
              _context2.next = 7;
              return this.axiosService.axiosInstance.post(url, model);
            case 7:
              result = _context2.sent;
--
              url = '/HyperPay/GetHyperPayModel?Amount=' + amount + '&IsMada=' + IsMada;
              _context3.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/citckeywords/getall';
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/providers/userProviderHasVatNumber';
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/providers/getProviderInvoiceInfo';
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/providers/editProviderInvoiceInfo';
              _context7.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/providers/getbalanceamount/' + id;
              _context8.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context8.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/set-credit-limit-transaction-as-paid';
              _context9.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context9.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/pay-request-email-creditlimit';
              _context0.next = 5;
              return this.axiosService.axiosInstance.put(url, ids);
            case 5:
              result = _context0.sent;
--
              url = window.$config.HostName + 'api/providers?Name=' + name + '&CrNumber=' + crNumber + '&AccreditationNumber=' + accreditationNumber + '&TaxNumber=' + taxNumber + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/providers';
              _context2.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: Id
--
              url = window.$config.HostName + 'api/providers';
              _context3.next = 6;
              return this.axiosService.axiosInstance.post(url, request);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/providers';
              _context4.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/providers/' + id;
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              }
              _context6.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/providers/send-otp';
              _context7.next = 6;
              return this.axiosService.axiosInstance.post(url, resendOtTPData);
            case 6:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/providers/switch-provider-short-code-link';
              _context8.next = 6;
              return this.axiosService.axiosInstance.post(url, oTPVerificationData);
            case 6:
              result = _context8.sent;
--
              url = window.$config.HostName + 'api/providerrequest?name=' + name + '&crNumber=' + crNumber + '&accreditationNumber=' + accreditationNumber + '&taxNumber=' + taxNumber + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/providerrequest/approve?id=' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/providerrequest/reject';
              _context3.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/providerrequest/' + id;
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/providers/finance-info?Name=' + name + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/providers/getfinanceinfobyid/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/providers/update-finance-info';
              _context3.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/payment-transaction/generate-qoyod-balance-invoice';
              _context4.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/public-payment-transaction';
              _context.next = 6;
              return this.axiosService.axiosInstance.post(url, ids);
            case 6:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/public-payment-transaction/get-payment-link-options';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/public-payment-transaction/updateinvoicedetails';
              _context3.next = 5;
              return this.axiosService.axiosInstance.put(url, invoiceDetailsModel);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/senders/pay';
              _context4.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context4.sent;
--
              url = '/HyperPay/GetHyperPayModel?Amount=' + amount + '&IsMada=' + IsMada;
              _context5.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/public-payment-transaction/add-payment-transaction';
              _context6.next = 7;
              return this.axiosService.axiosInstance.post(url, model);
            case 7:
              result = _context6.sent;
--
              url = this.controllerPath + '?rejectionText=' + rejectionText + '&requestType=' + requestType + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = this.controllerPath + '/getall';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = this.controllerPath + '/getById?id=${id}';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = this.controllerPath + '/isRejectionReasonExists?reasonText=' + reasonText + '&&requestType=' + requestType;
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = this.controllerPath;
              _context5.next = 6;
              return this.axiosService.axiosInstance.post(url, rejectionRessonModel);
            case 6:
              result = _context5.sent;
--
              url = this.controllerPath;
              _context6.next = 5;
              return this.axiosService.axiosInstance.put(url, rejectionRessonModel);
            case 5:
              result = _context6.sent;
--
              url = this.controllerPath;
              _context7.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + 'api/contract-requests/renew';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests/renew';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests/add-extend-duration-request';
              _context2.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + baseURL;
              _context.next = 4;
              return this.axiosService.axiosInstance.post(url, model);
            case 4:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/report/export-contract-requests-report';
              _context2.next = 3;
              return this.axiosService.axiosInstance.post(url, model);
            case 3:
              result = _context2.sent;
--
              url = "".concat(this.controllerPath, "?").concat(params.toString());
              _context.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context.sent;
--
              url = this.controllerPath + '/getall';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = this.controllerPath + '/getById?id=${id}';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = "".concat(this.controllerPath, "/isScamExists?").concat(params.toString());
              _context4.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context4.sent;
--
              url = "".concat(this.controllerPath, "/isScamExistsForEdit?").concat(params.toString());
              _context5.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context5.sent;
--
              url = this.controllerPath;
              _context6.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context6.sent;
--
              url = this.controllerPath;
              _context7.next = 5;
              return this.axiosService.axiosInstance.put(url, sacmModel);
            case 5:
              result = _context7.sent;
--
              url = this.controllerPath;
              _context8.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + 'api/complaint';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: model
              });
--
              url = window.$config.HostName + 'api/complaint/reactivate';
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context2.sent;
--
              };
              _context3.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context3.sent;
--
              };
              _context4.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/complaint/get-details/' + id;
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/complaint/edit-complaint';
              _context6.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/complaint/attachments/' + id;
              _context7.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context7.sent;
--
              url = "".concat(window.$config.HostName, "api/sender-conectivity-detail?senderId=").concat(encodeURIComponent(senderId));
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName, "api/senders/").concat(encodeURIComponent(senderId));
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail-log?connectivityDetailsId=' + id;
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/connectivity-details-configration';
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail';
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/mark-as-pending-addition?senderConectivityDetailId=' + id;
              _context6.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail';
              _context7.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  senderConectivityDetailId: senderConectivityDetailId
--
              url = "".concat(window.$config.HostName, "api/sender-conectivity-detail/sender-conectivity-details-by-info?").concat(params.toString());
              _context.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context.sent;
--
              url = "".concat(window.$config.HostName, "api/sender-conectivity-detail/export-sender-conectivity-support-report?").concat(params.toString());
              _context2.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/senders';
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = "".concat(window.$config.HostName, "api/senders/").concat(encodeURIComponent(senderId));
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail-log?connectivityDetailsId=' + id;
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/connectivity-details-configration';
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/mark-as-pending-addition?senderConectivityDetailId=' + id;
              _context7.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/mark-as-added?senderConectivityDetailId=' + id;
              _context8.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context8.sent;
--
              url = window.$config.HostName + 'api/sender-conectivity-detail/mark-as-deleted?senderConectivityDetailId=' + id;
              _context9.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context9.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/request-list-per-status';
              _context.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/kpi-dashboard/export-request-list';
              _context2.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-request-support-page/get-sender-support-details';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url, {
                params: {
                  senderName: senderName
--
              url = window.$config.HostName + 'api/sender-request-support-page/get-sender-request-approval-history?id=' + id + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-requests/edit-request';
              _context3.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-requests/add-history';
              _context4.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/sender-request-support-page/get-payment-transaction-by-requestid?id=' + id + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/sender-requests/delete-history';
              _context6.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + 'api/change-sender-type-requests/' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/change-sender-type-requests/';
              _context2.next = 5;
              return this.axiosService.axiosInstance.post(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-requests/' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests';
              _context2.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/senders';
              _context3.next = 5;
              return this.axiosService.axiosInstance.put(url, model);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-requests/' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests/attachments/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-request-approval-history/' + id;
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-extend-requests-history/' + id;
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/shortcodes?operatorId=' + operator + '&name=' + name + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/shortcodes/getall';
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + "api/shortcodes/getById?id=".concat(id);
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/shortcodes';
              _context4.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/shortcodes';
              _context5.next = 6;
              return this.axiosService.axiosInstance.put(url, model);
            case 6:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/shortcodes';
              _context6.next = 5;
              return this.axiosService.axiosInstance.delete(url, {
                params: {
                  id: id
--
              url = window.$config.HostName + 'api/short-code-bulk-activation/get-short-code-connectivity-history?operatorId=' + operator + '&name=' + name + '&connectivityStatus=' + connectivityStatus + '&requestStatus=' + requestStatus;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/shortcodes?operatorId=' + operator + '&name=' + name + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/short-code-bulk-activation/get-skipped-senders?bulkId=' + bulkId + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context3.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/shortcodes/getall';
              _context4.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context4.sent;
--
              url = window.$config.HostName + "api/short-code-bulk-activation/get-by-id?id=".concat(id);
              _context5.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/short-code-bulk-activation';
              _context6.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context6.sent;
--
              url = "".concat(window.$config.HostName, "api/payment-transaction/get-all-for-support?").concat(params.toString());
              _context.next = 6;
              return this.axiosService.axiosInstance.get(url);
            case 6:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/sender-requests/get-request-tracking-information?id=' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/update-request-data/' + id;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              }
              _context2.next = 6;
              return this.axiosService.axiosInstance.post(url, model);
            case 6:
              result = _context2.sent;
--
            case 9:
              _context2.next = 11;
              return this.axiosService.axiosInstance.put(url, model);
            case 11:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/users?username=' + username + '&email=' + email + '&mobileNumber=' + mobile + '&pageIndex=' + pageIndex + '&pageSize=' + pageSize;
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = window.$config.HostName + 'api/users/' + id;
              _context2.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context2.sent;
--
              url = window.$config.HostName + 'api/users';
              _context3.next = 6;
              return this.axiosService.axiosInstance.put(url, request);
            case 6:
              result = _context3.sent;
--
              url = window.$config.HostName + 'api/users';
              _context4.next = 6;
              return this.axiosService.axiosInstance.post(url, request);
            case 6:
              result = _context4.sent;
--
              url = window.$config.HostName + 'api/users/resend-password-admin?userName=' + userName;
              _context5.next = 5;
              return this.axiosService.axiosInstance.post(url);
            case 5:
              result = _context5.sent;
--
              url = window.$config.HostName + 'api/certificate/' + id;
              _context6.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context6.sent;
--
              }
              _context7.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context7.sent;
--
              url = window.$config.HostName + 'api/users/switch-admin-to-super?id=' + id;
              _context8.next = 5;
              return this.axiosService.axiosInstance.put(url);
            case 5:
              result = _context8.sent;
--
          axios = new _Services_AxiosService__WEBPACK_IMPORTED_MODULE_0__["default"]();
          _context.next = 3;
          return axios.axiosInstance.get('config.json?v=' + new Date().toISOString());
        case 3:
          result = _context.sent;
--
              url = window.$config.HostName + 'api/providers/is-provider-dissconected';
              _context.next = 5;
              return this.axiosService.axiosInstance.get(url);
            case 5:
              result = _context.sent;
--
              url = 'api/UserInfo';
              _context.next = 4;
              return this.axiosService.axiosInstance.get(url);
            case 4:
              result = _context.sent;
