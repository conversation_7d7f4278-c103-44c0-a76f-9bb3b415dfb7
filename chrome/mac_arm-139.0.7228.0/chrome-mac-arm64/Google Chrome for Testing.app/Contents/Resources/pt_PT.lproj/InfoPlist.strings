"Chromium Shortcut" = "Atalho do Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, Copyright 2025 Google LLC. Todos os direitos reservados.";
NSAudioCaptureUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSBluetoothAlwaysUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSBluetoothPeripheralUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSCameraUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Todos os direitos reservados.";
NSLocalNetworkUsageDescription = "Isto permite-lhe selecionar entre os dispositivos disponíveis e apresentar conteúdo nestes.";
NSLocationUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSMicrophoneUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Assim que o Chromium tiver acesso, os Websites poderão solicitar-lhe o acesso.";
