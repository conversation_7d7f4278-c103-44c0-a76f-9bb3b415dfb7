/* chrome version: 139.0.7228.0 */
Google_Chrome.pfm_title = "Google Chrome";
Google_Chrome.pfm_description = "Google Chrome preferences";
AIModeSettings.pfm_title = "Settings for Google's AI Mode integrations in the address bar and New Tab page search box.";
AIModeSettings.pfm_description = "0 - Allow AI Mode integrations.\n1 - Do not allow AI Mode integrations.\nThis policy controls Google's AI Mode integrations in the address bar and the New Tab page search box.\n\nTo access this feature, Google must be set as the user's default search engine.\n\n0/unset = The feature will be available to users.\n\n1 = The feature will not be available to users.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.";
AbusiveExperienceInterventionEnforce.pfm_title = "Abusive Experience Intervention Enforce";
AbusiveExperienceInterventionEnforce.pfm_description = "If SafeBrowsingEnabled is not Disabled, then setting AbusiveExperienceInterventionEnforce to Enabled or leaving it unset prevents sites with abusive experiences from opening new windows or tabs.\n\nSetting SafeBrowsingEnabled to Disabled or AbusiveExperienceInterventionEnforce to Disabled lets sites with abusive experiences open new windows or tabs.";
AccessCodeCastDeviceDuration.pfm_title = "Specifies how long (in seconds) a cast device selected with an access code or QR code stays in the Google Cast menu's list of cast devices.";
AccessCodeCastDeviceDuration.pfm_description = "This policy specifies how long (in seconds) a cast device that was previously selected via an access code or QR code can be seen within the Google Cast menu of cast devices.\nThe lifetime of an entry starts at the time the access code was first entered or the QR code was first scanned.\nDuring this period the cast device will appear in the Google Cast menu's list of cast devices.\nAfter this period, in order to use the cast device again the access code must be reentered or the QR code must be rescanned.\nBy default, the period is zero seconds, so cast devices will not stay in the Google Cast menu, and so the access code must be reentered, or the QR code rescanned, in order to initiate a new casting session.\nNote that this policy only affects how long a cast devices appears in the Google Cast menu, and has no effect on any ongoing cast session which will continue even if the period expires.\nThis policy has no effect unless the AccessCodeCastEnabled policy is Enabled.";
AccessCodeCastEnabled.pfm_title = "Allow users to select cast devices with an access code or QR code from within the Google Cast menu.";
AccessCodeCastEnabled.pfm_description = "This policy controls whether a user will be presented with an option, within the Google Cast menu which allows them to cast to cast devices that do not appear in the Google Cast menu, using either the access code or QR code displayed on the cast devices's screen.\nBy default, a user must reenter the access code or rescan the QR code in order to initiate a subsequent casting session, but if the AccessCodeCastDeviceDuration policy has been set to a non-zero value (the default is zero), then the cast device will remain in the list of available cast devices until the specified period of time has expired.\nWhen this policy is set to Enabled, users will be presented with the option to select cast devices by using an access code or by scanning a QR code.\nWhen this policy is set to Disabled or not set, users will not be given the option to select cast devices by using an access code or by scanning a QR code.";
AccessControlAllowMethodsInCORSPreflightSpecConformant.pfm_title = "Make Access-Control-Allow-Methods matching in CORS preflight spec conformant";
AccessControlAllowMethodsInCORSPreflightSpecConformant.pfm_description = "This policy controls whether request methods are uppercased when matching with Access-Control-Allow-Methods response headers in CORS preflight.\n\nIf the policy is Disabled, request methods are uppercased.\nThis is the behavior on or before Google Chrome 108.\n\nIf the policy is Enabled or not set, request methods are not uppercased, unless matching case-insensitively with DELETE, GET, HEAD, OPTIONS, POST, or PUT.\nThis would reject fetch(url, {method: 'Foo'}) + \"Access-Control-Allow-Methods: FOO\" response header,\nand would accept fetch(url, {method: 'Foo'}) + \"Access-Control-Allow-Methods: Foo\" response header.\n\nNote: request methods \"post\" and \"put\" are not affected, while \"patch\" is affected.\n\nThis policy is intended to be temporary and will be removed in the future.";
AccessibilityImageLabelsEnabled.pfm_title = "Enable Get Image Descriptions from Google.";
AccessibilityImageLabelsEnabled.pfm_description = "The Get Image Descriptions from Google\naccessibility feature enables visually-impaired screen reader users to\nget descriptions of unlabeled images on the web. Users who choose to enable it\nwill have the option of using an anonymous Google service to provide\nautomatic descriptions for unlabeled images they encounter on the web.\n\nIf this feature is enabled, the content of images will be sent to Google\nservers in order to generate a description. No cookies or other user\ndata is sent, and Google does not save or log any image content.\n\nIf this policy is set to Enabled, the\nGet Image Descriptions from Google\nfeature will be enabled, though it will only affect users who are using a\nscreen reader or other similar assistive technology.\n\nIf this policy is set to Disabled, users will not have the option of enabling\nthe feature.\n\nIf this policy is not set, user can choose to use this feature or not.\n";
AdHocCodeSigningForPWAsEnabled.pfm_title = "Native application signing during Progressive Web Application installation";
AdHocCodeSigningForPWAsEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset enables the use of ad-hoc signatures for the native application that is created when installing a Progressive Web Application (PWA). This ensures that each installed application has a unique identity to macOS system components.\n\nSetting the policy to Disabled will result in every native application created when installing Progressive Web Applications having the same identity. This can interfere with macOS functionality.\n\nOnly turn off the policy if you are using an endpoint security solution that blocks applications with an ad-hoc signature.";
AdditionalDnsQueryTypesEnabled.pfm_title = "Allow DNS queries for additional DNS record types";
AdditionalDnsQueryTypesEnabled.pfm_description = "This policy controls whether Google Chrome may query additional DNS record types when making insecure DNS requests. This policy has no effect on DNS queries made via Secure DNS, which may always query additional DNS types.\n\nIf this policy is unset or set to Enabled, additional types such as HTTPS (DNS type 65) may be queried in addition to A (DNS type 1) and AAAA (DNS type 28).\n\nIf this policy is set to Disabled, DNS will only be queried for A (DNS type 1) and/or AAAA (DNS type 28).\n\nThis policy is a temporary measure and will be removed in future versions of Google Chrome. After removal of the policy, Google Chrome will always be able to query additional DNS types.";
AdsSettingForIntrusiveAdsSites.pfm_title = "Ads setting for sites with intrusive ads";
AdsSettingForIntrusiveAdsSites.pfm_description = "1 - Allow ads on all sites\n2 - Do not allow ads on sites with intrusive ads\nUnless SafeBrowsingEnabled is set to False, then setting AdsSettingForIntrusiveAdsSites to 1 or leaving it unset allows ads on all sites.\n\nSetting the policy to 2 blocks ads on sites with intrusive ads.";
AdvancedProtectionAllowed.pfm_title = "Enable additional protections for users enrolled in the Advanced Protection program";
AdvancedProtectionAllowed.pfm_description = "This policy controls whether users enrolled in the Advanced Protection program receive extra protections. Some of these features may involve the sharing of data with Google (for example, Advanced Protection users will be able to send their downloads to Google for malware scanning). If set to True or not set, enrolled users will receive extra protections. If set to False, Advanced Protection users will receive only the standard consumer features.";
AllHttpAuthSchemesAllowedForOrigins.pfm_title = "List of origins allowing all HTTP authentication";
AllHttpAuthSchemesAllowedForOrigins.pfm_description = "Setting the policy specifies for which origins to allow all the HTTP authentication schemes Google Chrome supports regardless of the AuthSchemes policy.\n\nFormat the origin pattern according to this format (https://support.google.com/chrome/a?p=url_blocklist_filter_format). Up to 1,000 exceptions can be defined in AllHttpAuthSchemesAllowedForOrigins.\nWildcards are allowed for the whole origin or parts of the origin, either the scheme, host, port.";
AllowBackForwardCacheForCacheControlNoStorePageEnabled.pfm_title = "Allow pages with Cache-Control: no-store header to enter back/forward cache";
AllowBackForwardCacheForCacheControlNoStorePageEnabled.pfm_description = "This policy controls if a page with Cache-Control: no-store header can be stored in back/forward cache. The website setting this header may not expect the page to be restored from back/forward cache since some sensitive information could still be displayed after the restoration even if it is no longer accessible.\n\nIf the policy is enabled or unset, the page with Cache-Control: no-store header might be restored from back/forward cache unless the cache eviction is triggered (e.g. when there is HTTP-only cookie change to the site).\n\nIf the policy is disabled, the page with Cache-Control: no-store header will not be stored in back/forward cache.";
AllowCrossOriginAuthPrompt.pfm_title = "Cross-origin HTTP Authentication prompts";
AllowCrossOriginAuthPrompt.pfm_description = "Setting the policy to Enabled allows third-party images on a page to show an authentication prompt.\n\nSetting the policy to Disabled or leaving it unset renders third-party images unable to show an authentication prompt.\n\nTypically, this policy is Disabled as a phishing defense.";
AllowDeletingBrowserHistory.pfm_title = "Enable deleting browser and download history";
AllowDeletingBrowserHistory.pfm_description = "Setting the policy to Enabled or leaving it unset means browser history and download history can be deleted in Chrome, and users can't change this setting.\n\nSetting the policy to Disabled means browser history and download history can't be deleted. Even with this policy off, the browsing and download history are not guaranteed to be retained. Users may be able to edit or delete the history database files directly, and the browser itself may expire or archive any or all history items at any time.";
AllowDinosaurEasterEgg.pfm_title = "Allow Dinosaur Easter Egg Game";
AllowDinosaurEasterEgg.pfm_description = "Setting the policy to True allows users to play the dinosaur game. Setting the policy to False means users can't play the dinosaur easter egg game when device is offline.\n\nLeaving the policy unset means users can't play the game on enrolled Google ChromeOS, but can under other circumstances.";
AllowFileSelectionDialogs.pfm_title = "Allow invocation of file selection dialogs";
AllowFileSelectionDialogs.pfm_description = "Setting the policy to Enabled or leaving it unset means Chrome can display, and users can open, file selection dialogs.\n\nSetting the policy to Disabled means that whenever users perform actions provoking a file selection dialog, such as importing bookmarks, uploading files, and saving links, a message appears instead. The user is assumed to have clicked Cancel on the file selection dialog.";
AllowWebAuthnWithBrokenTlsCerts.pfm_title = "Allow Web Authentication requests on sites with broken TLS certificates.";
AllowWebAuthnWithBrokenTlsCerts.pfm_description = "If set to Enabled, Google Chrome will\nallow Web Authentication requests on websites that have TLS certificates with\nerrors (i.e. websites considered not secure).\n\nIf the policy is set to Disabled or left unset, the default behavior of\nblocking such requests will apply.";
AllowedDomainsForApps.pfm_title = "Define domains allowed to access Google Workspace";
AllowedDomainsForApps.pfm_description = "Setting the policy turns on Chrome's restricted sign-in feature in Google Workspace and prevents users from changing this setting. Users can only access Google tools using accounts from the specified domains (to allow gmail or googlemail accounts, add consumer_accounts to the list of domains). This setting prevents users from signing in and adding a Secondary Account on a managed device that requires Google authentication, if that account doesn't belong to one of the explicitly allowed domains.\n\nLeaving this setting empty or unset means users can access Google Workspace with any account.\n\nUsers cannot change or override this setting.\n\nNote: This policy causes the X-GoogApps-Allowed-Domains header to be appended to all HTTP and HTTPS requests to all google.com domains, as described in https://support.google.com/a/answer/1668854.";
AlternateErrorPagesEnabled.pfm_title = "Enable alternate error pages";
AlternateErrorPagesEnabled.pfm_description = "Setting the policy to True means Google Chrome uses alternate error pages built into (such as \"page not found\"). Setting the policy to False means Google Chrome never uses alternate error pages.\n\nIf you set the policy, users can't change it. If not set, the policy is on, but users can change this setting.";
AlternativeBrowserParameters.pfm_title = "Command-line parameters for the alternative browser.";
AlternativeBrowserParameters.pfm_description = "Setting the policy to a list of strings means each string is passed to the alternative browser as separate command-line parameters. On Microsoft® Windows®, the parameters are joined with spaces. On macOS and Linux®, a parameter can have spaces and still be treated as a single parameter.\n\nIf a parameter contains ${url}, ${url} is replaced with the URL of the page to open. If no parameter contains ${url}, the URL is appended at the end of the command line.\n\nEnvironment variables are expanded. On Microsoft® Windows®, %ABC% is replaced with the value of the ABC environment variable. On macOS and Linux®, ${ABC} is replaced with the value of the ABC environment variable.\n\nLeaving the policy unset means only the URL is passed as a command-line parameter.";
AlternativeBrowserPath.pfm_title = "Alternative browser to launch for configured websites.";
AlternativeBrowserPath.pfm_description = "Setting the policy controls which command to use to open URLs in an alternative browser. The policy can be set to one of ${ie}, ${firefox}, ${safari}, ${opera}, ${edge} or a file path. When this policy is set to a file path, that file is used as an executable file. ${ie} is only available on Microsoft® Windows®. ${safari} and ${edge} are only available on Microsoft® Windows® and macOS.\n\nLeaving the policy unset puts a platform-specific default in use: Internet Explorer® for Microsoft® Windows®, or Safari® for macOS. On Linux®, launching an alternative browser will fail.";
AlwaysOpenPdfExternally.pfm_title = "Always Open PDF files externally";
AlwaysOpenPdfExternally.pfm_description = "Setting the policy to Enabled turns the internal PDF viewer off in Google Chrome, treats PDF files as a download, and lets users open PDFs with the default application.\n\nSetting the policy to Disabled means that unless users turns off the PDF plugin, it will open PDF files.\n\nIf you set the policy, users can't change it in Google Chrome. If not set, users can choose whether to open PDF externally or not.";
AmbientAuthenticationInPrivateModesEnabled.pfm_title = "Enable Ambient Authentication for profile types.";
AmbientAuthenticationInPrivateModesEnabled.pfm_description = "0 - Enable ambient authentication in regular sessions only.\n1 - Enable ambient authentication in incognito and regular sessions.\n2 - Enable ambient authentication in guest and regular sessions.\n3 - Enable ambient authentication in regular, incognito and guest sessions.\nConfiguring this policy will allow/disallow ambient authentication for Incognito and Guest profiles in Google Chrome.\n\nAmbient Authentication is http authentication with default credentials if explicit credentials are not provided via NTLM/Kerberos/Negotiate challenge/response schemes.\n\nSetting the RegularOnly (value 0), allows ambient authentication for Regular sessions only. Incognito and Guest sessions wouldn't be allowed to ambiently authenticate.\n\nSetting the IncognitoAndRegular (value 1), allows ambient authentication for Incognito and Regular sessions. Guest sessions wouldn't be allowed to ambiently authenticate.\n\nSetting the GuestAndRegular (value 2), allows ambient authentication for Guest and Regular sessions. Incognito sessions wouldn't be allowed to ambiently authenticate.\n\nSetting the  All (value 3), allows ambient authentication for all sessions.\n\nNote that, ambient authentication is always allowed on regular profiles.\n\nIn Google Chrome version 81 and later, if the policy is left not set, ambient authentication will be enabled in regular sessions only.";
AudioCaptureAllowed.pfm_title = "Allow or deny audio capture";
AudioCaptureAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset means that, with the exception of URLs set in the AudioCaptureAllowedUrls list, users get prompted for audio capture access.\n\nSetting the policy to Disabled turns off prompts, and audio capture is only available to URLs set in the AudioCaptureAllowedUrls list.\n\nNote: The policy affects all audio input (not just the built-in microphone).";
AudioCaptureAllowedUrls.pfm_title = "URLs that will be granted access to audio capture devices without prompt";
AudioCaptureAllowedUrls.pfm_description = "Setting the policy means you specify the URL list whose patterns get matched to the security origin of the requesting URL. A match grants access to audio capture devices without prompt\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Note, however, that the pattern \"*\", which matches any URL, is not supported by this policy.";
AudioSandboxEnabled.pfm_title = "Allow the audio sandbox to run";
AudioSandboxEnabled.pfm_description = "This policy controls the audio process sandbox.\nIf this policy is enabled, the audio process will run sandboxed.\nIf this policy is disabled, the audio process will run unsandboxed and the WebRTC audio-processing module will run in the renderer process.\nThis leaves users open to security risks related to running the audio subsystem unsandboxed.\nIf this policy is not set, the default configuration for the audio sandbox will be used, which may differ per platform.\nThis policy is intended to give enterprises flexibility to disable the audio sandbox if they use security software setups that interfere with the sandbox.";
AuthNegotiateDelegateAllowlist.pfm_title = "Kerberos delegation server allowlist";
AuthNegotiateDelegateAllowlist.pfm_description = "Setting the policy assigns servers that Google Chrome may delegate to. Separate multiple server names with commas. Wildcards, *, are allowed.\n\nLeaving the policy unset means Google Chrome won't delegate user credentials, even if a server is detected as intranet.";
AuthNegotiateDelegateByKdcPolicy.pfm_title = "Use KDC policy to delegate credentials.";
AuthNegotiateDelegateByKdcPolicy.pfm_description = "Setting the policy to Enabled means HTTP authentication respects approval by KDC policy. In other words, Google Chrome delegates user credentials to the service being accessed if the KDC sets OK-AS-DELEGATE on the service ticket. See RFC 5896 ( https://tools.ietf.org/html/rfc5896.html ). The service should also be allowed by AuthNegotiateDelegateAllowlist.\n\nSetting the policy to Disabled or leaving it unset means KDC policy is ignored on supported platforms and only AuthNegotiateDelegateAllowlist is respected.\n\nOn Microsoft® Windows®, KDC policy is always respected.";
AuthSchemes.pfm_title = "Supported authentication schemes";
AuthSchemes.pfm_description = "Setting the policy specifies which HTTP authentication schemes Google Chrome supports.\n\nLeaving the policy unset employs all 4 schemes.\n\nValid values:\n\n* basic\n\n* digest\n\n* ntlm\n\n* negotiate\n\nNote: Separate multiple values with commas.";
AuthServerAllowlist.pfm_title = "Authentication server allowlist";
AuthServerAllowlist.pfm_description = "Setting the policy specifies which servers should be allowed for integrated authentication. Integrated authentication is only on when Google Chrome gets an authentication challenge from a proxy or from a server in this permitted list.\n\nLeaving the policy unset means Google Chrome tries to detect if a server is on the intranet. Only then will it respond to IWA requests. If a server is detected as internet, then Google Chrome ignores IWA requests from it.\n\nNote: Separate multiple server names with commas. Wildcards, *, are allowed.";
AutoLaunchProtocolsFromOrigins.pfm_title = "Define a list of protocols that can launch an external application from listed origins without prompting the user";
AutoLaunchProtocolsFromOrigins.pfm_description = "Allows you to set a list of protocols, and for each protocol an associated list of allowed origin patterns, that can launch an external application without prompting the user. The trailing separator should not be included when listing the protocol, so list \"skype\" instead of \"skype:\" or \"skype://\".\n\nIf this policy is set, a protocol will only be permitted to launch an external application without prompting by policy if the protocol is listed, and the origin of the site trying to launch the protocol matches one of the origin patterns in that protocol's allowed_origins list. If either condition is false the external protocol launch prompt will not be omitted by policy.\n\nIf this policy is not set, no protocols can launch without a prompt by default. Users may opt out of prompts on a per-protocol/per-site basis unless the ExternalProtocolDialogShowAlwaysOpenCheckbox policy is set to Disabled. This policy has no impact on per-protocol/per-site prompt exemptions set by users.\n\nThe origin matching patterns use a similar format to those for the 'URLBlocklist' policy, which are documented at https://support.google.com/chrome/a?p=url_blocklist_filter_format.\n\nHowever, origin matching patterns for this policy cannot contain \"/path\" or \"@query\" elements. Any pattern that does contain a \"/path\" or \"@query\" element will be ignored.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=AutoLaunchProtocolsFromOrigins for more information about schema and formatting.";
AutoOpenAllowedForURLs.pfm_title = "URLs where AutoOpenFileTypes can apply";
AutoOpenAllowedForURLs.pfm_description = "List of URLs specifying which urls AutoOpenFileTypes will apply to. This policy has no impact on automatically open values set by users.\n\nIf this policy is set, files will only automatically open by policy if the url is part of this set and the file type is listed in AutoOpenFileTypes. If either condition is false the download won't automatically open by policy.\n\nIf this policy isn't set, all downloads where the file type is in AutoOpenFileTypes will automatically open.\n\nA URL pattern has to be formatted according to https://support.google.com/chrome/a?p=url_blocklist_filter_format.";
AutoOpenFileTypes.pfm_title = "List of file types that should be automatically opened on download";
AutoOpenFileTypes.pfm_description = "List of file types that should be automatically opened on download. The leading separator should not be included when listing the file type, so list \"txt\" instead of \".txt\".\n\nFiles with types that should be automatically opened will still be subject to the enabled safe browsing checks and won't be opened if they fail those checks.\n\nIf this policy isn't set, only file types that a user has already specified to automatically be opened will do so when downloaded.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.";
AutoSelectCertificateForUrls.pfm_title = "Automatically select client certificates for these sites";
AutoSelectCertificateForUrls.pfm_description = "Setting the policy lets you make a list of URL patterns that specify sites for which Chrome can automatically select a client certificate. The value is an array of stringified JSON dictionaries, each with the form { \"pattern\": \"$URL_PATTERN\", \"filter\" : $FILTER }, where $URL_PATTERN is a content setting pattern. $FILTER restricts the client certificates the browser automatically selects from. Independent of the filter, only certificates that match the server's certificate request are selected.\n\nExamples for the usage of the $FILTER section:\n\n* When $FILTER is set to { \"ISSUER\": { \"CN\": \"$ISSUER_CN\" } }, only client certificates issued by a certificate with the CommonName $ISSUER_CN are selected.\n\n* When $FILTER contains both the \"ISSUER\" and the \"SUBJECT\" sections, only client certificates that satisfy both conditions are selected.\n\n* When $FILTER contains a \"SUBJECT\" section with the \"O\" value, a certificate needs at least one organization matching the specified value to be selected.\n\n* When $FILTER contains a \"SUBJECT\" section with a \"OU\" value, a certificate needs at least one organizational unit matching the specified value to be selected.\n\n* When $FILTER is set to {}, the selection of client certificates is not additionally restricted. Note that filters provided by the web server still apply.\n\nLeaving the policy unset means there's no autoselection for any site.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=AutoSelectCertificateForUrls for more information about schema and formatting.";
AutofillAddressEnabled.pfm_title = "Enable AutoFill for addresses";
AutofillAddressEnabled.pfm_description = "Setting the policy to True or leaving it unset gives users control of Autofill for addresses in the UI.\n\nSetting the policy to False means Autofill never suggests or fills address information, nor does it save additional address information that users submit while browsing the web.";
AutofillCreditCardEnabled.pfm_title = "Enable AutoFill for credit cards";
AutofillCreditCardEnabled.pfm_description = "Setting the policy to True or leaving it unset means users can control autofill suggestions for credit cards in the UI.\n\nSetting the policy to False means autofill never suggests or fills credit card information, nor will it save additional credit card information that users might submit while browsing the web.";
AutofillPredictionSettings.pfm_title = "Settings for Autofill with AI";
AutofillPredictionSettings.pfm_description = "0 - Allow autofill with AI and improve AI models.\n1 - Allow autofill with AI without improving AI models.\n2 - Do not allow autofill with AI.\nSpecifies whether users can let Google Chrome use Generative AI to better understand forms and help them fill more fields.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
AutomaticFullscreenAllowedForUrls.pfm_title = "Allow automatic fullscreen on these sites";
AutomaticFullscreenAllowedForUrls.pfm_description = "For security reasons, the\nrequestFullscreen() web API\nrequires a prior user gesture (\"transient activation\") to be called or will\notherwise fail. Users' personal settings may allow certain origins to call\nthis API without a prior user gesture, as described in\nhttps://chromestatus.com/feature/****************.\n\nThis policy supersedes users' personal settings and allows matching origins to\ncall the API without a prior user gesture.\n\nFor detailed information on valid url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.\nWildcards, *, are allowed.\n\nOrigins matching both blocked and allowed policy patterns will be blocked.\nOrigins not specified by policy nor user settings will require a prior user\ngesture to call this API.";
AutomaticFullscreenBlockedForUrls.pfm_title = "Block automatic fullscreen on these sites";
AutomaticFullscreenBlockedForUrls.pfm_description = "For security reasons, the\nrequestFullscreen() web API\nrequires a prior user gesture (\"transient activation\") to be called or will\notherwise fail. Users' personal settings may allow certain origins to call\nthis API without a prior user gesture, as described in\nhttps://chromestatus.com/feature/****************.\n\nThis policy supersedes users' personal settings and blocks matching origins\nfrom calling the API without a prior user gesture.\n\nFor detailed information on valid url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.\nWildcards, *, are allowed.\n\nOrigins matching both blocked and allowed policy patterns will be blocked.\nOrigins not specified by policy nor user settings will require a prior user\ngesture to call this API.";
AutoplayAllowed.pfm_title = "Allow media autoplay";
AutoplayAllowed.pfm_description = "Setting the policy to True lets Google Chrome autoplay media. Setting the policy to False stops Google Chrome from autoplaying media.\n\n By default, Google Chrome doesn't autoplay media. But, for certain URL patterns, you can use the AutoplayAllowlist policy to change this setting.\n\nIf this policy changes while Google Chrome is running, it only applies to newly opened tabs.";
AutoplayAllowlist.pfm_title = "Allow media autoplay on a allowlist of URL patterns";
AutoplayAllowlist.pfm_description = "Setting the policy lets videos play automatically (without user consent) with audio content in Google Chrome. If AutoplayAllowed policy is set to True, then this policy has no effect. If AutoplayAllowed is set to False, then any URL patterns set in this policy can still play. If this policy changes while Google Chrome is running, it only applies to newly opened tabs.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.";
BasicAuthOverHttpEnabled.pfm_title = "Allow Basic authentication for HTTP";
BasicAuthOverHttpEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset will allow Basic authentication challenges received over non-secure HTTP.\n\nSetting the policy to Disabled forbids non-secure HTTP requests from using the Basic authentication scheme; only secure HTTPS is allowed.\n\nThis policy setting is ignored (and Basic is always forbidden) if the AuthSchemes policy is set and does not include Basic.";
BatterySaverModeAvailability.pfm_title = "Enable Battery Saver Mode";
BatterySaverModeAvailability.pfm_description = "0 - Battery Saver Mode will be disabled.\n1 - Battery Saver Mode will be enabled when the device is on battery power and battery level is low.\n2 - This value is deprecated as of M121. In M121 and after, values will be treated as EnabledBelowThreshold.\nThis policy enables or disables the Battery Saver Mode setting.\nOn Chrome, this setting makes it so that frame rate is throttled to lower power consumption. If this policy is unset, the end user can control this setting in chrome://settings/performance.\nOn ChromeOS, this setting makes it so that frame rate and CPU frequency are throttled, backlights are dimmed, and Android is put in Battery Saver Mode. On devices with multiple CPUs, some CPUs will be turned off.\nThe different levels are:\nDisabled (0): Battery Saver Mode will be disabled.\nEnabledBelowThreshold (1): Battery Saver Mode will be enabled when the device is on battery power and battery level is low.\nEnabledOnBattery (2): This value is deprecated as of M121. From M121 onwards, values will be      treated as EnabledBelowThreshold.\n";
BlockExternalExtensions.pfm_title = "Blocks external extensions from being installed";
BlockExternalExtensions.pfm_description = "Controls external extensions installation.\n\nSetting this policy to Enabled blocks external extensions from being installed.\n\nSetting this policy to Disabled or leaving it unset allows external extensions to be installed.\n\nExternal extensions and their installation are documented at https://developer.chrome.com/docs/extensions/how-to/distribute/install-extensions.";
BlockThirdPartyCookies.pfm_title = "Block third party cookies";
BlockThirdPartyCookies.pfm_description = "Setting the policy to Enabled prevents webpage elements that aren't from the domain that's in the browser's address bar from setting cookies. Setting the policy to Disabled lets those elements set cookies and prevents users from changing this setting.\n\nLeaving it unset turns third-party cookies on, but users can change this setting.";
BookmarkBarEnabled.pfm_title = "Enable Bookmark Bar";
BookmarkBarEnabled.pfm_description = "Setting the policy to True displays a bookmark bar in Google Chrome. Setting the policy to False means users never see the bookmark bar.\n\nIf you set the policy, users can't change it. If not set, users decide whether to use this function.";
BrowserAddPersonEnabled.pfm_title = "Enable add person in user manager";
BrowserAddPersonEnabled.pfm_description = "If this policy is set to true or not configured, Google Chrome and Lacros will allow to add a new person from the user manager.\n\nIf this policy is set to false, Google Chrome and Lacros will not allow adding a new person from the user manager.";
BrowserGuestModeEnabled.pfm_title = "Enable guest mode in browser";
BrowserGuestModeEnabled.pfm_description = "If this policy is set to Enabled or not configured, Google Chrome will enable guest logins. Guest logins are Google Chrome profiles where all windows are in incognito mode.\n\nIf this policy is set to Disabled, Google Chrome will not allow guest profiles to be started.";
BrowserGuestModeEnforced.pfm_title = "Enforce browser guest mode";
BrowserGuestModeEnforced.pfm_description = "Setting the policy to Enabled means Google Chrome enforces guest sessions and prevents profile sign-ins. Guest sign-ins are Google Chrome profiles where windows are in Incognito mode.\n\nSetting the policy to Disabled, leaving it unset, or disabling browser Guest mode (through BrowserGuestModeEnabled) allows the use of new and existing profiles.";
BrowserLabsEnabled.pfm_title = "Browser experiments icon in toolbar";
BrowserLabsEnabled.pfm_description = "Setting the policy to Enabled or leaving the policy unset means that users can access browser experimental features through an icon in the toolbar\n\nSetting the policy to Disabled removes the browser experimental features icon from the toolbar.\n\nchrome://flags and any other means of turning off and on browser features will still behave as expected regardless of whether this policy is Enabled or Disabled.";
BrowserNetworkTimeQueriesEnabled.pfm_title = "Allow queries to a Google time service";
BrowserNetworkTimeQueriesEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset means Google Chrome send occasional queries to a Google server to retrieve an accurate timestamp.\n\nSetting the policy to Disabled stops Google Chrome from sending these queries.";
BrowserSignin.pfm_title = "Browser sign in settings";
BrowserSignin.pfm_description = "0 - Disable browser sign-in\n1 - Enable browser sign-in\n2 - Force users to sign-in to use the browser\nThis policy controls the sign-in behavior of the browser. It allows you to specify if the user can sign in to Google Chrome with their account and use account related services like Google Chrome Sync.\n\nIf the policy is set to \"Disable browser sign-in\" then the user cannot sign in to the browser and use account-based services. In this case browser-level features like Google Chrome Sync cannot be used and will be unavailable. On iOS, if the user was signed in and the policy is set to \"Disabled\" they will be signed out immediately. On other platforms, they will be signed out the next time they run Google Chrome. On all platforms, their local profile data like bookmarks, passwords etc. will be preserved and still usable. The user will still be able to sign into and use Google web services like Gmail.\n\nIf the policy is set to \"Enable browser sign-in,\" then the user is allowed to sign in to the browser. On all platforms except iOS, the user is automatically signed in to the browser when signed in to Google web services like Gmail. Being signed in to the browser means the user's account information will be kept by the browser. However, it does not mean that Google Chrome Sync will be turned on by default; the user must separately opt-in to use this feature. Enabling this policy will prevent the user from turning off the setting that allows browser sign-in. To control the availability of Google Chrome Sync, use the SyncDisabled policy.\n\nIf the policy is set to \"Force browser sign-in\" the user is presented with an account selection dialog and has to choose and sign in to an account to use the browser. This ensures that for managed accounts the policies associated with the account are applied and enforced. The default value of BrowserGuestModeEnabled will be set to disabled. Note that existing unsigned profiles will be locked and inaccessible after enabling this policy. For more information, see help center article: https://support.google.com/chrome/a/answer/7572556 . This option is not supported on Linux nor Android, where it will fall back to \"Enable browser sign-in\" if used.\n\nIf this policy is not set then the user can decide if they want to enable browser sign-in in the Google Chrome settings and use it as they see fit.";
BrowserSwitcherDelay.pfm_title = "Delay before launching alternative browser (milliseconds)";
BrowserSwitcherDelay.pfm_description = "Setting the policy to a number has Google Chrome show a message for that number of milliseconds, then it opens an alternative browser.\n\nLeaving the policy unset or set to 0 means navigating to a designated URL immediately opens it in an alternative browser.";
BrowserSwitcherEnabled.pfm_title = "Enable the Legacy Browser Support feature.";
BrowserSwitcherEnabled.pfm_description = "Setting the policy to Enabled means Google Chrome will try to launch some URLs in an alternate browser, such as Internet Explorer®. This feature is set using the policies in the Legacy Browser support group.\n\nSetting the policy to Disabled or leaving it unset means Google Chrome won't try to launch designated URLs in an alternate browser.";
BrowserSwitcherExternalGreylistUrl.pfm_title = "URL of an XML file that contains URLs that should never trigger a browser switch.";
BrowserSwitcherExternalGreylistUrl.pfm_description = "Setting the policy to a valid URL has Google Chrome download the site list from that URL and apply the rules as if they were set up with the BrowserSwitcherUrlGreylist policy. These policies prevent Google Chrome and the alternative browser from opening one another.\n\nLeaving it unset (or set to a invalid URL) means Google Chrome doesn't use the policy as a source of rules for not switching browsers.\n\nNote: This policy points to an XML file in the same format as Internet Explorer®'s SiteList policy. This loads rules from an XML file, without sharing those rules with Internet Explorer®. Read more on Internet Explorer®'s SiteList policy ( https://docs.microsoft.com/internet-explorer/ie11-deploy-guide/what-is-enterprise-mode )";
BrowserSwitcherExternalSitelistUrl.pfm_title = "URL of an XML file that contains URLs to load in an alternative browser.";
BrowserSwitcherExternalSitelistUrl.pfm_description = "Setting the policy to a valid URL has Google Chrome download the site list from that URL and apply the rules as if they were set up with the BrowserSwitcherUrlList policy.\n\nLeaving it unset (or set to a invalid URL) means Google Chrome doesn't use the policy as a source of rules for switching browsers.\n\nNote: This policy points to an XML file in the same format as Internet Explorer®'s SiteList policy. This loads rules from an XML file, without sharing those rules with Internet Explorer®. Read more on Internet Explorer®'s SiteList policy ( https://docs.microsoft.com/internet-explorer/ie11-deploy-guide/what-is-enterprise-mode)";
BrowserSwitcherKeepLastChromeTab.pfm_title = "Keep last tab open in Chrome.";
BrowserSwitcherKeepLastChromeTab.pfm_description = "Setting the policy to Enabled or leaving it unset has Google Chrome keep at least one tab open, after switching to an alternate browser.\n\nSetting the policy to Disabled has Google Chrome close the tab after switching to an alternate browser, even if it was the last tab. This causes Google Chrome to exit completely.";
BrowserSwitcherParsingMode.pfm_title = "Sitelist parsing mode";
BrowserSwitcherParsingMode.pfm_description = "0 - Default behavior for LBS.\n1 - More compatible with Microsoft IE/Edge enterprise mode sitelists.\nThis policy controls how Google Chrome interprets sitelist/greylist policies for the Legacy Browser Support feature. It affects the following policies: BrowserSwitcherUrlList, BrowserSwitcherUrlGreylist, BrowserSwitcherUseIeSitelist, BrowserSwitcherExternalSitelistUrl, and BrowserSwitcherExternalGreylistUrl.\n\nIf 'Default' (0) or unset, URL matching is less strict. Rules that do not contain \"/\" look for a substring anywhere in the URL's hostname. Matching the path component of a URL is case-sensitive.\n\nIf 'IESiteListMode' (1), URL matching is more strict. Rules that do not contain \"/\" only match at the end of the hostname. They must also be at a domain name boundary. Matching the path component of a URL is case-insensitive. This is more compatible with Microsoft® Internet Explorer® and Microsoft® Edge®.\n\nFor example, with the rules \"example.com\" and \"acme.com/abc\":\n\n\"http://example.com/\", \"http://subdomain.example.com/\" and \"http://acme.com/abc\" match regardless of parsing mode.\n\n\"http://notexample.com/\", \"http://example.com.invalid.com/\", \"http://example.comabc/\" only match in 'Default' mode.\n\n\"http://acme.com/ABC\" only matches in 'IESiteListMode'.";
BrowserSwitcherUrlGreylist.pfm_title = "Websites that should never trigger a browser switch.";
BrowserSwitcherUrlGreylist.pfm_description = "Setting the policy controls the list of websites that will never cause a browser switch. Each item is treated as a rule. Those rules that match won't open an alternative browser. Unlike the BrowserSwitcherUrlList policy, rules apply to both directions. When the Internet Explorer® add-in is on, it also controls whether Internet Explorer® should open these URLs in Google Chrome.\n\nLeaving the policy unset adds no websites to the list.\n\nNote: Elements can also be added to this list through the BrowserSwitcherExternalGreylistUrl policy.";
BrowserSwitcherUrlList.pfm_title = "Websites to open in alternative browser";
BrowserSwitcherUrlList.pfm_description = "Setting the policy controls the list of websites to open in an alternative browser. Each item is treated as a rule for something to open in an alternative browser. Google Chrome uses those rules when choosing if a URL should open in an alternative browser. When the Internet Explorer® add-in is on, Internet Explorer® switches back to Google Chrome when the rules don't match. If rules contradict each other, Google Chrome uses the most specific rule.\n\nLeaving the policy unset adds no websites to the list.\n\nNote: Elements can also be added to this list through the BrowserSwitcherUseIeSitelist and BrowserSwitcherExternalSitelistUrl policies.";
BrowserThemeColor.pfm_title = "Configure the color of the browser's theme";
BrowserThemeColor.pfm_description = "This policy allows admins to configure the color of Google Chrome's theme. The input string should be a valid hex color string matching the format \"#RRGGBB\".\n\nSetting the policy to a valid hex color causes a theme based on that color to be automatically generated and applied to the browser. Users won't be able to change the theme set by the policy.\n\nLeaving the policy unset lets users change their browser's theme as preferred.";
BrowsingDataLifetime.pfm_title = "Browsing Data Lifetime Settings";
BrowsingDataLifetime.pfm_description = "Configures browsing data lifetime settings for Google Chrome. This policy allows admins to configure (per data-type) when data is deleted by the browser. This is useful for customers that work with sensitive customer data.\n\nWarning: Setting this policy can impact and permanently remove local personal data. It is recommended to test your settings before deploying to prevent accidental deletion of personal data.\n\nThe available data types are 'browsing_history', 'download_history', 'cookies_and_other_site_data', 'cached_images_and_files', 'password_signin', 'autofill', 'site_settings' and 'hosted_app_data'. 'download_history' and 'hosted_app_data' are not supported on Android.\n\nThe browser will automatically remove data of selected types that is older than 'time_to_live_in_hours'. The minimum value that can be set is 1 hour.\n\nThe deletion of expired data will happen 15 seconds after the browser starts then every 30 minutes while the browser is running.\n\nUntil Chrome 114, this policy required the SyncDisabled policy to be set to true. Starting Chrome 115, setting this policy will disable sync for the respective data types if neither `Chrome Sync` is disabled by setting the SyncDisabled policy nor BrowserSignin is disabled.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=BrowsingDataLifetime for more information about schema and formatting.";
BuiltInAIAPIsEnabled.pfm_title = "Allow pages to use the built-in AI APIs.";
BuiltInAIAPIsEnabled.pfm_description = "This policy controls if a page can use the built-in AI APIs (such as LanguageModel API, Summarization API, Writer API, and Rewriter API).\n\nIf the policy is enabled or unset, the APIs are enabled to be used.\n\nIf the policy is disabled, attempting using the APIs will result in an error.";
BuiltInDnsClientEnabled.pfm_title = "Use built-in DNS client";
BuiltInDnsClientEnabled.pfm_description = "This policy controls which software stack is used to communicate with the DNS server: the Operating System DNS client, or Google Chrome's built-in DNS client. This policy does not affect which DNS servers are used: if, for example, the operating system is configured to use an enterprise DNS server, that same server would be used by the built-in DNS client. It also does not control if DNS-over-HTTPS is used; Google Chrome will always use the built-in resolver for DNS-over-HTTPS requests. Please see the DnsOverHttpsMode policy for information on controlling DNS-over-HTTPS.\n\nIf this policy is set to Enabled or is left unset, the built-in DNS client will be used.\n\nIf this policy is set to Disabled, the built-in DNS client will only be used when DNS-over-HTTPS is in use.";
CACertificateManagementAllowed.pfm_title = "Allow users to manage installed CA certificates.";
CACertificateManagementAllowed.pfm_description = "0 - Allow users to manage all certificates\n1 - Allow users to manage user certificates\n2 - Disallow users from managing certificates\nSetting the policy to All (0) or leaving it unset lets users edit trust settings for all CA certificates, remove user-imported certificates, and import certificates using Certificate Manager. Setting the policy to UserOnly (1) lets users manage only user-imported certificates, but not change trust settings of built-in certificates. Setting it to None (2) lets users view (not manage) CA certificates.";
CACertificates.pfm_title = "TLS certificates that should be trusted by Google Chrome for server authentication";
CACertificates.pfm_description = "A list of TLS certificates that should be trusted by Google Chrome for server authentication.\nCertificates should be base64-encoded.";
CACertificatesWithConstraints.pfm_title = "TLS certificates that should be trusted by Google Chrome for server authentication with constraints";
CACertificatesWithConstraints.pfm_description = "A list of TLS certificates that should be trusted by Google Chrome for server authentication, with constraints added outside the certificate. If no constraint of a certain type is present, then any name of that type is allowed.\nCertificates should be base64-encoded. At least one constraint must be specified for each certificate.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=CACertificatesWithConstraints for more information about schema and formatting.";
CADistrustedCertificates.pfm_title = "TLS certificates that should be distrusted by Google Chrome for server authentication";
CADistrustedCertificates.pfm_description = "A list of certificate public keys that should be distrusted by Google Chrome for TLS server\nauthentication.\n\nThe policy value is a list of base64-encoded X.509 certificates. Any\ncertificate with a matching SPKI (SubjectPublicKeyInfo) will be distrusted.";
CAHintCertificates.pfm_title = "TLS certificates that are not trusted or distrusted but can be used in path-building for server authentication";
CAHintCertificates.pfm_description = "A list of certificates that are not trusted or distrusted in Google Chrome\nbut can be used as hints for path-building. Certificates should be base64-encoded.";
CAPlatformIntegrationEnabled.pfm_title = "Use user-added TLS certificates from platform trust stores for server authentication";
CAPlatformIntegrationEnabled.pfm_description = "If enabled(or not set), user-added TLS certificates from platform trust stores will be used in path-building for TLS server authentication.\n\nIf disabled, user-added TLS certificates from platform trust stores will not be used in path-building for TLS server authentication.";
CORSNonWildcardRequestHeadersSupport.pfm_title = "CORS non-wildcard request headers support";
CORSNonWildcardRequestHeadersSupport.pfm_description = "Configures support of CORS non-wildcard request headers.\n\nGoogle Chrome version 97 introduces support for CORS non-wildcard request headers. When scripts make a cross-origin network request via fetch() and XMLHttpRequest with a script-added Authorization header, the header must be explicitly allowed by the Access-Control-Allow-Headers header in the CORS preflight response. \"Explicitly\" here means that the wild card symbol \"*\" doesn't cover the Authorization header. See https://chromestatus.com/feature/5742041264816128 for more detail.\n\nIf this policy is not set, or set to True, Google Chrome will support the CORS non-wildcard request headers and behave as described above.\n\nWhen this policy is set to False, chrome will allow the wildcard symbol (\"*\") in the Access-Control-Allow-Headers header in the CORS preflight response to cover the Authorization header.\n\nThis Enterprise policy is temporary; it's intended to be removed in the future.";
CertificateTransparencyEnforcementDisabledForCas.pfm_title = "Disable Certificate Transparency enforcement for a list of subjectPublicKeyInfo hashes";
CertificateTransparencyEnforcementDisabledForCas.pfm_description = "Setting the policy turns off enforcement of Certificate Transparency disclosure requirements for a list of subjectPublicKeyInfo hashes. Enterprise hosts can keep using certificates that otherwise wouldn't be trusted (because they weren't properly publicly disclosed). To turn off enforcement, the hash must meet one of these conditions:\n\n* It's of the server certificate's subjectPublicKeyInfo.\n\n* It's of a subjectPublicKeyInfo that appears in a Certificate Authority (CA) certificate in the certificate chain. That CA certificate is constrained through the X.509v3 nameConstraints extension, one or more directoryName nameConstraints are present in the permittedSubtrees, and the directoryName has an organizationName attribute.\n\n* It's of a subjectPublicKeyInfo that appears in a CA certificate in the certificate chain, the CA certificate has one or more organizationName attributes in the certificate Subject, and the server's certificate has the same number of organizationName attributes, in the same order, and with byte-for-byte identical values.\n\nSpecify a subjectPublicKeyInfo hash by linking the hash algorithm name, a slash, and the Base64 encoding of that hash algorithm applied to the DER-encoded subjectPublicKeyInfo of the specified certificate. Base64 encoding format matches that of an SPKI Fingerprint. The only recognized hash algorithm is sha256; others are ignored.\n\nLeaving the policy unset means that if certificates requiring disclosure through Certificate Transparency aren't disclosed, then Google Chrome doesn't trust those certificates.";
CertificateTransparencyEnforcementDisabledForUrls.pfm_title = "Disable Certificate Transparency enforcement for a list of URLs";
CertificateTransparencyEnforcementDisabledForUrls.pfm_description = "Setting the policy turns off Certificate Transparency disclosure requirements for the hostnames in the specified URLs. While making it harder to detect misissued certificates, hosts can keep using certificates that otherwise wouldn't be trusted (because they weren't properly publicly disclosed).\n\nLeaving the policy unset means that if certificates requiring disclosure through Certificate Transparency aren't disclosed, then Google Chrome doesn't trust those certificates.\n\nA URL pattern follows this format ( https://support.google.com/chrome/a?p=url_blocklist_filter_format ). However, because the validity of certificates for a given hostname is independent of the scheme, port, or path, Google Chrome only considers the hostname portion of the URL. Wildcard hosts aren't supported.";
ChromeForTestingAllowed.pfm_title = "Allow Chrome for Testing";
ChromeForTestingAllowed.pfm_description = "Controls whether users may use Chrome for Testing.\n\nIf this policy is set to Enabled or not set, users may install and run Chrome for Testing.\n\nIf this policy is set to Disabled, users are not allowed to run Chrome for Testing. Users will still be able to install Chrome for Testing, however it will not run with the profiles where this policy is set to Disabled.";
ChromeVariations.pfm_title = "Determine the availability of variations";
ChromeVariations.pfm_description = "0 - Enable all variations\n1 - Enable variations concerning critical fixes only\n2 - Disable all variations\nConfiguring this policy allows to specify which variations are allowed to be applied in Google Chrome.\n\nVariations provide a means for offering modifications to Google Chrome without shipping a new version of the browser by selectively enabling or disabling already existing features. See https://support.google.com/chrome/a?p=Manage_the_Chrome_variations_framework for more information.\n\nSetting the VariationsEnabled (value 0), or leaving the policy not set allows all variations to be applied to the browser.\n\nSetting the CriticalFixesOnly (value 1), allows only variations considered critical security or stability fixes to be applied to Google Chrome.\n\nSetting the VariationsDisabled (value 2), prevent all variations from being applied to the browser. Please note that this mode can potentially prevent the Google Chrome developers from providing critical security fixes in a timely manner and is thus not recommended.";
ClearBrowsingDataOnExitList.pfm_title = "Clear Browsing Data on Exit";
ClearBrowsingDataOnExitList.pfm_description = "browsing_history - Browsing history\ndownload_history - Download history\ncookies_and_other_site_data - Cookies and other site data\ncached_images_and_files - Cached images and files\npassword_signin - Password signin\nautofill - Autofill\nsite_settings - Site settings\nhosted_app_data - Hosted apps data\nConfigures a list of browsing data types that should be deleted when the user closes all browser windows.\n\nWarning: Setting this policy can impact and permanently remove local personal data. It is recommended to test your settings before deploying to prevent accidental deletion of personal data.\n\nThe available data types are browsing history (browsing_history), download history (download_history), cookies (cookies_and_other_site_data), cache(cached_images_and_files), autofill (autofill), passwords (password_signin), site settings (site_settings) and hosted apps data (hosted_app_data). This policy does not take precedence over AllowDeletingBrowserHistory.\n\nUntil Chrome 114, this policy required the SyncDisabled policy to be set to true. Starting Chrome 115, setting this policy will disable sync for the respective data types if neither `Chrome Sync` is disabled by setting the SyncDisabled policy nor BrowserSignin is disabled.\n\nIf for some reason the data deletion has started and did not complete, the browsing data will be cleared the next time the profile is loaded.\n\nIf Google Chrome does not exit cleanly (for example, if the browser or the OS crashes), the browsing data will not be cleared since the browser closing was not a result of the use closing all the browser windows.";
ClickToCallEnabled.pfm_title = "Enable the Click to Call Feature";
ClickToCallEnabled.pfm_description = "Enable the Click to Call feature which allows users to send phone numbers from Chrome Desktops to an Android device when the user is Signed-in. For more information, see help center article: https://support.google.com/chrome/answer/9430554?hl=en.\n\nIf this policy is set to enabled, the capability of sending phone numbers to Android devices will be enabled for the Chrome user.\n\nIf this policy is set to disabled, the capability of sending phone numbers to Android devices will be disabled for the Chrome user.\n\nIf you set this policy, users cannot change or override it.\n\nIf this policy is left unset, the Click to Call feature is enabled by default.";
ClipboardAllowedForUrls.pfm_title = "Allow clipboard on these sites";
ClipboardAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify sites that can use the clipboard site permission. This does not include all clipboard operations on origins matching the patterns. For instance, users will still be able to paste using keyboard shortcuts as this isn't gated by the clipboard site permission.\n\n\nLeaving the policy unset means DefaultClipboardSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
ClipboardBlockedForUrls.pfm_title = "Block clipboard on these sites";
ClipboardBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify sites that can't use the clipboard site permission. This does not include all clipboard operations on origins matching the patterns. For instance, users will still be able to paste using keyboard shortcuts as this isn't gated by the clipboard site permission.\n\nLeaving the policy unset means DefaultClipboardSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
CloudManagementEnrollmentMandatory.pfm_title = "Enable mandatory cloud management enrollment";
CloudManagementEnrollmentMandatory.pfm_description = "Setting the policy to Enabled mandates Chrome Enterprise Core browser enrollment and blocks Google Chrome launch process if failed.\n\nSetting the policy to Disabled or leaving it unset renders Chrome Enterprise Core browser enrollment optional and doesn't block Google Chrome launch process if failed.\n\nMachine scope cloud policy enrollment on desktop uses this policy. See https://support.google.com/chrome/a/answer/9301891 for details.";
CloudManagementEnrollmentToken.pfm_title = "The enrollment token of cloud policy";
CloudManagementEnrollmentToken.pfm_description = "Setting the policy means Google Chrome tries to register itself with Chrome Enterprise Core browser management. The value of this policy is an enrollment token you can retrieve from the Google Admin console.\n\nSee https://support.google.com/chrome/a/answer/9301891 for details.";
CloudPolicyOverridesPlatformPolicy.pfm_title = "Google Chrome cloud policy overrides Platform policy.";
CloudPolicyOverridesPlatformPolicy.pfm_description = "Setting the policy to Enabled means cloud policy takes precedence if it conflicts with platform policy.\n\nSetting the policy to Disabled or leaving it unset means platform policy takes precedence if it conflicts with cloud policy.\n\nThis mandatory policy affects machine scope cloud policies.\n\nThis policy is specific to Google Chrome and does not affect Google Update because they are independent applications.\nGoogle Update has a separate policy with the same name.";
CloudPrintProxyEnabled.pfm_title = "Enable Google Cloud Print proxy";
CloudPrintProxyEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset lets Google Chrome act as a proxy between Google Cloud Print and legacy printers connected to the machine. Using their Google Account, users may turn on the cloud print proxy by authentication.\n\nSetting the policy to Disabled means users can't turn on the proxy, and the machine can't share its printers with Google Cloud Print.";
CloudUserPolicyMerge.pfm_title = "Enables merging of user cloud policies into machine-level policies";
CloudUserPolicyMerge.pfm_description = "Setting the policy to Enabled allows policies associated with a managed account to be merged into machine-level policies.\n\nSetting the policy to Disabled or leaving it unset prevents user-level cloud policies from being merged with policies from any other sources.\n\nOnly policies originating from secure users can take precedence. A secure user is affiliated with the organization that manages their browser using Chrome Enterprise Core. All other user-level policies will have default precedence.\n\nPolicies that need to be merged also need to be set in either PolicyListMultipleSourceMergeList or PolicyDictionaryMultipleSourceMergeList. This policy will be ignored if neither of the two aforementioned policies is configured.";
CloudUserPolicyOverridesCloudMachinePolicy.pfm_title = "Allow user cloud policies to override Chrome Browser Cloud Management policies.";
CloudUserPolicyOverridesCloudMachinePolicy.pfm_description = "Setting the policy to Enabled allows policies associated with a managed account to take precedence if they conflict with Chrome Enterprise Core browser policies.\n\nSetting the policy to Disabled or leaving it unset causes user-level cloud policies to have default priority.\n\nOnly policies originating from secure users can take precedence. A secure user is affiliated with the organization that manages their browser using Chrome Enterprise Core. All other user-level policies will have default precedence.\n\nThe policy can be combined with CloudPolicyOverridesPlatformPolicy. If both policies are enabled, user cloud policies will also take precedence over conflicting platform policies.";
CommandLineFlagSecurityWarningsEnabled.pfm_title = "Enable security warnings for command-line flags";
CommandLineFlagSecurityWarningsEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset means security warnings appear when potentially dangerous command-line flags are used to launch Chrome.\n\nSetting the policy to Disabled prevents security warnings from appearing when Chrome is launched with potentially dangerous command-line flags.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
ComponentUpdatesEnabled.pfm_title = "Enable component updates in Google Chrome";
ComponentUpdatesEnabled.pfm_description = "Enables component updates for all components in Google Chrome when not set or set to enabled.\n\nIf set to disabled, updates to components are disabled. However, some components are exempt from this policy: updates to any component that does not contain executable code and is critical for the security of the browser will not be disabled.\nExamples of such components include the certificate revocation lists and subresource filters.";
CompressionDictionaryTransportEnabled.pfm_title = "Enable compression dictionary transport support";
CompressionDictionaryTransportEnabled.pfm_description = "This feature enables the use of dictionary-specific content encodings in the Accept-Encoding request header (\"sbr\" and \"zst-d\") when dictionaries are available for use.\n\nSetting the policy to Enabled or leaving it unset means Google Chrome will accept web contents using the compression dictionary transport feature.\nSetting the policy to Disabled turns off the compression dictionary transport feature.";
CookiesAllowedForUrls.pfm_title = "Allow cookies on these sites";
CookiesAllowedForUrls.pfm_description = "Allows you to set a list of url patterns that specify sites which are allowed to set cookies.\n\nURL patterns may be a single URL indicating that the site may use cookies on all top-level sites.\n\nPatterns may also be two URLs delimited by a comma. The first specifies the site that should be allowed to use cookies. The second specifies the top-level site that the first value should be applied on.\n\nIf you use a pair of URLs, the first value in the pair supports * but the second value does not. Using * for the first value indicates that all sites may use cookies when the second URL is the top-level site.\n\nIf this policy is left not set the global default value will be used for all sites either from the DefaultCookiesSetting or BlockThirdPartyCookies policies if they are set, or the user's personal configuration otherwise.\n\nSee also policies CookiesBlockedForUrls and CookiesSessionOnlyForUrls. Note that there must be no conflicting URL patterns between these three policies - it is unspecified which policy takes precedence.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
CookiesBlockedForUrls.pfm_title = "Block cookies on these sites";
CookiesBlockedForUrls.pfm_description = "Setting the policy lets you make a list of URL patterns that specify sites that can't set cookies.\n\nLeaving the policy unset results in the use of DefaultCookiesSetting for all sites, if it's set. If not, the user's personal setting applies.\n\nWhile no specific policy takes precedence, see CookiesAllowedForUrls and CookiesSessionOnlyForUrls. URL patterns among these 3 policies must not conflict.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
CookiesSessionOnlyForUrls.pfm_title = "Limit cookies from matching URLs to the current session";
CookiesSessionOnlyForUrls.pfm_description = "Unless the RestoreOnStartup policy is set to permanently restore URLs from previous sessions, then setting CookiesSessionOnlyForUrls lets you make a list of URL patterns that specify sites that can and can't set cookies for one session.\n\nLeaving the policy unset results in the use of DefaultCookiesSetting for all sites, if it's set. If not, the user's personal setting applies. URLs not covered by the patterns specified also result in the use of defaults.\n\nWhile no specific policy takes precedence, see CookiesBlockedForUrls and CookiesAllowedForUrls. URL patterns among these 3 policies must not conflict.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
CreatePasskeysInICloudKeychain.pfm_title = "Control whether passkey creation will default to iCloud Keychain.";
CreatePasskeysInICloudKeychain.pfm_description = "Google Chrome may direct\npasskey/WebAuthn creation requests directly to iCloud Keychain on macOS 13.5\nor later. If iCloud Keychain syncing has not been enabled yet, this will\nprompt the user to sign in with iCloud, or may prompt them to enable iCloud\nKeychain syncing.\n\nIf this policy is set to false, iCloud Keychain will not be used by default\nand the previous behavior (of creating the credential in the Google Chrome profile) may be used\ninstead. Users will still be able to select iCloud Keychain as an option, and\nmay still see iCloud Keychain credentials when signing in.\n\nIf this policy is set to \"true\" then iCloud Keychain will be the default\nwhenever the WebAuthn request is compatible with that choice.\n\nIf this policy is not set then the default depends on factors such as\nwhether iCloud Drive is enabled, and whether the user has recently used or\ncreated a credential in their\nGoogle Chrome profile.";
CreateThemesSettings.pfm_title = "Settings for Create Themes with AI";
CreateThemesSettings.pfm_description = "0 - Allow Create Themes and improve AI models.\n1 - Allow Create Themes without improving AI models.\n2 - Do not allow Create Themes.\nCreate Themes with AI lets users create custom themes/wallpapers by preselecting from a list of options.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. It may also be reviewed by humans to improve AI models. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
DNSInterceptionChecksEnabled.pfm_title = "DNS interception checks enabled";
DNSInterceptionChecksEnabled.pfm_description = "This policy configures a local switch that can be used to disable DNS interception checks. The checks attempt to discover whether the browser is behind a proxy that redirects unknown host names.\n\nThis detection may not be necessary in an enterprise environment where the network configuration is known, since it causes some amount of DNS and HTTP traffic on start-up and each DNS configuration change.\n\nWhen this policy is not set, or is enabled, the DNS interception checks are performed. When explicitly disabled, they're not.";
DataURLWhitespacePreservationEnabled.pfm_title = "DataURL Whitespace Preservation for all media types";
DataURLWhitespacePreservationEnabled.pfm_description = "This policy provides a temporary opt-out for changes to how Chrome handles whitepsace in data URLS.\nPreviously, whitespace would be kept only if the top level media type was text or contained the media type string xml.\nNow, whitespace will be preserved in all data URLs, regardless of media type.\n\nIf this policy is left unset or is set to True, the new behavior is enabled.\n\nWhen this policy is set to False, the old behavior is enabled.";
DataUrlInSvgUseEnabled.pfm_title = "Data URL support for SVGUseElement.";
DataUrlInSvgUseEnabled.pfm_description = "This policy enables Data URL support for SVGUseElement, which will be disabled\nby default starting in M119.\nIf this policy is set to Enabled, Data URLs will continue to work in SVGUseElement.\nIf this policy is set to Disabled or not set, Data URLs won't work in SVGUseElement.";
DefaultBrowserSettingEnabled.pfm_title = "Set Google Chrome as Default Browser";
DefaultBrowserSettingEnabled.pfm_description = "Setting the policy to True has Google Chrome always check whether it's the default browser on startup and, if possible, automatically register itself. Setting the policy to False stops Google Chrome from ever checking if it's the default and turns user controls off for this option.\n\nLeaving the policy unset means Google Chrome lets users control whether it's the default and, if not, whether user notifications should appear.\n\nNote: For Microsoft®Windows® administrators, turning this setting on only works for machines running Windows 7. For later versions, you must deploy a \"default application associations\" file that makes Google Chrome the handler for the https and http protocols (and, optionally, the ftp protocol and other file formats). See Chrome Help ( https://support.google.com/chrome?p=make_chrome_default_win ).";
DefaultClipboardSetting.pfm_title = "Default clipboard setting";
DefaultClipboardSetting.pfm_description = "2 - Do not allow any site to use the clipboard site permission\n3 - Allow sites to ask the user to grant the clipboard site permission\nSetting the policy to 2 blocks sites from using the clipboard site permission. Setting the policy to 3 or leaving it unset lets the user change the setting and decide if the clipboard APIs are available when a site wants to use one.\n\nThis policy can be overridden for specific URL patterns using the ClipboardAllowedForUrls and ClipboardBlockedForUrls policies.\n\nThis policy only affects clipboard operations controlled by the clipboard site permission, and does not affect sanitized clipboard writes or trusted copy and paste operations.";
DefaultCookiesSetting.pfm_title = "Default cookies setting";
DefaultCookiesSetting.pfm_description = "1 - Allow all sites to set local data\n2 - Do not allow any site to set local data\n4 - Keep cookies for the duration of the session\nUnless the RestoreOnStartup policy is set to permanently restore URLs from previous sessions, then setting CookiesSessionOnlyForUrls lets you make a list of URL patterns that specify sites that can and can't set cookies for one session.\n\nLeaving the policy unset results in the use of DefaultCookiesSetting for all sites, if it's set. If not, the user's personal setting applies. URLs not covered by the patterns specified also result in the use of defaults.\n\nWhile no specific policy takes precedence, see CookiesBlockedForUrls and CookiesAllowedForUrls. URL patterns among these 3 policies must not conflict.";
DefaultDownloadDirectory.pfm_title = "Set default download directory";
DefaultDownloadDirectory.pfm_description = "Setting the policy changes the default directory that Chrome downloads files to, but users can change the directory.\n\nLeaving the policy unset means Chrome uses its platform-specific default directory.\n\nThis policy has no effect if the policy DownloadDirectory is set.\n\nNote: See a list of variables you can use ( https://www.chromium.org/administrators/policy-list-3/user-data-directory-variables ).";
DefaultFileSystemReadGuardSetting.pfm_title = "Control use of the File System API for reading";
DefaultFileSystemReadGuardSetting.pfm_description = "2 - Do not allow any site to request read access to files and directories via the File System API\n3 - Allow sites to ask the user to grant read access to files and directories via the File System API\nSetting the policy to 3 lets websites ask for read access to files and directories in the host operating system's file system via the File System API. Setting the policy to 2 denies access.\n\nLeaving it unset lets websites ask for access, but users can change this setting.";
DefaultFileSystemWriteGuardSetting.pfm_title = "Control use of the File System API for writing";
DefaultFileSystemWriteGuardSetting.pfm_description = "2 - Do not allow any site to request write access to files and directories\n3 - Allow sites to ask the user to grant write access to files and directories\nSetting the policy to 3 lets websites ask for write access to files and directories in the host operating system's file system. Setting the policy to 2 denies access.\n\nLeaving it unset lets websites ask for access, but users can change this setting.";
DefaultGeolocationSetting.pfm_title = "Default geolocation setting";
DefaultGeolocationSetting.pfm_description = "1 - Allow sites to track the users' physical location\n2 - Do not allow any site to track the users' physical location\n3 - Ask whenever a site wants to track the users' physical location\nSetting the policy to 1 lets sites track the users' physical location as the default state. Setting the policy to 2 denies this tracking by default. You can set the policy to ask whenever a site wants to track the users' physical location.\n\nLeaving the policy unset means the AskGeolocation policy applies, but users can change this setting.";
DefaultImagesSetting.pfm_title = "Default images setting";
DefaultImagesSetting.pfm_description = "1 - Allow all sites to show all images\n2 - Do not allow any site to show images\nSetting the policy to 1 lets all websites display images. Setting the policy to 2 denies image display.\n\nLeaving it unset allows images, but users can change this setting.";
DefaultInsecureContentSetting.pfm_title = "Control use of insecure content exceptions";
DefaultInsecureContentSetting.pfm_description = "2 - Do not allow any site to load mixed content\n3 - Allow users to add exceptions to allow mixed content\nAllows you to set whether users can add exceptions to allow mixed content for specific sites.\n\nThis policy can be overridden for specific URL patterns using the 'InsecureContentAllowedForUrls' and 'InsecureContentBlockedForUrls' policies.\n\nIf this policy is left not set, users will be allowed to add exceptions to allow blockable mixed content and disable autoupgrades for optionally blockable mixed content.";
DefaultJavaScriptJitSetting.pfm_title = "Control use of JavaScript JIT";
DefaultJavaScriptJitSetting.pfm_description = "1 - Allow any site to run JavaScript JIT\n2 - Do not allow any site to run JavaScript JIT\nAllows you to set whether Google Chrome will run the v8 JavaScript engine with JIT (Just In Time) compiler enabled or not.\n\nDisabling the JavaScript JIT will mean that Google Chrome may render web content more slowly, and may also disable parts of JavaScript including WebAssembly. Disabling the JavaScript JIT may allow Google Chrome to render web content in a more secure configuration.\n\nThis policy can be overridden for specific URL patterns using the JavaScriptJitAllowedForSites and JavaScriptJitBlockedForSites policies.\n\nIf this policy is left not set, JavaScript JIT is enabled.";
DefaultJavaScriptOptimizerSetting.pfm_title = "Control use of JavaScript optimizers";
DefaultJavaScriptOptimizerSetting.pfm_description = "1 - Enable advanced JavaScript optimizations on all sites\n2 - Disable advanced JavaScript optimizations on all sites\nAllows you to set whether Google Chrome\nwill run the v8 JavaScript engine with more advanced JavaScript optimizations enabled.\n\nDisabling JavaScript optimizations (by setting this policy's value to 2) will\nmean that Google Chrome may render web\ncontent more slowly.\n\nThis policy can be overridden for specific URL patterns using the JavaScriptOptimizerAllowedForSites and JavaScriptOptimizerBlockedForSites policies.\n\nIf this policy is left not set, JavaScript optimizations are enabled.";
DefaultJavaScriptSetting.pfm_title = "Default JavaScript setting";
DefaultJavaScriptSetting.pfm_description = "1 - Allow all sites to run JavaScript\n2 - Do not allow any site to run JavaScript\nSetting the policy to 1 lets websites run JavaScript. Setting the policy to 2 denies JavaScript.\n\nLeaving it unset allows JavaScript, but users can change this setting.";
DefaultLocalFontsSetting.pfm_title = "Default Local Fonts permission setting";
DefaultLocalFontsSetting.pfm_description = "2 - Denies the Local Fonts permission on all sites by default\n3 - Ask every time a site wants obtain the Local Fonts permission\nSetting the policy to BlockLocalFonts (value 2) automatically denies the local fonts permission to sites by default. This will limit the ability of sites to see information about local fonts.\n\nSetting the policy to AskLocalFonts (value 3) will prompt the user when the local fonts permission is requested by default. If users allow the permission, it will extend the ability of sites to see information about local fonts.\n\nLeaving the policy unset means the default behavior applies which is to prompt the user, but users can change this setting";
DefaultNotificationsSetting.pfm_title = "Default notification setting";
DefaultNotificationsSetting.pfm_description = "1 - Allow sites to show desktop notifications\n2 - Do not allow any site to show desktop notifications\n3 - Ask every time a site wants to show desktop notifications\nSetting the policy to 1 lets websites display desktop notifications. Setting the policy to 2 denies desktop notifications.\n\nLeaving it unset means AskNotifications applies, but users can change this setting.";
DefaultPopupsSetting.pfm_title = "Default pop-ups setting";
DefaultPopupsSetting.pfm_description = "1 - Allow all sites to show pop-ups\n2 - Do not allow any site to show pop-ups\nSetting the policy to 1 lets websites display pop-ups. Setting the policy to 2 denies pop-ups.\n\nLeaving it unset means BlockPopups applies, but users can change this setting.";
DefaultPrinterSelection.pfm_title = "Default printer selection rules";
DefaultPrinterSelection.pfm_description = "Setting the policy sets the rules for selecting the default printer in Google Chrome, overriding the default rules. Printer selection occurs the first time users try to print, when Google Chrome seeks a printer matching the specified attributes. In case of a less than perfect match, Google Chrome can be set to select any matching printer, depending on the order printers are discovered.\n\nLeaving the policy unset or set to attributes for which there's no match means the built-in PDF printer is the default. If there's no PDF printer, Google Chrome defaults to none.\n\nCurrently, all printers are classified as \"local\". Printers connected to Google Cloud Print are considered \"cloud\", but Google Cloud Print is no longer supported.\n\nNote: Omitting a field means all values match for that particular field. For example, not specifying idPattern means Print Preview accepts all printer IDs. Regular expression patterns must follow the JavaScript RegExp syntax, and matches are case sensistive.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=DefaultPrinterSelection for more information about schema and formatting.";
DefaultSearchProviderAlternateURLs.pfm_title = "List of alternate URLs for the default search provider";
DefaultSearchProviderAlternateURLs.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderAlternateURLs specifies a list of alternate URLs for extracting search terms from the search engine. The URLs should include the string '{searchTerms}'.\n\nLeaving DefaultSearchProviderAlternateURLs unset means no alternate URLs are used to extract search terms.";
DefaultSearchProviderContextMenuAccessAllowed.pfm_title = "Allow default search provider context menu search access";
DefaultSearchProviderContextMenuAccessAllowed.pfm_description = "Enables the use of a default search provider on the context menu.\n\nIf you set this policy to disabled the search context menu item that relies on your default search provider will not be available.\n\nIf this policy is set to enabled or not set, the context menu item for your default search provider will be available.\n\nThe policy value is only appled when the DefaultSearchProviderEnabled policy is enabled, and is not applicable otherwise.";
DefaultSearchProviderEnabled.pfm_title = "Enable the default search provider";
DefaultSearchProviderEnabled.pfm_description = "Setting the policy to Enabled means a default search is performed when a user enters non-URL text in the address bar. To specify the default search provider, set the rest of the default search policies. If you leave those policies empty, the user can choose the default provider. Setting the policy to Disabled means there's no search when the user enters non-URL text in the address bar. The Disabled value is not supported by the Google Admin console.\n\nIf you set the policy, users can't change it in Google Chrome. If not set, the default search provider is on, and users can set the search provider list.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
DefaultSearchProviderEncodings.pfm_title = "Default search provider encodings";
DefaultSearchProviderEncodings.pfm_description = "If DefaultSearchProviderEnabled is on, setting DefaultSearchProviderEncodings specifies the character encodings supported by the search provider. Encodings are code page names such as UTF-8, GB2312, and ISO-8859-1. They're tried in the order provided.\n\nLeaving DefaultSearchProviderEncodings unset puts UTF-8 in use.";
DefaultSearchProviderImageURL.pfm_title = "Parameter providing search-by-image feature for the default search provider";
DefaultSearchProviderImageURL.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderImageURL specifies the URL of the search engine used for image search. (If DefaultSearchProviderImageURLPostParams is set, then image search requests use the POST method instead.)\n\nLeaving DefaultSearchProviderImageURL unset means no image search is used.\n\nIf image search uses the GET method, then the URL must specify image\nparameters using a valid combination of the following placeholders:\n'{google:imageURL}',\n'{google:imageOriginalHeight}',\n'{google:imageOriginalWidth}',\n'{google:processedImageDimensions}',\n'{google:imageSearchSource}',\n'{google:imageThumbnail}',\n'{google:imageThumbnailBase64}'.";
DefaultSearchProviderImageURLPostParams.pfm_title = "Parameters for image URL which uses POST";
DefaultSearchProviderImageURLPostParams.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderImageURLPostParams specifies the parameters during image search with POST. It consists of comma-separated, name-value pairs. If a value is a template parameter, such as {imageThumbnail}, real image thumbnail data replaces it.\n\nLeaving DefaultSearchProviderImageURLPostParams unset means image search request is sent using the GET method.\n\nThe URL must specify the image parameter using a valid combination of\nthe following placeholders depending on what the search provider supports:\n'{google:imageURL}',\n'{google:imageOriginalHeight}',\n'{google:imageOriginalWidth}',\n'{google:processedImageDimensions}',\n'{google:imageSearchSource}',\n'{google:imageThumbnail}',\n'{google:imageThumbnailBase64}'.";
DefaultSearchProviderKeyword.pfm_title = "Default search provider keyword";
DefaultSearchProviderKeyword.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderKeyword specifies the keyword or shortcut used in the address bar to trigger the search for this provider.\n\nLeaving DefaultSearchProviderKeyword unset means no keyword activates the search provider.";
DefaultSearchProviderName.pfm_title = "Default search provider name";
DefaultSearchProviderName.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderName specifies the default search provider's name.\n\nLeaving DefaultSearchProviderName unset means the hostname specified by the search URL is used.";
DefaultSearchProviderNewTabURL.pfm_title = "Default search provider new tab page URL";
DefaultSearchProviderNewTabURL.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderNewTabURL specifies the URL of the search engine used to provide a New Tab page.\n\nLeaving DefaultSearchProviderNewTabURL unset means no new tab page is provided.";
DefaultSearchProviderSearchURL.pfm_title = "Default search provider search URL";
DefaultSearchProviderSearchURL.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderSearchURL specifies the URL of the search engine used during a default search. The URL should include the string '{searchTerms}', replaced in the query by the user's search terms.\n\nYou can specify Google's search URL as: '{google:baseURL}search?q={searchTerms}&{google:RLZ}{google:originalQueryForSuggestion}{google:assistedQueryStats}{google:searchFieldtrialParameter}{google:searchClient}{google:sourceId}ie={inputEncoding}'.";
DefaultSearchProviderSearchURLPostParams.pfm_title = "Parameters for search URL which uses POST";
DefaultSearchProviderSearchURLPostParams.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderSearchURLPostParams specifies the parameters when searching a URL with POST. It consists of comma-separated, name-value pairs. If a value is a template parameter, such as '{searchTerms}', real search terms data replaces it.\n\nLeaving DefaultSearchProviderSearchURLPostParams unset means search requests are sent using the GET method.";
DefaultSearchProviderSuggestURL.pfm_title = "Default search provider suggest URL";
DefaultSearchProviderSuggestURL.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderSuggestURL specifies the URL of the search engine to provide search suggestions. The URL should include the string '{searchTerms}', replaced in the query by the user's search terms.\n\nYou can specify Google's search URL as: '{google:baseURL}complete/search?output=chrome&q={searchTerms}'.";
DefaultSearchProviderSuggestURLPostParams.pfm_title = "Parameters for suggest URL which uses POST";
DefaultSearchProviderSuggestURLPostParams.pfm_description = "If DefaultSearchProviderEnabled is on, then setting DefaultSearchProviderSuggestURLPostParams specifies the parameters during suggestion search with POST. It consists of comma-separated, name-value pairs. If a value is a template parameter, such as '{searchTerms}', real search terms data replaces it.\n\nLeaving DefaultSearchProviderSuggestURLPostParams unset unset means suggest search requests are sent using the GET method.";
DefaultSensorsSetting.pfm_title = "Default sensors setting";
DefaultSensorsSetting.pfm_description = "1 - Allow sites to access sensors\n2 - Do not allow any site to access sensors\nSetting the policy to 1 lets websites access and use sensors such as motion and light. Setting the policy to 2 denies access to sensors.\n\nLeaving it unset means AllowSensors applies, but users can change this setting.";
DefaultSerialGuardSetting.pfm_title = "Control use of the Serial API";
DefaultSerialGuardSetting.pfm_description = "2 - Do not allow any site to request access to serial ports via the Serial API\n3 - Allow sites to ask the user to grant access to a serial port\nSetting the policy to 3 lets websites ask for access to serial ports. Setting the policy to 2 denies access to serial ports.\n\nLeaving it unset lets websites ask for access, but users can change this setting.";
DefaultThirdPartyStoragePartitioningSetting.pfm_title = "Default third-party storage partitioning setting";
DefaultThirdPartyStoragePartitioningSetting.pfm_description = "1 - Allow third-party storage partitioning by default.\n2 - Disable third-party storage partitioning.\nThis policy controls whether third-party storage partitioning is allowed by default.\n\nIf this policy is set to 1 - AllowPartitioning, or unset, third-party storage partitioning will be allowed by default. This default may be overridden for specific top-level origins by other means.\n\nIf this policy is set to 2 - BlockPartitioning, third-party storage partitioning will be disabled for all contexts.\n\nUse ThirdPartyStoragePartitioningBlockedForOrigins to disable third-party storage partitioning for specific top-level origins. For detailed information on third-party storage partitioning, please see https://developers.google.com/privacy-sandbox/cookies/storage-partitioning.";
DefaultWebBluetoothGuardSetting.pfm_title = "Control use of the Web Bluetooth API";
DefaultWebBluetoothGuardSetting.pfm_description = "2 - Do not allow any site to request access to Bluetooth devices via the Web Bluetooth API\n3 - Allow sites to ask the user to grant access to a nearby Bluetooth device\nSetting the policy to 3 lets websites ask for access to nearby Bluetooth devices. Setting the policy to 2 denies access to nearby Bluetooth devices.\n\nLeaving the policy unset lets sites ask for access, but users can change this setting.";
DefaultWebHidGuardSetting.pfm_title = "Control use of the WebHID API";
DefaultWebHidGuardSetting.pfm_description = "2 - Do not allow any site to request access to HID devices via the WebHID API\n3 - Allow sites to ask the user to grant access to a HID device\nSetting the policy to 3 lets websites ask for access to HID devices. Setting the policy to 2 denies access to HID devices.\n\nLeaving it unset lets websites ask for access, but users can change this setting.\n\nThis policy can be overridden for specific url patterns using the WebHidAskForUrls and WebHidBlockedForUrls policies.";
DefaultWebUsbGuardSetting.pfm_title = "Control use of the WebUSB API";
DefaultWebUsbGuardSetting.pfm_description = "2 - Do not allow any site to request access to USB devices via the WebUSB API\n3 - Allow sites to ask the user to grant access to a connected USB device\nSetting the policy to 3 lets websites ask for access to connected USB devices. Setting the policy to 2 denies access to connected USB devices.\n\nLeaving it unset lets websites ask for access, but users can change this setting.";
DefaultWindowManagementSetting.pfm_title = "Default Window Management permission setting";
DefaultWindowManagementSetting.pfm_description = "2 - Denies the Window Management permission on all sites by default\n3 - Ask every time a site wants obtain the Window Management permission\nSetting the policy to BlockWindowManagement (value 2) automatically denies the window management permission to sites by default. This will limit the ability of sites to see information about the device's screens and use that information to open and place windows or request fullscreen on specific screens.\n\nSetting the policy to AskWindowManagement (value 3) will prompt the user when the window management permission is requested by default. If users allow the permission, it will extend the ability of sites to see information about the device's screens and use that information to open and place windows or request fullscreen on specific screens.\n\nLeaving the policy unset means the AskWindowManagement policy applies, but users can change this setting.\n\nThis replaces the deprecated DefaultWindowPlacementSetting policy.";
DeletingUndecryptablePasswordsEnabled.pfm_title = "Enable deleting undecryptable passwords";
DeletingUndecryptablePasswordsEnabled.pfm_description = "This policy controls whether the built-in password manager can delete undecryptable passwords from its database. This is required to restore the full functionality of the built-in password manager, but it may include a permanent data loss. Undecryptable password values will not become decryptable on their own and, if fixing them is possible, it usually requires complex user actions.\n\nSetting the policy to Enabled or leaving it unset means that users with undecryptable passwords saved to the built-in password manager will lose them. Passwords that are still in a working state will remain untouched.\n\nSetting the policy to Disabled means users will leave their password manager data untouched, but will experience a broken password manager functionality.\n\nIf the policy is set, users can't change it in Google Chrome.";
DesktopSharingHubEnabled.pfm_title = "Enable desktop sharing in the omnibox and 3-dot menu";
DesktopSharingHubEnabled.pfm_description = "Setting the policy to True or leaving it unset lets users share or save the current webpage using actions provided by the desktop sharing hub. The sharing hub is accessed through either an omnibox icon or the 3-dot menu.\n\nSetting the policy to False removes the sharing icon from the omnibox and the entry from the 3-dot menu.";
DevToolsGenAiSettings.pfm_title = "Settings for DevTools Generative AI Features";
DevToolsGenAiSettings.pfm_description = "0 - Allow DevTools Generative AI Features and improve AI models.\n1 - Allow DevTools Generative AI Features without improving AI models.\n2 - Do not allow DevTools Generative AI Features.\nThese features in Google Chrome's DevTools employ generative AI models to provide additional debugging information. To use these features, Google Chrome has to collect data such as error messages, stack traces, code snippets, and network requests and send them to a server owned by Google, which runs a generative AI model. Response body or authentication and cookie headers in network requests are not included in the data sent to the server.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. It may also be reviewed by humans to improve AI models. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nDevTools Generative AI features include:\n\n- Console Insights: explains console messages and offers suggestions on how to fix console errors.\n\n- AI assistance: get help with understanding CSS styles (since version 131), network requests, performance, and files (all since version 132).\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
DeveloperToolsAvailability.pfm_title = "Control where Developer Tools can be used";
DeveloperToolsAvailability.pfm_description = "0 - Disallow usage of the Developer Tools on apps and extensions installed by enterprise policy or, since version 114 and if this is a managed user, extensions built into the browser. Allow usage of the Developer Tools in other contexts\n1 - Allow usage of the Developer Tools\n2 - Disallow usage of the Developer Tools\nSetting the policy to 0 (the default) means you can access the developer tools and the JavaScript console, but not in the context of extensions installed by enterprise policy or, since version 114 and if this is a managed user, extensions built into the browser. Setting the policy to 1 means you can access the developer tools and the JavaScript console in all contexts, including that of extensions installed by enterprise policy. Setting the policy to 2 means you can't access developer tools, and you can't inspect website elements.\n\nThis setting also turns off keyboard shortcuts and menu or context menu entries to open developer tools or the JavaScript console.\n\nAs of Google Chrome version 99, this setting also controls entry points for the 'View page source' feature. If you set this policy to 'DeveloperToolsDisallowed' (value 2), users cannot access source viewing via keyboard shortcut or the context menu. To fully block source viewing, you must also add 'view-source:*' to the URLBlocklist policy.\n\nAs of Google Chrome version 119, this setting also controls whether developer mode for Isolated Web Apps can be activated and used.\n\nAs of Google Chrome version 128, this setting will not control developer mode on extensions page if ExtensionDeveloperModeSettings policy is set.";
Disable3DAPIs.pfm_title = "Disable support for 3D graphics APIs";
Disable3DAPIs.pfm_description = "Setting the policy to True (or setting HardwareAccelerationModeEnabled to False) prevents webpages from accessing the WebGL API, and plugins can't use the Pepper 3D API.\n\nSetting the policy to False or leaving it unset lets webpages use the WebGL API and plugins use the Pepper 3D API, but the browser's default settings might still require command line arguments to use these APIs.";
DisableAuthNegotiateCnameLookup.pfm_title = "Disable CNAME lookup when negotiating Kerberos authentication";
DisableAuthNegotiateCnameLookup.pfm_description = "Setting the policy to Enabled skips CNAME lookup. The server name is used as entered when generating the Kerberos SPN.\n\nSetting the policy to Disabled or leaving it unset means CNAME lookup determines the canonical name of the server when generating the Kerberos SPN.";
DisablePrintPreview.pfm_title = "Disable Print Preview";
DisablePrintPreview.pfm_description = "Setting the policy to Enabled has Google Chrome open the system print dialog instead of the built-in print preview when users request a printout.\n\nSetting the policy to Disabled or leaving it unset has print commands trigger the print preview screen.";
DisableSafeBrowsingProceedAnyway.pfm_title = "Disable proceeding from the Safe Browsing warning page";
DisableSafeBrowsingProceedAnyway.pfm_description = "Setting the policy to Enabled prevents users from proceeding past the warning page the Safe Browsing service shows to the malicious site. This policy only prevents users from proceeding on Safe Browsing warnings such as malware and phishing, not for SSL certificate-related issues such as invalid or expired certificates.\n\nSetting the policy to Disabled or leaving it unset means users can choose to proceed to the flagged site after the warning appears.\n\nSee more about Safe Browsing ( https://developers.google.com/safe-browsing ).";
DisableScreenshots.pfm_title = "Disable taking screenshots";
DisableScreenshots.pfm_description = "Setting the policy to Enabled disallows screenshots taken with keyboard shortcuts\nor extension APIs. Setting the policy to Disabled or not set allows screenshots.\n\nNote that on Microsoft® Windows®, macOS and Linux,\nthis does not prevent screenshots that are taken with operating system or third party applications.";
DiskCacheDir.pfm_title = "Set disk cache directory";
DiskCacheDir.pfm_description = "Setting the policy has Google Chrome use the directory you provide for storing cached files on the disk—whether or not users specify the --disk-cache-dir flag.\n\nIf not set, Google Chrome uses the default cache directory, but users can change that setting with the --disk-cache-dir command line flag.\n\nGoogle Chrome manages the contents of a volume's root directory. So to avoid data loss or other errors, do not set this policy to the root directory or any directory used for other purposes. See the variables you can use ( https://www.chromium.org/administrators/policy-list-3/user-data-directory-variables ).";
DiskCacheSize.pfm_title = "Set disk cache size";
DiskCacheSize.pfm_description = "Setting the policy to None has Google Chrome use the default cache size for storing cached files on the disk. Users can't change it.\n\nIf you set the policy, Google Chrome uses the cache size you provide—whether or not users specify the --disk-cache-size flag. (Values below a few megabytes are rounded up.)\n\nIf not set, Google Chrome uses the default size. Users can change that setting using the --disk-cache-size flag.\n\nNote: The value specified in this policy is used as a hint to various cache subsystems in the browser. Therefore the actual total disk consumption of all caches will be higher but within the same order of magnitude as the value specified.";
DnsOverHttpsMode.pfm_title = "Controls the mode of DNS-over-HTTPS";
DnsOverHttpsMode.pfm_description = "off - Disable DNS-over-HTTPS\nautomatic - Enable DNS-over-HTTPS with insecure fallback\nsecure - Enable DNS-over-HTTPS without insecure fallback\nControls the mode of the DNS-over-HTTPS resolver. Please note that this\npolicy will only set the default mode for each query. The mode may be\noverridden for special types of queries such as requests to resolve a\nDNS-over-HTTPS server hostname.\n\nThe \"off\" mode will disable\nDNS-over-HTTPS.\n\nThe \"automatic\" mode will send\nDNS-over-HTTPS queries first if a DNS-over-HTTPS server is available and\nmay fallback to sending insecure queries on error.\n\nThe \"secure\" mode will only send\nDNS-over-HTTPS queries and will fail to resolve on error.\n\nOn Android Pie and above, if DNS-over-TLS\nis active, Google Chrome will not\nsend insecure DNS requests.\n\nIf this policy is unset, for managed devices DNS-over-HTTPS queries will not\nbe sent. Otherwise, the browser may send DNS-over-HTTPS requests to a\nresolver associated with the user's configured system resolver.";
DnsOverHttpsTemplates.pfm_title = "Specify URI template of desired DNS-over-HTTPS resolver";
DnsOverHttpsTemplates.pfm_description = "The URI template of the desired DNS-over-HTTPS resolver. To specify multiple DNS-over-HTTPS resolvers, separate the corresponding URI templates with spaces.\n\nIf the DnsOverHttpsMode is set to \"secure\" then this policy must be set and not empty. On Google ChromeOS only, either this policy or the DnsOverHttpsTemplatesWithIdentifiers must be set, otherwise the DNS resolution will fail.\n\nIf the DnsOverHttpsMode is set to \"automatic\" and this policy is set then the URI templates specified will be used; if this policy is unset then hardcoded mappings will be used to attempt to upgrade the user's current DNS resolver to a DoH resolver operated by the same provider.\n\nIf the URI template contains a dns variable, requests to the resolver will use GET; otherwise requests will use POST.\n\nIncorrectly formatted templates will be ignored.";
DomainReliabilityAllowed.pfm_title = "Allow reporting of domain reliability related data";
DomainReliabilityAllowed.pfm_description = "If this policy is set false, domain reliability diagnostic data reporting is disabled and no data is sent to Google.\nIf this policy is set true or not set, domain reliability diagnostic data reporting will follow the behavior of MetricsReportingEnabled for Google Chrome or DeviceMetricsReportingEnabled for Google ChromeOS.";
DownloadDirectory.pfm_title = "Set download directory";
DownloadDirectory.pfm_description = "Setting the policy sets up the directory Chrome uses for downloading files. It uses the provided directory, whether or not users specify one or turned on the flag to be prompted for download location every time.\n\nThis policy overrides the DefaultDownloadDirectory policy.\n\nLeaving the policy unset means Chrome uses the default download directory, and users can change it.\n\nOn Google ChromeOS it's possible to set it only to Google Drive directories.\n\nNote: See a list of variables you can use ( https://www.chromium.org/administrators/policy-list-3/user-data-directory-variables ).";
DownloadRestrictions.pfm_title = "Download restrictions";
DownloadRestrictions.pfm_description = "0 - No special restrictions. Default.\n1 - Block malicious downloads and dangerous file types.\n2 - Block malicious downloads, uncommon or unwanted downloads and dangerous file types.\n3 - Block all downloads.\n4 - Block malicious downloads. Recommended.\nSetting the policy means users can't bypass download security decisions.\n\nThere are many types of download warnings within Chrome, which roughly break down into these categories (learn more about Safe Browsing verdicts https://support.google.com/chrome/?p=ib_download_blocked):\n\n* Malicious, as flagged by the Safe Browsing server\n* Uncommon or unwanted, as flagged by the Safe Browsing server\n* A dangerous file type (e.g. all SWF downloads and many EXE downloads)\n\nSetting the policy blocks different subsets of these, depending on it's value:\n\n0: No special restrictions. Default.\n\n1: Blocks malicious files flagged by the Safe Browsing server AND Blocks all dangerous file types. Only recommended for OUs/browsers/users that have a high tolerance for False Positives.\n\n2: Blocks malicious files flagged by the Safe Browsing server AND Blocks uncommon or unwanted files flagged by the Safe Browsing server AND Blocks all dangerous file types. Only recommended for OUs/browsers/users that have a high tolerance for False Positives.\n\n3: Blocks all downloads. Not recommended, except for special use cases.\n\n4: Blocks malicious files flagged by the Safe Browsing server, does not block dangerous file types. Recommended.\n\nNote: These restrictions apply to downloads triggered from webpage content, as well as the Download link… menu option. They don't apply to the download of the currently displayed page or to saving as PDF from the printing options. Read more about Safe Browsing ( https://developers.google.com/safe-browsing ).";
EditBookmarksEnabled.pfm_title = "Enable or disable bookmark editing";
EditBookmarksEnabled.pfm_description = "Setting the policy to True or leaving it unset lets users add, remove, modify, or upload bookmarks.\n\nSetting the policy to False means users can't add, remove, modify or upload bookmarks. They can still use existing bookmarks.";
EnableAuthNegotiatePort.pfm_title = "Include non-standard port in Kerberos SPN";
EnableAuthNegotiatePort.pfm_description = "Setting the policy to Enabled and entering a nonstandard port (in other words, a port other than 80 or 443) includes it in the generated Kerberos SPN.\n\nSetting the policy to Disabled or leaving it unset means the generated Kerberos SPN won't include a port.";
EnableExperimentalPolicies.pfm_title = "Enables experimental policies";
EnableExperimentalPolicies.pfm_description = "Allows Google Chrome to load experimental policies.\n\nWARNING: Experimental policies are unsupported and subject to change or be removed without notice in future version of the browser!\n\nAn experimental policy may not be finished or still have known or unknown defects. It may be changed or even removed without any notification. By enabling experimental policies, you could lose browser data or compromise your security or privacy.\n\nIf a policy is not in the list and it's not officially released, its value will be ignored on Beta and Stable channel.\n\nIf a policy is in the list and it's not officially released, its value will be applied.\n\nThis policy has no effect on already released policies.";
EnableMediaRouter.pfm_title = "Enable Google Cast";
EnableMediaRouter.pfm_description = "Setting the policy to Enabled or leaving it unset turns on Google Cast, which users can launch from the app menu, page context menus, media controls on Cast-enabled websites, and (if shown) the Cast toolbar icon.\n\nSetting the policy to Disabled turns off Google Cast.";
EnableOnlineRevocationChecks.pfm_title = "Enable online OCSP/CRL checks";
EnableOnlineRevocationChecks.pfm_description = "Setting the policy to True means online OCSP/CRL checks are performed.\n\nSetting the policy to False or leaving it unset means Google Chrome won't perform online revocation checks in Google Chrome 19 and later.\n\nNote: OCSP/CRL checks provide no effective security benefit.";
EncryptedClientHelloEnabled.pfm_title = "Enable TLS Encrypted ClientHello";
EncryptedClientHelloEnabled.pfm_description = "Encrypted ClientHello (ECH) is an extension to TLS to encrypt sensitive fields of the ClientHello and improve privacy.\n\nIf this policy is not configured, or is set to enabled, Google Chrome will follow the default rollout process for ECH. If it is disabled, Google Chrome will not enable ECH.\n\nWhen the feature is enabled, Google Chrome may or may not use ECH depending on server support, availability of the HTTPS DNS record, or rollout status.\n\nECH is an evolving protocol, so Google Chrome's implementation is subject to change. As such, this policy is a temporary measure to control the initial experimental implementation. It will be replaced with final controls as the protocol finalizes.";
EnterpriseCustomLabel.pfm_title = "Set a custom enterprise label for a managed profile";
EnterpriseCustomLabel.pfm_description = "This policy controls a custom label used to identify managed profiles. For managed profiles, this label will be shown next to the avatar in the toolbar. The custom label will not be translated.\n\nWhen this policy is applied, any strings that surpass 16 characters will be truncated with a “...” Please refrain from using extended names.\n\nThis policy can only be set as a user policy.\n\nNote that this policy has no effect if the EnterpriseProfileBadgeToolbarSettings policy is set to hide_expanded_enterprise_toolbar_badge (value 1).";
EnterpriseHardwarePlatformAPIEnabled.pfm_title = "Enables managed extensions to use the Enterprise Hardware Platform API";
EnterpriseHardwarePlatformAPIEnabled.pfm_description = "Setting the policy to True lets extensions installed by enterprise policy use the Enterprise Hardware Platform API.\n\nSetting the policy to False or leaving it unset prevents extensions from using this API.\n\nNote: This policy also applies to component extensions, such as the Hangout Services extension.";
EnterpriseLogoUrl.pfm_title = "Enterprise Logo URL for a managed profile";
EnterpriseLogoUrl.pfm_description = "A URL to an image that will be used as an enterprise badge for a managed profile. The URL must point to an image.\n\nThis policy can only be set as a user policy.\n\nIt is recommended to use the favicon (example https://www.google.com/favicon.ico) or an icon no smaller than 48 x 48 px.";
EnterpriseProfileBadgeToolbarSettings.pfm_title = "Controls visibility of enterprise profile badge in the toolbar";
EnterpriseProfileBadgeToolbarSettings.pfm_description = "0 - Show expanded enterprise toolbar badge\n1 - Hide expanded enterprise toolbar badge\nFor work and school profiles, the toolbar will show a \"Work\" or \"School\" label by default next to the toolbar avatar. The label will only be shown if the signed in account is managed.\n\nSetting this policy to hide_expanded_enterprise_toolbar_badge (value 1) will hide the enterprise badge for a managed profile in the toolbar.\n\nLeaving this policy unset or setting it to show_expanded_enterprise_toolbar_badge (value 0) will show the enterprise badge.\n\nThe label is customizable via the EnterpriseCustomLabel policy.";
EnterpriseProfileCreationKeepBrowsingData.pfm_title = "Keep browsing data when creating enterprise profile by default";
EnterpriseProfileCreationKeepBrowsingData.pfm_description = "If this policy is Enabled, the option to keep any existing browsing data when creating an enterprise profile will be checked by default.\n\nIf this policy is unset or Disabled, the option to keep any existing browsing data when creating an enterprise profile will not be checked by default.\n\nRegardless of the value, the user will be able to decide whether or not to keep any existing browsing data when creating an enterprise profile.\n\nThis policy has no effect if the option to keep existing browsing data is not available; this happens if enterprise profile separation is strictly enforced, or if the data would be from an already managed profile.\n";
EnterpriseSearchAggregatorSettings.pfm_title = "Enterprise search aggregator settings";
EnterpriseSearchAggregatorSettings.pfm_description = "This policy allows administrators to set a designated enterprise search aggregator that will provide search recommendations and results within the address bar when triggered by a specific keyword. Users can initiate a search by typing the keyword specified in the shortcut field with or without the @ prefix (e.g. @work), followed by Space or Tab, in the address bar.\n\nThe following fields are required: name, shortcut, search_url, suggest_url.\n\nThe name field corresponds to the search engine name shown to the user in the address bar.\n\nThe shortcut field corresponds to the keyword that the user enters to trigger the search. The shortcut can include plain words and characters, but cannot include spaces or start with the @ symbol. Shortcuts must be unique.\n\nThe search_url field specifies the URL on which to search. Enter the web address for the search engine's results page, and use '{searchTerms}' in place of the query.\n\nThe suggest_url field specifies the URL that provides search suggestions. A POST request will be made and the user's query will be passed in the POST params under key 'query'.\n\nThe icon_url field specifies the URL to an image that will be used on the search suggestions. A default icon will be used when this field is not set. It's recommended to use a favicon (example https://www.google.com/favicon.ico). Supported image file formats: JPEG, PNG, and ICO.\n\nThe require_shortcut field specifies whether the address bar shortcut is required to see search recommendations. If this field is not set, the address bar shortcut is not required.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=EnterpriseSearchAggregatorSettings for more information about schema and formatting.";
ExemptDomainFileTypePairsFromFileTypeDownloadWarnings.pfm_title = "Disable download file type extension-based warnings for specified file types on domains";
ExemptDomainFileTypePairsFromFileTypeDownloadWarnings.pfm_description = "You can enable this policy to create a dictionary of file type extensions with a corresponding list of domains that will be exempted from file type extension-based download warnings. This lets enterprise administrators block file type extension-based download warnings for files that are associated with a listed domain. For example, if  the \"jnlp\" extension is associated with \"website1.com\", users would not see a warning when downloading \"jnlp\" files from \"website1.com\", but see a download warning when downloading \"jnlp\" files from \"website2.com\".\n\nFiles with file type extensions specified for domains identified by this policy will still be subject to non-file type extension-based security warnings such as mixed-content download warnings and Safe Browsing warnings.\n\nIf you disable this policy or don't configure it, file types that trigger extension-based download warnings will show warnings to the user.\n\nIf you enable this policy:\n\n* The URL pattern should be formatted according to https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.\n\n* The file type extension entered must be in lower-cased ASCII. The leading separator should not be included when listing the file type extension, so list \"jnlp\" should be used instead of \".jnlp\".\n\nExample:\n\nThe following example value would prevent file type extension-based download warnings on \"exe\" and \"jnlp\" extensions for *.example.com domains, and on \"swf\" extensions for all domains. It will show the user a file type extension-based download warning on any other domain for exe and jnlp files, but not for swf files.\n\n[\n{ \"file_extension\": \"jnlp\", \"domains\": [\"example.com\"] },\n{ \"file_extension\": \"exe\", \"domains\": [\"example.com\"] },\n{ \"file_extension\": \"swf\", \"domains\": [\"*\"] }\n]\n\nNote that while the preceding example shows the suppression of file type extension-based download warnings for \"swf\" files for all domains, applying suppression of such warnings for all domains for any dangerous file type extension is not recommended due to security concerns. It is shown in the example merely to demonstrate the ability to do so.\n\nIf this policy is enabled alongside DownloadRestrictions, then the exemptions to file type extension-based warnings specified by this policy take precedence over a DownloadRestrictions setting that would block dangerous file types. The exemptions specified by this policy only apply to the \"block dangerous file types\" behavior specified by values 1 and 2 of DownloadRestrictions.\n\nFor example, if this policy specifies an exemption for \"exe\" downloads from \"website1.com\", and DownloadRestrictions is set to block malicious downloads and dangerous file types (value 1), then \"exe\" downloads from \"website1.com\" will be exempt from file type extension-based blocking but will still be blocked if they are malicious.\n\nMore information about DownloadRestrictions can be found at https://chromeenterprise.google/policies/?policy=DownloadRestrictions.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=ExemptDomainFileTypePairsFromFileTypeDownloadWarnings for more information about schema and formatting.";
ExplicitlyAllowedNetworkPorts.pfm_title = "Explicitly allowed network ports";
ExplicitlyAllowedNetworkPorts.pfm_description = "554 - port 554 (can be unblocked until 2021/10/15)\n10080 - port 10080 (can be unblocked until 2022/04/01)\n6566 - port 6566 (can be unblocked until 2021/10/15)\n989 - port 989 (can be unblocked until 2022/02/01)\n990 - port 990 (can be unblocked until 2022/02/01)\nThere is a list of restricted ports built into Google Chrome. Connections to these ports will fail. This setting permits bypassing that list. The value is a comma-separated list of zero or more ports that outgoing connections will be permitted on.\n\nPorts are restricted to prevent Google Chrome being used as a vector to exploit various network vulnerabilities. Setting this policy may expose your network to attacks. This policy is intended as a temporary workaround for errors with code \"ERR_UNSAFE_PORT\" while migrating a service running on a blocked port to a standard port (ie. port 80 or 443).\n\nMalicious websites can easily detect that this policy is set, and for what ports, and use that information to target attacks.\n\nEach port here is labelled with a date that it can be unblocked until. After that date the port will be restricted regardless of this setting.\n\nLeaving the value empty or unset means that all restricted ports will be blocked. If there is a mixture of valid and invalid values, the valid ones will be applied.\n\nThis policy overrides the \"--explicitly-allowed-ports\" command-line option.";
ExtensibleEnterpriseSSOBlocklist.pfm_title = "Blocklist of identity providers that cannot use Extensible Enterprise SSO for the browser";
ExtensibleEnterpriseSSOBlocklist.pfm_description = "all - All identity providers\nmicrosoft - Microsoft® cloud identity provider\nDisable single sign-on using Extensible Enterprise SSO for the listed identity providers.\n\nBy adding the value 'all', all supported identity providers for SSO are disabled.\n\nBy adding the value 'microsoft', support for single sign-on for Microsoft® cloud identity provider in Chrome is disabled.\n\nBy leaving this policy unset, all identity providers that are supported by Google Chrome will be enabled.\n\nFor identity providers that are enabled in Google Chrome and configured on the device the administrator, users who sign into their once using that identity provider, on any application that supports Extensible Enterprise SSO, can be signed into web properties using that identity provider automatically. Information pertaining to the user's authencitation information is transmitted to the user's cloud identity provider for each authentication event.\n\nNotes:\n\nGoogle Chrome does not support Single Sign-on using Extensible Enterprise SSO in Incognito or Guest modes.\n\nGoogle Chrome only supports Single Sign-on using Extensible Enterprise SSO for the following identity providers: Microsoft.\n\nThis feature is available starting in macOS 10.15.";
ExtensionAllowedTypes.pfm_title = "Types of extensions/apps that are allowed to be installed";
ExtensionAllowedTypes.pfm_description = "extension - Extension\ntheme - Theme\nuser_script - User script\nhosted_app - Hosted app\nlegacy_packaged_app - Legacy packaged app\nplatform_app - Platform app\nSetting the policy controls which apps and extensions may be installed in Google Chrome, which hosts they can interact with, and limits runtime access.\n\nLeaving the policy unset results in no restrictions on the acceptable extension and app types.\n\nExtensions and apps which have a type that's not on the list won't be installed. Each value should be one of these strings:\n\n* \"extension\"\n\n* \"theme\"\n\n* \"user_script\"\n\n* \"hosted_app\"\n\n* \"legacy_packaged_app\"\n\n* \"platform_app\"\n\nSee the Google Chrome extensions documentation for more information on these types.\n\nVersions earlier than 75 that use multiple comma separated extension IDs aren't supported and are skipped. The rest of the policy applies.\n\nNote: This policy also affects extensions and apps to be force-installed using ExtensionInstallForcelist.";
ExtensionDeveloperModeSettings.pfm_title = "Control the availability of developer mode on extensions page";
ExtensionDeveloperModeSettings.pfm_description = "0 - Allow the usage of developer mode on extensions page\n1 - Do not allow the usage of developer mode on extensions page\nControl if users can turn on Developer Mode on chrome://extensions.\n\nIf the policy is not set, users can turn on developer mode on extension page unless DeveloperToolsAvailability policy is set to DeveloperToolsDisallowed (2).\nIf the policy is set to Allow (0), users can turn on developer mode on extensions page.\nIf the policy is set to Disallow (1), users can not turn on developer mode on extensions page.\n\nIf this policy is set, DeveloperToolsAvailability can no longer control extensions developer mode.";
ExtensionExtendedBackgroundLifetimeForPortConnectionsToUrls.pfm_title = "Configure a list of origins that grant extended background lifetime to the connecting extensions.";
ExtensionExtendedBackgroundLifetimeForPortConnectionsToUrls.pfm_description = "Extensions that connect to one of these origins will be be kept running as long as the port is connected.\n\nIf unset, the policy's default values will be used. These are app origins that offer SDKs that are known to not offer the possibility to restart a closed connection to a previous state:\n- Smart Card Connector\n- Citrix Receiver (stable, beta, back-up)\n- VMware Horizon (stable, beta)\n\nIf set, the default value list is extended with the newly configured values. Both defaults and the policy-provided entries will grant the exception to the connecting extensions, as long as the port is connected.";
ExtensionInstallAllowlist.pfm_title = "Extension IDs to exempt from the blocklist";
ExtensionInstallAllowlist.pfm_description = "Setting the policy specifies which extensions are not subject to the blocklist.\n\nA blocklist value of * means all extensions are blocked and users can only install extensions listed in the allow list.\n\nBy default, all extensions are allowed. But, if you prohibited extensions by policy, use the list of allowed extensions to change that policy.";
ExtensionInstallBlocklist.pfm_title = "Extension IDs the user should be prevented from installing (or * for all)";
ExtensionInstallBlocklist.pfm_description = "Allows you to specify which extensions the users can NOT install. Extensions already installed will be disabled if blocked, without a way for the user to enable them. Once an extension disabled due to the blocklist is removed from it, it will automatically get re-enabled.\n\nA blocklist value of '*' means all extensions are blocked unless they are explicitly listed in the allowlist.\n\nIf this policy is left not set the user can install any extension in Google Chrome.";
ExtensionInstallForcelist.pfm_title = "Extension/App IDs and update URLs to be silently installed";
ExtensionInstallForcelist.pfm_description = "Setting the policy specifies a list of apps and extensions that install silently, without user interaction, and which users can't uninstall or turn off through the Google Chrome interface. Permissions are granted implicitly, including for the enterprise.deviceAttributes and enterprise.platformKeys extension APIs. (These 2 APIs aren't available to apps and extensions that aren't force-installed.)\n\nAlthough Google Chrome aims to prevent users from uninstalling these extensions, some operating systems make it impossible for Google Chrome to defend robustly against extensions being modified externally, so this prevention is best efforts.\n\nLeaving the policy unset means no apps or extensions are autoinstalled, and users can uninstall any app or extension in Google Chrome.\n\nThis policy supersedes ExtensionInstallBlocklist policy. If a previously force-installed app or extension is removed from this list, Google Chrome automatically uninstalls it.\n\nThe source code of any extension may be altered by users through developer tools, potentially rendering the extension dysfunctional. If this is a concern, set the DeveloperToolsDisabled policy.\n\nEach list item of the policy is a string that contains an extension ID and, optionally, an update URL separated by a semicolon (;). The extension ID is the 32-letter string found, for example, on chrome://extensions when in Developer mode. If specified, the update URL should point to an Update Manifest XML document ( https://developer.chrome.com/extensions/autoupdate ). The update URL should use one of the following schemes: http, https or file. By default, the Chrome Web Store's update URL is used. The update URL set in this policy is only used for the initial installation; subsequent updates of the extension use the update URL in the extension's manifest. The update url for subsequent updates can be overridden using the ExtensionSettings policy, see http://support.google.com/chrome/a?p=Configure_ExtensionSettings_policy.\n\nOn Microsoft® Windows® instances, apps and extensions from outside the Chrome Web Store can only be forced installed if the instance is joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS instances, apps and extensions from outside the Chrome Web Store can only be force installed if the instance is managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\n\nNote: This policy doesn't apply to Incognito mode. Read about hosting extensions ( https://developer.chrome.com/extensions/hosting ).";
ExtensionInstallSources.pfm_title = "URL patterns to allow extension, app, and user script installs from";
ExtensionInstallSources.pfm_description = "Setting the policy specifies which URLs may install extensions, apps, and themes. Before Google Chrome 21, users could click on a link to a *.crx file, and Google Chrome would offer to install the file after a few warnings. Afterwards, such files must be downloaded and dragged to the Google Chrome settings page. This setting allows specific URLs to have the old, easier installation flow.\n\nEach item in this list is an extension-style match pattern (see https://developer.chrome.com/extensions/match_patterns). Users can easily install items from any URL that matches an item in this list. Both the location of the *.crx file and the page where the download is started from (the referrer) must be allowed by these patterns.\n\nExtensionInstallBlocklist takes precedence over this policy. That is, an extension on the blocklist won't be installed, even if it happens from a site on this list.";
ExtensionInstallTypeBlocklist.pfm_title = "Blocklist for install types of extensions";
ExtensionInstallTypeBlocklist.pfm_description = "command_line - Blocks extensions from being loaded from command line\nThe blocklist controls which extensions install types are disallowed.\n\nSetting \"command_line\" will block extension from being loaded from\ncommand line.";
ExtensionManifestV2Availability.pfm_title = "Control Manifest v2 extension availability";
ExtensionManifestV2Availability.pfm_description = "0 - Default browser behavior\n1 - Manifest v2 is disabled\n2 - Manifest v2 is enabled\n3 - Manifest v2 is enabled for forced extensions only\nControl if Manifest v2 extensions can be used by browser.\n\nManifest v2 extensions support will be deprecated and all extensions need\nto be migrated to v3 in the future. More information and timeline of the\nmigration can be found at https://developer.chrome.com/docs/extensions/mv3/mv2-sunset/.\n\nIf the policy is set to Default (0) or not set, v2 extensions loading are decided by browser, following the timeline above.\nIf the policy is set to Disable (1), v2 extensions installation are blocked, existing ones are disabled. The option is going to be treated the same as if the policy is not set after v2 support is turned off by default.\nIf the policy is set to Enable (2), v2 extensions are allowed. The option is going to be treated the same as if the policy is not set before v2 support is turned off by default.\nIf the policy is set to EnableForForcedExtensions (3), force installed v2 extensions are allowed. This includes extensions that are listed by ExtensionInstallForcelist or ExtensionSettings with installation_mode \"force_installed\" or \"normal_installed\". All other v2 extensions are disabled. The option is always available regardless of the migration state.\n\n\nExtensions availability are still controlled by other policies.";
ExtensionSettings.pfm_title = "Extension management settings";
ExtensionSettings.pfm_description = "Setting the policy controls extension management settings for Google Chrome, including any controlled by existing extension-related policies. The policy supersedes any legacy policies that might be set.\n\nThis policy maps an extension ID or an update URL to its specific setting only. A default configuration can be set for the special ID \"*\", which applies to all extensions without a custom configuration in this policy. With an update URL, configuration applies to extensions with the exact update URL stated in the extension manifest ( http://support.google.com/chrome/a?p=Configure_ExtensionSettings_policy ). If the 'override_update_url' flag is set to true, the extension is installed and updated using the \"update\" URL specified in the ExtensionInstallForcelist policy or in 'update_url' field in this policy. The flag 'override_update_url' is ignored if the 'update_url' is a Chrome Web Store url.\n\nOn Microsoft® Windows® instances, apps and extensions from outside the Chrome Web Store can only be forced installed if the instance is joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS instances, apps and extensions from outside the Chrome Web Store can only be force installed if the instance is managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=ExtensionSettings for more information about schema and formatting.";
ExtensionUnpublishedAvailability.pfm_title = "Control availability of extensions unpublished on the Chrome Web Store.";
ExtensionUnpublishedAvailability.pfm_description = "0 - Allow unpublished extensions\n1 - Disable unpublished extensions\nIf this policy is enabled, extensions that are unpublished on the Chrome Web\nStore will be disabled in Google Chrome.\nThis policy only applies to extensions that are installed and updated from the\nChrome Web Store.\n\nOff-store extensions such as unpacked extensions installed using developer\nmode and extensions installed using the command-line switch are ignored.\nForce-installed extensions that are self-hosted are ignored. All\nversion-pinned extensions are also ignored.\n\nIf the policy is set to AllowUnpublished (0) or not set, extensions that are unpublished on the Chrome Web Store are allowed.\nIf the policy is set to DisableUnpublished (1), extensions that are unpublished on the Chrome Web Store are disabled.";
ExternalProtocolDialogShowAlwaysOpenCheckbox.pfm_title = "Show an \"Always open\" checkbox in external protocol dialog.";
ExternalProtocolDialogShowAlwaysOpenCheckbox.pfm_description = "This policy controls whether or not the \"Always open\" checkbox is shown on external protocol launch confirmation prompts.\n\n     If this policy is set to True or not set, when an external protocol confirmation is shown, the user can select \"Always allow\" to skip all future confirmation prompts for the protocol on this site.\n\n     If this policy is set to False, the \"Always allow\" checkbox is not displayed and the user will be prompted each time an external protocol is invoked.";
FeedbackSurveysEnabled.pfm_title = "Specifies whether in-product Google Chrome surveys are shown to users.";
FeedbackSurveysEnabled.pfm_description = "Google Chrome in-product surveys collect user feedback for the browser. Survey responses are not associated with user accounts.\nWhen this policy is Enabled or not set, in-product surveys may be shown to users.\nWhen this policy is Disabled, in-product surveys are not shown to users.\n\nThis policy has no effect if MetricsReportingEnabled is set to Disabled, which disables in-product surveys as well.";
FetchKeepaliveDurationSecondsOnShutdown.pfm_title = "Fetch keepalive duration on Shutdown";
FetchKeepaliveDurationSecondsOnShutdown.pfm_description = "Controls the duration (in seconds) allowed for keepalive requests on browser shutdown.\n\nWhen specified, browser shutdown can be blocked up to the specified seconds,\nto process keepalive (https://fetch.spec.whatwg.org/#request-keepalive-flag) requests.\n\nThe default value (0) means this feature is disabled.";
FileOrDirectoryPickerWithoutGestureAllowedForOrigins.pfm_title = "Allow file or directory picker APIs to be called without prior user gesture";
FileOrDirectoryPickerWithoutGestureAllowedForOrigins.pfm_description = "For security reasons, the\nshowOpenFilePicker(),\nshowSaveFilePicker() and\nshowDirectoryPicker() web APIs\nrequire a prior user gesture (\"transient activation\") to be called or will\notherwise fail.\n\nWith this policy set, admins can specify origins on which these APIs can be\ncalled without prior user gesture.\n\nFor detailed information on valid url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is\nnot an accepted value for this policy.\n\nIf this policy is unset, all origins will require a prior user gesture to call\nthese APIs.";
FileSystemReadAskForUrls.pfm_title = "Allow read access via the File System API on these sites";
FileSystemReadAskForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can ask users to grant them read access to files or directories in the host operating system's file system via the File System API.\n\nLeaving the policy unset means DefaultFileSystemReadGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nURL patterns must not conflict with FileSystemReadBlockedForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
FileSystemReadBlockedForUrls.pfm_title = "Block read access via the File System API on these sites";
FileSystemReadBlockedForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can't ask users to grant them read access to files or directories in the host operating system's file system via the File System API.\n\nLeaving the policy unset means DefaultFileSystemReadGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nURL patterns can't conflict with FileSystemReadAskForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
FileSystemWriteAskForUrls.pfm_title = "Allow write access to files and directories on these sites";
FileSystemWriteAskForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can ask users to grant them write access to files or directories in the host operating system's file system.\n\nLeaving the policy unset means DefaultFileSystemWriteGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nURL patterns must not conflict with FileSystemWriteBlockedForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
FileSystemWriteBlockedForUrls.pfm_title = "Block write access to files and directories on these sites";
FileSystemWriteBlockedForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can't ask users to grant them write access to files or directories in the host operating system's file system.\n\nLeaving the policy unset means DefaultFileSystemWriteGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nURL patterns can't conflict with FileSystemWriteAskForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
FirstPartySetsEnabled.pfm_title = "Enable First-Party Sets.";
FirstPartySetsEnabled.pfm_description = "This policy is provided as a way to opt-out of the First-Party Sets feature.\n\nWhen this policy is unset or set to Enabled, the First-Party Sets feature is enabled.\n\nWhen this policy is set to Disabled, the First-Party Sets feature is disabled.\n\nIt controls whether Chrome supports First-Party Sets related integrations.\n\nThis is the equivalent of the RelatedWebsiteSetsEnabled policy.\nEither policy may be used, but this one will be deprecated soon so the RelatedWebsiteSetsEnabled policy is preferred.\nThey both have the same effect on the browser's behavior.";
FirstPartySetsOverrides.pfm_title = "Override First-Party Sets.";
FirstPartySetsOverrides.pfm_description = "This policy provides a way to override the list of sets the browser uses for First-Party Sets features.\n\nEach set in the browser's list of First-Party Sets must meet the requirements of a First-Party Set.\nA First-Party Set must contain a primary site and one or more member sites.\nA set can also contain a list of service sites that it owns, as well as a map from a site to all of its ccTLD variants.\nSee https://github.com/WICG/first-party-sets for more information on First-Party Sets are used by Google Chrome.\n\nAll sites in a First-Party Set must be a registrable domain served over HTTPS. Each site in a First-Party Set must also be unique,\nmeaning a site cannot be listed more than once in a First-Party Set.\n\nWhen this policy is given an empty dictionary, the browser uses the public list of First-Party Sets.\n\nFor all sites in a First-Party Set from the replacements list, if a site is also present\non a First-Party Set in the browser's list, then that site will be removed from the browser's First-Party Set.\nAfter this, the policy's First-Party Set will be added to the browser's list of First-Party Sets.\n\nFor all sites in a First-Party Set from the additions list, if a site is also present\non a First-Party Set in the browser's list, then the browser's First-Party Set will be updated so that the\nnew First-Party Set can be added to the browser's list. After the browser's list has been updated,\nthe policy's First-Party Set will be added to the browser's list of First-Party Sets.\n\nThe browser's list of First-Party Sets requires that for all sites in its list, no site is in\nmore than one set. This is also required for both the replacements list\nand the additions list. Similarly, a site cannot be in both the\nreplacements list and the additions list.\n\nWildcards (*) are not supported as a policy value, nor within any First-Party Set in these lists.\n\nAll sets provided by the policy must be valid First-Party Sets, if they aren't then an\nappropriate error will be outputted.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\n\nThis is the equivalent of the RelatedWebsiteSetsOverrides policy.\nEither policy may be used, but this one will be deprecated soon so the RelatedWebsiteSetsOverrides policy is preferred.\nThey both have the same effect on the browser's behavior.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=FirstPartySetsOverrides for more information about schema and formatting.";
ForceEphemeralProfiles.pfm_title = "Ephemeral profile";
ForceEphemeralProfiles.pfm_description = "If set to enabled this policy forces the profile to be switched to ephemeral mode. If this policy is specified as an OS policy (e.g. GPO on Windows) it will apply to every profile on the system; if the policy is set as a Cloud policy it will apply only to a profile signed in with a managed account.\n\nIn this mode the profile data is persisted on disk only for the length of the user session. Features like browser history, extensions and their data, web data like cookies and web databases are not preserved after the browser is closed. However this does not prevent the user from downloading any data to disk manually, save pages or print them.\n\nIf the user has enabled sync all this data is preserved in their sync profile just like with regular profiles. Incognito mode is also available if not explicitly disabled by policy.\n\nIf the policy is set to disabled or left not set signing in leads to regular profiles.";
ForceGoogleSafeSearch.pfm_title = "Force Google SafeSearch";
ForceGoogleSafeSearch.pfm_description = "Setting the policy to Enabled means SafeSearch in Google Search is always active, and users can't change this setting.\n\nSetting the policy to Disabled or leaving it unset means SafeSearch in Google Search is not enforced.";
ForcePermissionPolicyUnloadDefaultEnabled.pfm_title = "Controls whether unload event handlers can be disabled.";
ForcePermissionPolicyUnloadDefaultEnabled.pfm_description = "unload event handlers are being deprecated. Whether they fire depends on the unload Permissions-Policy. Currently, they are allowed by policy by default. In the future they will gradually move to being disallowed by default and sites must explicitly enable them using Permissions-Policy headers. This enterprise policy can be used to opt out of this gradual deprecation by forcing the default to remain as enabled.\n\nPages may depend on unload event handlers to save data or signal the end of a user session to the server. This is not recommended as it is unreliable and impacts performance by blocking use of BackForwardCache. Recommended alternatives exist, however the unload event has been used for a long time. Some applications may still rely on them.\n\nIf this policy is set to false or not set, then unload events handlers will be gradually deprecated in-line with the deprecation rollout and sites which do not set Permissions-Policy header will stop firing `unload` events.\n\nIf this policy is set to true then unload event handlers will continue to work by default.\n\nNOTE: This policy had an incorrectly documented default of `true` in M117. The unload event did and will not change in M117, so this policy has no effect in that version.\n";
ForceYouTubeRestrict.pfm_title = "Force minimum YouTube Restricted Mode";
ForceYouTubeRestrict.pfm_description = "0 - Do not enforce Restricted Mode on YouTube\n1 - Enforce at least Moderate Restricted Mode on YouTube\n2 - Enforce Strict Restricted Mode for YouTube\nSetting the policy enforces a minimum Restricted mode on YouTube and prevents users from picking a less restricted mode. If you set it to:\n\n* Strict, Strict Restricted mode on YouTube is always active.\n\n* Moderate, the user may only pick Moderate Restricted mode and Strict Restricted mode on YouTube, but can't turn off Restricted mode.\n\n* Off or if no value is set, Restricted mode on YouTube isn't enforced by Chrome. External policies such as YouTube policies might still enforce Restricted mode.";
ForcedLanguages.pfm_title = "Configure the content and order of preferred languages";
ForcedLanguages.pfm_description = "This policy allows admins to configure the order of the preferred languages in Google Chrome's settings.\n\nThe order of the list will appear in the same order under the \"Order languages based on your preference\" section in chrome://settings/languages. Users won't be able to remove or reorder languages set by the policy, but will be able to add languages underneath those set by the policy. Users will also have full control over the browser's UI language and translation/spell check settings, unless enforced by other policies.\n\nLeaving the policy unset lets users manipulate the entire list of preferred languages.";
GeminiSettings.pfm_title = "Settings for Gemini integration";
GeminiSettings.pfm_description = "0 - Allow Gemini integrations.\n1 - Do not allow Gemini integrations.\nThis setting allows Gemini app integrations.\n\n0/unset = Gemini integration will be available for users.\n\n1 = Gemini integration will not be available for users.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information, please check the Enterprise Release Notes (https://support.google.com/chrome/a/answer/7679408).";
GenAILocalFoundationalModelSettings.pfm_title = "Settings for GenAI local foundational model";
GenAILocalFoundationalModelSettings.pfm_description = "0 - Downloads model automatically\n1 - Do not download model\nConfigure how Google Chrome downloads the foundational GenAI model and uses for inference locally.\n\nWhen the policy is set to Allowed (0) or not set, the model is downloaded automatically, and used for inference.\n\nWhen the policy is set to Disabled (1), the model will not be downloaded.\n\nModel downloading can also be disabled by ComponentUpdatesEnabled.";
GloballyScopeHTTPAuthCacheEnabled.pfm_title = "Enable globally scoped HTTP auth cache";
GloballyScopeHTTPAuthCacheEnabled.pfm_description = "This policy configures a single global per profile cache with HTTP server authentication credentials.\n\nIf this policy is unset or disabled, the browser will use the default behavior of cross-site auth, which as of version 80, will be to scope HTTP server authentication credentials by top-level site, so if two sites use resources from the same authenticating domain, credentials will need to be provided independently in the context of both sites. Cached proxy credentials will be reused across sites.\n\nIf the policy is enabled, HTTP auth credentials entered in the context of one site will automatically be used in the context of another.\n\nEnabling this policy leaves sites open to some types of cross-site attacks, and allows users to be tracked across sites even without cookies by adding entries to the HTTP auth cache using credentials embedded in URLs.\n\nThis policy is intended to give enterprises depending on the legacy behavior a chance to update their login procedures, and will be removed in the future.";
GoogleSearchSidePanelEnabled.pfm_title = "Enable Google Search Side Panel";
GoogleSearchSidePanelEnabled.pfm_description = "If set to Enabled or not set, Google Search Side Panel is allowed on all web pages.\n\nIf set to Disabled, Google Search Side Panel is not available on any webpage.\n\nGenAI capabilities that are part of this feature are not available for Educational or Enterprise accounts.";
HSTSPolicyBypassList.pfm_title = "List of names that will bypass the HSTS policy check";
HSTSPolicyBypassList.pfm_description = "Setting the policy specifies a list of hostnames that bypass preloaded HSTS upgrades from http to https.\n\nOnly single-label hostnames are allowed in this policy, and this policy only applies to \"static\" HSTS-preloaded entries (for instance, \"app\", \"new\", \"search\", \"play\"). This policy does not prevent HSTS upgrades for servers that have \"dynamically\" requested HSTS upgrades using a Strict-Transport-Security response header.\n\nSupplied hostnames must be canonicalized: Any IDNs must be converted to their A-label format, and all ASCII letters must be lowercase. This policy only applies to the specific single-label hostnames specified, not to subdomains of those names.";
HappyEyeballsV3Enabled.pfm_title = "Use the Happy Eyeballs V3 algorithm";
HappyEyeballsV3Enabled.pfm_description = "This feature enables the Happy Eyeballs V3 algorithm to make connection attempts. See https://datatracker.ietf.org/doc/draft-pauly-happy-happyeyeballs-v3 for details.\n\nSetting the policy to Enabled means Google Chrome will use the Happy Eyeballs V3 algorithm for connection attempts.\n\nSetting the policy to Disabled turns off the Happy Eyeballs V3 algorithm.\n\nNot setting the policy, Google Chrome will turn on or off the Happy Eyeballs V3 algorithm based on chrome://flags/#happy-eyeballs-v3.\n\nThis policy supports dynamic refresh.\n\nThis policy is a temporary measure and will be removed in future versions of Google Chrome.";
HardwareAccelerationModeEnabled.pfm_title = "Use graphics acceleration when available";
HardwareAccelerationModeEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset turns on graphics acceleration, if available.\n\nSetting the policy to Disabled turns off graphics acceleration.";
HeadlessMode.pfm_title = "Control use of the Headless Mode";
HeadlessMode.pfm_description = "1 - Allow use of the Headless Mode\n2 - Do not allow use of the Headless Mode\nSetting this policy to Enabled or leaving the policy unset allows use of the headless mode. Setting this policy to Disabled denies use of the headless mode.";
HelpMeWriteSettings.pfm_title = "Settings for Help Me Write";
HelpMeWriteSettings.pfm_description = "0 - Allow Help Me Write and improve AI models.\n1 - Allow Help Me Write without improving AI models.\n2 - Do not allow Help Me Write.\nHelp Me Write is an AI-based writing assistant for short-form content on the web. Suggested content is based on prompts entered by the user and the content of the web page.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. It may also be reviewed by humans to improve AI models. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
HideWebStoreIcon.pfm_title = "Hide the web store from the New Tab Page and app launcher";
HideWebStoreIcon.pfm_description = "Hide the Chrome Web Store app and footer link from the New Tab Page and Google ChromeOS app launcher.\n\nWhen this policy is set to true, the icons are hidden.\n\nWhen this policy is set to false or is not configured, the icons are visible.";
HighEfficiencyModeEnabled.pfm_title = "Enable High Efficiency Mode";
HighEfficiencyModeEnabled.pfm_description = "This policy enables or disables the High Efficiency Mode setting. This setting makes it so that tabs are discarded after some period of time in the background to reclaim memory.\nIf this policy is unset, the end user can control this setting in chrome://settings/performance.\n";
HistoryClustersVisible.pfm_title = "Show a view of Chrome history with groups of pages";
HistoryClustersVisible.pfm_description = "This policy controls the visibility of the Chrome history page organized into groups of pages.\n\nIf the policy is set to Enabled, a Chrome history page organized into groups will be visible at chrome://history/grouped.\n\nIf the policy is set to Disabled, a Chrome history page organized into groups will not be visible at chrome://history/grouped.\n\nIf the policy is left unset, a Chrome history page organized into groups will be visible at chrome://history/grouped by default.\n\nPlease note, if ComponentUpdatesEnabled policy is set to Disabled, but HistoryClustersVisible is set to Enabled or unset, a Chrome history page organized into groups will still be available at chrome://history/grouped, but may be less relevant to the user.\n";
HistorySearchSettings.pfm_title = "Settings for AI-powered History Search";
HistorySearchSettings.pfm_description = "0 - Allow AI History Search and improve AI models.\n1 - Allow AI History Search without improving AI models.\n2 - Do not allow AI History Search.\nAI History Search is a feature that allows users to search their browsing history and receive generated answers based on page contents and not just the page title and URL.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. It may also be reviewed by humans to improve AI models. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
HomepageIsNewTabPage.pfm_title = "Use New Tab Page as homepage";
HomepageIsNewTabPage.pfm_description = "Setting the policy to Enabled makes the New Tab page the user's homepage, ignoring any homepage URL location. Setting the policy to Disabled means that their homepage is never the New Tab page, unless the user's homepage URL is set to chrome://newtab.\n\nIf you set the policy, users can't change their homepage type in Google Chrome. If not set, the user decides whether or not the New Tab page is their homepage.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
HomepageLocation.pfm_title = "Home page URL";
HomepageLocation.pfm_description = "Setting the policy sets the default homepage URL in Google Chrome. You open the homepage using the Home button. On desktop, the RestoreOnStartup policies control the pages that open on startup.\n\nIf the homepage is set to the New Tab Page, by the user or HomepageIsNewTabPage, this policy has no effect.\n\nThe URL needs a standard scheme, such as http://example.com or https://example.com. When this policy is set, users can't change their homepage URL in Google Chrome.\n\nLeaving both HomepageLocation and HomepageIsNewTabPage unset lets users choose their homepage.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
HttpAllowlist.pfm_title = "HTTP Allowlist";
HttpAllowlist.pfm_description = "Setting the policy specifies a list of hostnames or hostname patterns (such as\n'[*.]example.com') that will not be upgraded to HTTPS and will not show an\nerror interstitial if HTTPS-First Mode is enabled. Organizations can use this\npolicy to maintain access to servers that do not support HTTPS, without\nneeding to disable HTTPS Upgrades and/or HTTPS-First Mode.\n\nSupplied hostnames must be canonicalized: Any IDNs must be converted to their\nA-label format, and all ASCII letters must be lowercase.\n\nBlanket host wildcards (i.e., \"*\" or \"[*]\") are not allowed. Instead,\nHTTPS-First Mode and HTTPS Upgrades should be explicitly disabled via their\nspecific policies.\n\nNote: This policy does not apply to HSTS upgrades.";
HttpsOnlyMode.pfm_title = "Allow HTTPS-Only Mode to be enabled";
HttpsOnlyMode.pfm_description = "allowed - Do not restrict users' HTTPS-Only Mode setting\ndisallowed - Do not allow users to enable any HTTPS-Only Mode\nforce_enabled - Force enable HTTPS-Only Mode in Strict mode\nforce_balanced_enabled - Force enable HTTPS-Only Mode in Balanced Mode\nThis policy controls whether users can enable HTTPS-Only Mode (Always Use Secure Connections) in Settings. HTTPS-Only Mode upgrades all navigations to HTTPS.\nIf this setting is not set or set to allowed, users will be allowed to enable HTTPS-Only Mode.\nIf this setting is set to disallowed, users will not be allowed to enable HTTPS-Only Mode.\nIf this setting is set to force_enabled, HTTPS-Only Mode will be enabled in Strict mode and users will not be able to disable it.\nIf this setting is set to force_balanced_enabled, HTTPS-Only Mode will be enabled in Balanced mode and users will not be able to disable it.\nforce_enabled is supported from M112 onwards, force_balanced_enabled is supported from M129 onwards.\nIf you set this policy to a value that is not supported by the version of Chrome that receives the policy, Chrome will default to the allowed setting.\n\nThe separate HttpAllowlist policy\ncan be used to exempt specific hostnames or hostname patterns from being\nupgraded to HTTPS by this feature.";
HttpsUpgradesEnabled.pfm_title = "Enable automatic HTTPS upgrades";
HttpsUpgradesEnabled.pfm_description = "Google Chrome attempts to upgrade some\nnavigations from HTTP to HTTPS, when possible. This policy can be used to\ndisable this behavior. If set to \"true\" or left unset, this feature will be\nenabled by default.\n\nThe separate HttpAllowlist policy\ncan be used to exempt specific hostnames or hostname patterns from being\nupgraded to HTTPS by this feature.\n\nSee also the HttpsOnlyMode policy.";
IPv6ReachabilityOverrideEnabled.pfm_title = "Enable IPv6 reachability check override";
IPv6ReachabilityOverrideEnabled.pfm_description = "Setting the policy to true overrides the IPv6 reachability check. This means that the\nsystem will always query AAAA records when resolving host names. It applies to\nall users and interfaces on the device.\n\nSetting the policy to false or leaving it unset does not overrides the IPv6 reachability check.\nThe system only queries AAAA records when it is reachable to a global IPv6 host.";
IdleTimeout.pfm_title = "Delay before running idle actions";
IdleTimeout.pfm_description = "Triggers an action when the computer is idle.\n\nIf this policy is set, it specifies the length of time without user input (in minutes) before the browser runs actions configured via the IdleTimeoutActions policy.\n\nIf this policy is not set, no action will be ran.\n\nThe minimum threshold is 1 minute.\n\n\"User input\" is defined by Operating System APIs, and includes things like moving the mouse or typing on the keyboard.";
IdleTimeoutActions.pfm_title = "Actions to run when the computer is idle";
IdleTimeoutActions.pfm_description = "close_browsers - Close Browsers\nshow_profile_picker - Show Profile Picker\nclear_browsing_history - Clear Browsing History\nclear_download_history - Clear Download History\nclear_cookies_and_other_site_data - Clear Cookies and Other Site Data\nclear_cached_images_and_files - Clear Cached Images and Files\nclear_password_signin - Clear Password Signin\nclear_autofill - Clear Autofill\nclear_site_settings - Clear Site Settings\nclear_hosted_app_data - Clear Hosted App Data\nreload_pages - Reload Pages\nsign_out - Sign Out\nclose_tabs - Close Tabs\nList of actions to run when the timeout from the IdleTimeout policy is reached.\n\nWarning: Setting this policy can impact and permanently remove local personal data. It is recommended to test your settings before deploying to prevent accidental deletion of personal data.\n\nIf the IdleTimeout policy is unset, this policy has no effect.\n\nWhen the timeout from the IdleTimeout policy is reached, the browser runs the actions configured in this policy.\n\nIf this policy is empty or left unset, the IdleTimeout policy has no effect.\n\nSupported actions are:\n\n'close_browsers': close all browser windows and PWAs for this profile. Not supported on Android and iOS.\n\n'close_tabs': close all open tabs in open windows. Only supported on iOS.\n\n'show_profile_picker': show the Profile Picker window. Not supported on Android and iOS.\n\n'sign_out': Signs out the current signed in user. Only supported on iOS.\n\n'clear_browsing_history', 'clear_download_history', 'clear_cookies_and_other_site_data', 'clear_cached_images_and_files', 'clear_password_signing', 'clear_autofill', 'clear_site_settings', 'clear_hosted_app_data': clear the corresponding browsing data. See the ClearBrowsingDataOnExitList policy for more details. The types supported on iOS are 'clear_browsing_history', 'clear_cookies_and_other_site_data', 'clear_cached_images_and_files', 'clear_password_signing', and 'clear_autofill'\n\n'reload_pages': reload all webpages. For some pages, the user may be prompted for confirmation first. Not supported on iOS.\n\nSetting 'clear_browsing_history', 'clear_password_signing', 'clear_autofill', and 'clear_site_settings' will disable sync for the respective data types if neither `Chrome Sync` is disabled by setting the SyncDisabled policy nor BrowserSignin is disabled.";
ImagesAllowedForUrls.pfm_title = "Allow images on these sites";
ImagesAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify sites that may display images.\n\nLeaving the policy unset means DefaultImagesSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.\n\nNote that previously this policy was erroneously enabled on Android, but this functionality has never been fully supported on Android.";
ImagesBlockedForUrls.pfm_title = "Block images on these sites";
ImagesBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify sites that can't display images.\n\nLeaving the policy unset means DefaultImagesSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\n For detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.\n\n Note that previously this policy was erroneously enabled on Android, but this functionality has never been fully supported on Android.";
ImportAutofillFormData.pfm_title = "Import autofill form data from default browser on first run";
ImportAutofillFormData.pfm_description = "Setting the policy to Enabled imports autofill form data from the previous default browser on first run. Setting the policy to Disabled or leaving it unset means no autofill form data is imported on first run.\n\nUsers can trigger an import dialog and the autofill form data checkbox will be checked or unchecked to match this policy's value.";
ImportBookmarks.pfm_title = "Import bookmarks from default browser on first run";
ImportBookmarks.pfm_description = "Setting the policy to Enabled imports bookmarks from the previous default browser on first run. Setting the policy to Disabled or leaving it unset means no bookmarks are imported on first run.\n\nUsers can trigger an import dialog and the bookmarks checkbox will be checked or unchecked to match this policy's value.";
ImportHistory.pfm_title = "Import browsing history from default browser on first run";
ImportHistory.pfm_description = "Setting the policy to Enabled imports browsing history from the previous default browser on first run. Setting the policy to Disabled or leaving it unset means no browsing history is imported on first run.\n\nUsers can trigger an import dialog and the browsing history checkbox will be checked or unchecked to match this policy's value.";
ImportHomepage.pfm_title = "Import of homepage from default browser on first run";
ImportHomepage.pfm_description = "Setting the policy to Enabled imports the homepage from the previous default browser on first run. Setting the policy to Disabled or leaving it unset means the homepage isn't imported on first run.\n\nUsers can trigger an import dialog and the homepage checkbox will be checked or unchecked to match this policy's value.";
ImportSavedPasswords.pfm_title = "Import saved passwords from default browser on first run";
ImportSavedPasswords.pfm_description = "This policy controls only the first run import behavior after installation. It enables more seamless transition to Google Chrome in environments where a different browser was extensively used prior to installing the browser. This policy does not affect password manager capabilities for Google accounts.\n\nSetting the policy to Enabled imports saved passwords from the previous default browser on first run and manual importing from the settings page is also possible.\nSetting the policy to Disabled means no saved passwords are imported on first run and manual importing from the Settings page is blocked.\nLeaving the policy unset means no saved passwords are imported on first run but the user can choose to do that from the settings page.";
ImportSearchEngine.pfm_title = "Import search engines from default browser on first run";
ImportSearchEngine.pfm_description = "Setting the policy to Enabled imports the default search engine from the previous default browser on first run. Setting the policy to Disabled or leaving it unset means the default search engine isn't imported on first run.\n\nUsers can trigger an import dialog and the default search engine checkbox will be checked or unchecked to match this policy's value.";
IncognitoModeAvailability.pfm_title = "Incognito mode availability";
IncognitoModeAvailability.pfm_description = "0 - Incognito mode available\n1 - Incognito mode disabled\n2 - Incognito mode forced\nSpecifies whether the user may open pages in Incognito mode in Google Chrome.\n\nIf 'Enabled' is selected or the policy is left unset, pages may be opened in Incognito mode.\n\nIf 'Disabled' is selected, pages may not be opened in Incognito mode.\n\nIf 'Forced' is selected, pages may be opened ONLY in Incognito mode. Note that 'Forced' does not work for Android-on-Chrome\n\nNote: On iOS, if the policy is changed during a session, it will only take effect on relaunch.";
InsecureContentAllowedForUrls.pfm_title = "Allow insecure content on these sites";
InsecureContentAllowedForUrls.pfm_description = "Allows you to set a list of url patterns that specify sites which are allowed to display blockable (i.e. active) mixed content (i.e. HTTP content on HTTPS sites) and for which optionally blockable mixed content upgrades will be disabled.\n\nIf this policy is left not set blockable mixed content will be blocked and optionally blockable mixed content will be upgraded, and users will be allowed to set exceptions to allow it for specific sites.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
InsecureContentBlockedForUrls.pfm_title = "Block insecure content on these sites";
InsecureContentBlockedForUrls.pfm_description = "Allows you to set a list of url patterns that specify sites which are not allowed to display blockable (i.e. active) mixed content (i.e. HTTP content on HTTPS sites), and for which optionally blockable (i.e. passive) mixed content will be upgraded.\n\nIf this policy is left not set blockable mixed content will be blocked and optionally blockable mixed content will be upgraded, but users will be allowed to set exceptions to allow it for specific sites.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
IntensiveWakeUpThrottlingEnabled.pfm_title = "Control the IntensiveWakeUpThrottling feature.";
IntensiveWakeUpThrottlingEnabled.pfm_description = "When enabled the IntensiveWakeUpThrottling feature causes JavaScript timers in background tabs to be aggressively throttled and coalesced, running no more than once per minute after a page has been backgrounded for 5 minutes or more.\n\nThis is a web standards compliant feature, but it may break functionality\non some websites by causing certain actions to be delayed by up to a\nminute. However, it results in significant CPU and battery savings when\nenabled. See https://bit.ly/30b1XR4 for more details.\n\nIf this policy is set to enabled then the feature will be force enabled, and\nusers will not be able to override this.\n\nIf this policy is set to disabled then the feature will be force disabled, and\nusers will not be able to override this.\n\nIf this policy is left unset then the feature will be controlled by its\nown internal logic, which can be manually configured by users.\n\nNote that the policy is applied per renderer process, with the most recent\nvalue of the policy setting in force when a renderer process starts. A full\nrestart is required to ensure that all loaded tabs receive a consistent\npolicy setting. It is harmless for processes to be running with different\nvalues of this policy.\n";
IntranetRedirectBehavior.pfm_title = "Intranet Redirection Behavior";
IntranetRedirectBehavior.pfm_description = "0 - Use default browser behavior.\n1 - Disable DNS interception checks and did-you-mean \"http://intranetsite/\" infobars.\n2 - Disable DNS interception checks; allow did-you-mean \"http://intranetsite/\" infobars.\n3 - Allow DNS interception checks and did-you-mean \"http://intranetsite/\" infobars.\nThis policy configures behavior for intranet redirection via DNS interception checks. The checks attempt to discover whether the browser is behind a proxy that redirects unknown host names.\n\nIf this policy is not set, the browser will use the default behavior of DNS interception checks and intranet redirect suggestions. In M88, they are enabled by default but will be disabled by default in the future release.\n\nDNSInterceptionChecksEnabled is a related policy that may also disable DNS interception checks; this policy is a more flexible version which may separately control intranet redirection infobars and may be expanded in the future.\nIf either DNSInterceptionChecksEnabled or this policy requests to disable interception checks, the checks will be disabled.";
IsolateOrigins.pfm_title = "Enable Site Isolation for specified origins";
IsolateOrigins.pfm_description = "Setting the policy means each of the named origins in a comma-separated list runs in a dedicated process. Each named origin's process will only be allowed to contain documents from that origin and its subdomains. For example, specifying https://a1.example.com/ allows https://a2.a1.example.com/ in the same process, but not https://example.com or https://b.example.com.\n\nSince Google Chrome 77, you can also specify a range of origins to isolate using a wildcard. For example, specifying https://[*.]corp.example.com will give every origin underneath https://corp.example.com its own dedicated process, including https://corp.example.com itself, https://a1.corp.example.com, and https://a2.a1.corp.example.com.\n\nNote that all sites (i.e., scheme plus eTLD+1, such as https://example.com) are already isolated by default on Desktop platforms, as noted in the SitePerProcess policy. This IsolateOrigins policy is useful to isolate specific origins at a finer granularity (e.g., https://a.example.com).\n\nAlso note that origins isolated by this policy will be unable to script other origins in the same site, which is otherwise possible if two same-site documents modify their document.domain values to match. Administrators should confirm this uncommon behavior is not used on an origin before isolating it.\n\nSetting the policy to off or leaving it unset lets users change this setting.\n\nNote: For Android, use the IsolateOriginsAndroid policy instead.";
JavaScriptAllowedForUrls.pfm_title = "Allow JavaScript on these sites";
JavaScriptAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can run JavaScript.\n\nLeaving the policy unset means DefaultJavaScriptSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
JavaScriptBlockedForUrls.pfm_title = "Block JavaScript on these sites";
JavaScriptBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can't run JavaScript.\n\nLeaving the policy unset means DefaultJavaScriptSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.\n\nNote that this policy blocks JavaScript based on whether the origin of the top-level document (usually the page URL that is also displayed in the address bar) matches any of the patterns. Therefore this policy is not appropriate for mitigating web supply-chain attacks. For example, supplying the pattern \"https://[*.]foo.com/\" will not prevent a page hosted on, say, https://example.com from running a script loaded from https://www.foo.com/example.js. Furthermore, supplying the pattern \"https://example.com/\" will not prevent a document from https://example.com from running scripts if it is not the top-level document, but embedded as a sub-frame into a page hosted on another origin, say, https://www.bar.com.";
JavaScriptJitAllowedForSites.pfm_title = "Allow JavaScript to use JIT on these sites";
JavaScriptJitAllowedForSites.pfm_description = "Allows you to set a list of site url patterns that specify sites which are allowed to run JavaScript with JIT (Just In Time) compiler enabled.\n\nFor detailed information on valid site url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.\n\nJavaScript JIT policy exceptions will only be enforced at a site granularity (eTLD+1). A policy set for only subdomain.site.com will not correctly apply to site.com or subdomain.site.com since they both resolve to the same eTLD+1 (site.com) for which there is no policy. In this case, policy must be set on site.com to apply correctly for both site.com and subdomain.site.com.\n\nThis policy applies on a frame-by-frame basis and not based on top level origin url alone, so e.g. if site-one.com is listed in the JavaScriptJitAllowedForSites policy but site-one.com loads a frame containing site-two.com then site-one.com will have JavaScript JIT enabled, but site-two.com will use the policy from DefaultJavaScriptJitSetting, if set, or default to JavaScript JIT enabled.\n\nIf this policy is not set for a site then the policy from DefaultJavaScriptJitSetting applies to the site, if set, otherwise Javascript JIT is enabled for the site.";
JavaScriptJitBlockedForSites.pfm_title = "Block JavaScript from using JIT on these sites";
JavaScriptJitBlockedForSites.pfm_description = "Allows you to set a list of site url patterns that specify sites which are not allowed to run JavaScript JIT (Just In Time) compiler enabled.\n\nDisabling the JavaScript JIT will mean that Google Chrome may render web content more slowly, and may also disable parts of JavaScript including WebAssembly. Disabling the JavaScript JIT may allow Google Chrome to render web content in a more secure configuration.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.\n\nJavaScript JIT policy exceptions will only be enforced at a site granularity (eTLD+1). A policy set for only subdomain.site.com will not correctly apply to site.com or subdomain.site.com since they both resolve to the same eTLD+1 (site.com) for which there is no policy. In this case, policy must be set on site.com to apply correctly for both site.com and subdomain.site.com.\n\nThis policy applies on a frame-by-frame basis and not based on top level origin url alone, so e.g. if site-one.com is listed in the JavaScriptJitBlockedForSites policy but site-one.com loads a frame containing site-two.com then site-one.com will have JavaScript JIT disabled, but site-two.com will use the policy from DefaultJavaScriptJitSetting, if set, or default to JavaScript JIT enabled.\n\nIf this policy is not set for a site then the policy from DefaultJavaScriptJitSetting applies to the site, if set, otherwise JavaScript JIT is enabled for the site.";
JavaScriptOptimizerAllowedForSites.pfm_title = "Allow JavaScript optimization on these sites";
JavaScriptOptimizerAllowedForSites.pfm_description = "Allows you to set a list of site url patterns that specify sites for which\nadvanced JavaScript optimizations are enabled.\n\nFor detailed information on valid site url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.\nWildcards, *, are allowed.\n\nJavaScript optimization policy exceptions will only be enforced at a site\ngranularity (eTLD+1). A policy set for only subdomain.site.com will not\ncorrectly apply to site.com or subdomain.site.com since they both resolve to\nthe same eTLD+1 (site.com) for which there is no policy. In this case, policy\nmust be set on site.com to apply correctly for both site.com and\nsubdomain.site.com.\n\nThis policy applies on a frame-by-frame basis and not based on top level\norigin url alone, so e.g. if site-one.com is listed in the JavaScriptOptimizerAllowedForSites policy but site-one.com loads a frame   containing site-two.com then site-one.com will have JavaScript optimizations\nenabled, but site-two.com will use the policy from DefaultJavaScriptOptimizerSetting, if set, or default to JavaScript\noptimizations enabled. Blocklist entries have higher priority than allowlist\nentries, which in turn have higher priority than the configured default value.\n\nIf this policy is not set for a site then the policy from DefaultJavaScriptOptimizerSetting applies to the site, if set, otherwise\nJavascript optimization is enabled for the site.";
JavaScriptOptimizerBlockedForSites.pfm_title = "Block JavaScript optimizations on these sites";
JavaScriptOptimizerBlockedForSites.pfm_description = "Allows you to set a list of site url patterns that specify sites for which\nadvanced JavaScript optimizations are disabled.\n\nDisabling JavaScript optimizations will mean that Google Chrome may render web content more slowly.\n\nFor detailed information on valid url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.\nWildcards, *, are allowed.\n\nJavaScript optimization policy exceptions will only be enforced at a site\ngranularity (eTLD+1). A policy set for only subdomain.site.com will not\ncorrectly apply to site.com or subdomain.site.com since they both resolve to\nthe same eTLD+1 (site.com) for which there is no policy. In this case, policy\nmust be set on site.com to apply correctly for both site.com and\nsubdomain.site.com.\n\nThis policy applies on a frame-by-frame basis and not based on top level\norigin url alone, so e.g. if site-one.com is listed in the JavaScriptOptimizerBlockedForSites policy but site-one.com loads a frame\ncontaining site-two.com then site-one.com will have JavaScript optimizations\ndisabled, but site-two.com will use the policy from DefaultJavaScriptOptimizerSetting, if set, or default to JavaScript\noptimizations enabled. Blocklist entries have higher priority than allowlist\nentries, which in turn have higher priority than the configured default value.\n\nIf this policy is not set for a site then the policy from DefaultJavaScriptOptimizerSetting applies to the site, if set, otherwise\nJavaScript optimization is enabled for the site.";
LensDesktopNTPSearchEnabled.pfm_title = "Allow Google Lens button to be shown in the search box on the New Tab page if supported.";
LensDesktopNTPSearchEnabled.pfm_description = "Leaving the policy unset or setting it to Enabled allows users to view and use the Google Lens button in the search box on the New Tab page. Setting the policy to Disabled means users will not see the Google Lens button in the search box on the New Tab page.";
LensOverlaySettings.pfm_title = "Settings for the Lens Overlay feature";
LensOverlaySettings.pfm_description = "0 - Enable\n1 - Disable\nLens Overlay lets users issue Google searches by interacting with a screenshot of the current page laid over the actual web contents.\n\nThere is no user setting to control this feature, it is generally made available to all users with Google as their default search engine unless disabled by this policy.\n\nWhen policy is set to 0 - Enabled or not set, the feature will be available to users.\nWhen policy is set to 1 - Disabled, the feature will not be available.";
LensRegionSearchEnabled.pfm_title = "Allow Google Lens region search menu item to be shown in context menu if supported.";
LensRegionSearchEnabled.pfm_description = "Leaving the policy unset or setting it to Enabled allows users to view and use the Google Lens region search menu item in the context menu. Setting the policy to Disabled means users will not see the Google Lens region search menu item in the context menu when Google Lens region search is supported.";
LiveTranslateEnabled.pfm_title = "Enable Live Translate";
LiveTranslateEnabled.pfm_description = "Enable translation of live captions. Captions will be sent to Google for translation.\n\nIf this policy is set to Enabled, Live Translate will always be turned on.\n\nIf this policy is set to Disabled, Live Translate will always be turned off.\n\nIf you set this policy as mandatory, users cannot change or override it.\n\nIf this policy is left unset, Live Translate is disabled initially but can be enabled by the user anytime.";
LocalFontsAllowedForUrls.pfm_title = "Allow Local Fonts permission on these sites";
LocalFontsAllowedForUrls.pfm_description = "Sets a list of site url patterns that specify sites which will automatically grant the local fonts permission. This will extend the ability of sites to see information about local fonts.\n\nFor detailed information on valid site url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed. This policy only matches based on origin, so any path in the URL pattern is ignored.\n\nIf this policy is not set for a site then the policy from DefaultLocalFontsSetting applies to the site, if set, otherwise the permission will follow the browser's defaults and allow users to choose this permission per site.";
LocalFontsBlockedForUrls.pfm_title = "Block Local Fonts permission on these sites";
LocalFontsBlockedForUrls.pfm_description = "Sets a list of site url patterns that specify sites which will automatically deny the local fonts permission. This will limit the ability of sites to see information about local fonts.\n\nFor detailed information on valid site url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed. This policy only matches based on origin, so any path in the URL pattern is ignored.\n\nIf this policy is not set for a site then the policy from DefaultLocalFontsSetting applies to the site, if set, otherwise the permission will follow the browser's defaults and allow users to choose this permission per site.";
LocalNetworkAccessRestrictionsEnabled.pfm_title = "Specifies whether to apply restrictions to requests to local network endpoints";
LocalNetworkAccessRestrictionsEnabled.pfm_description = "When this policy is set to Enabled, any time when a warning is supposed to be\ndisplayed in the DevTools due to Local Network Access checks failing, the\nmain request will be blocked instead.\n\nWhen this policy is set to Disabled or unset, Local Network Access requests will use the\ndefault handling of these requests.\n\nSee https://github.com/explainers-by-googlers/local-network-access for Local Network Access restrictions.";
LookalikeWarningAllowlistDomains.pfm_title = "Suppress lookalike domain warnings on domains";
LookalikeWarningAllowlistDomains.pfm_description = "This policy prevents the display of lookalike URL warnings on the sites listed. These warnings are typically shown on sites that Google Chrome believes might be trying to spoof another site the user is familiar with.\n\nIf the policy is enabled and set to one or more domains, no lookalike warnings pages will be shown when the user visits pages on that domain.\n\nIf the policy is not set, or set to an empty list, warnings may appear on any site the user visits.\n\nA hostname can be allowed with a complete host match, or any domain match. For example, a URL like \"https://foo.example.com/bar\" may have warnings suppressed if this list includes either \"foo.example.com\" or \"example.com\".";
ManagedAccountsSigninRestriction.pfm_title = "Add restrictions on managed accounts";
ManagedAccountsSigninRestriction.pfm_description = "primary_account - A Managed account must be a primary account and importing existing browsing data is allowed at the time of profile creation\nprimary_account_strict - A Managed account must be a primary account and have no secondary accounts and importing existing browsing data is allowed at the time of profile creation\nnone - No restrictions on managed accounts\nprimary_account_keep_existing_data - A Managed account must be a primary account and the user can import existing data at the time of its creation\nprimary_account_strict_keep_existing_data - A Managed account must be a primary account and have no secondary accounts and the user can import existing data at the time of its creation\n\nDefault behavior (Policy unset)\nWhen an account is added in the content area a small dialog may appear asking the user to create a new profile. This dialog is dismissable.\n\nManagedAccountsSigninRestriction = 'primary_account'\nIf a user signs into a Google service for the first time in a Google Chrome browser, a dialog will appear asking the user to create a new profile for their enterprise account. The user may click Cancel and get signed out, or Continue to create a new profile. Any existing browsing data will not be added to the new profile. The newly created profile is allowed to have secondary accounts, for example the user can sign into another account in the content area.\n\nManagedAccountsSigninRestriction = 'primary_account_strict'\nThis is the same behavior as 'primary_account' except the newly created profile is not allowed to have secondary accounts.\n\nManagedAccountsSigninRestriction = 'primary_account_keep_existing_data'\nThis is the same behavior as 'primary_account' except a checkbox will be added to the dialog to allow the user to keep local browsing data.\nIf the user checks the box, then the existing profile data becomes associated with the Managed account.\n-  All existing browsing data will be present in the new profile.\n-  This data includes bookmarks, history, password, autofill data, open tabs, cookies, cache, web storage, extensions, etc.\nIf the user does not check the box:\n-  The old profile will continue to exist, no data will be lost.\n-  A new profile will be created.\n\nManagedAccountsSigninRestriction = 'primary_account_strict_keep_existing_data'\nThis is the same behavior as 'primary_account_keep_existing_data' except the newly created profile is not allowed to have secondary accounts.";
ManagedBookmarks.pfm_title = "Managed Bookmarks";
ManagedBookmarks.pfm_description = "Setting the policy sets up a list of bookmarks where each one is a dictionary with the keys \"name\" and \"url\". These keys hold the bookmark's name and target. Admins can set up a subfolder by defining a bookmark without a \"url\" key, but with an additional \"children\" key. This key also has a list of bookmarks, some of which can also be folders. Chrome amends incomplete URLs as if they were submitted through the address bar. For example, \"google.com\" becomes \"https://google.com/\".\n\nUsers can't change the folders the bookmarks are placed in (though they can hide it from the bookmark bar). The default folder name for managed bookmarks is \"Managed bookmarks\" but it can be changed by adding a new sub-dictionary to the policy with a single key named \"toplevel_name\" with the desired folder name as its value. Managed bookmarks are not synced to the user account and extensions can't modify them.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=ManagedBookmarks for more information about schema and formatting.";
ManagedConfigurationPerOrigin.pfm_title = "Sets managed configuration values to websites to specific origins";
ManagedConfigurationPerOrigin.pfm_description = "Setting the policy defines the return value of Managed Configuration API for given origin.\n\n Managed configuration API is a key-value configuration that can be accessed via navigator.managed.getManagedConfiguration() javascript call. This API is only available to origins which correspond to force-installed web applications via WebAppInstallForceList.\n\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=ManagedConfigurationPerOrigin for more information about schema and formatting.";
MandatoryExtensionsForIncognitoNavigation.pfm_title = "Extensions that have to be allowed to run in Incognito by the user in order to navigate in Incognito mode";
MandatoryExtensionsForIncognitoNavigation.pfm_description = "This policy allows administrators to configure a list of extension ids required for Incognito mode navigation.\n\nThe user must explicitly allow all extensions in this list to run in Incognito mode, otherwise navigation in Incognito is not allowed.\n\nIf an extension specified in this policy is not installed, Incognito navigation is blocked.\n\nThis policy is applied to the Incognito mode. This means Incognito must be enabled in the browser. If Incognito mode is disabled via the policy IncognitoModeAvailability, this policy has no effect.";
MaxConnectionsPerProxy.pfm_title = "Maximal number of concurrent connections to the proxy server";
MaxConnectionsPerProxy.pfm_description = "Setting the policy specifies the maximal number of simultaneous connections to the proxy server. Some proxy servers can't handle a high number of concurrent connections per client, which is solved by setting this policy to a lower value. The value should be lower than 100 and higher than 6. Some web apps are known to consume many connections with hanging GETs, so setting a value below 32 may lead to browser networking hangs if there are too many web apps with hanging connections open. Lower below the default at your own risk.\n\nLeaving the policy unset means a default of 32 is used.";
MaxInvalidationFetchDelay.pfm_title = "Maximum fetch delay after a policy invalidation";
MaxInvalidationFetchDelay.pfm_description = "Setting the policy specifies the maximum delay in milliseconds between receiving a policy invalidation and fetching the new policy from the device management service. Valid values range from 1,000 (1 second) to 300,000 (5 minutes). Values outside this range will be clamped to the respective boundary.\n\nLeaving the policy unset means Google Chrome uses the default value of 10 seconds.";
MediaRecommendationsEnabled.pfm_title = "Enable Media Recommendations";
MediaRecommendationsEnabled.pfm_description = "By default the browser will show media recommendations that are personalized to the user. Setting this policy to Disabled will result in these recommendations being hidden from the user. Setting this policy to Enabled or leaving it unset will result in the media recommendations being shown to the user.";
MediaRouterCastAllowAllIPs.pfm_title = "Allow Google Cast to connect to Cast devices on all IP addresses.";
MediaRouterCastAllowAllIPs.pfm_description = "Unless EnableMediaRouter is set to Disabled, setting MediaRouterCastAllowAllIPs to Enabled connects Google Cast to Cast devices on all IP addresses, not just RFC1918/RFC4193 private addresses.\n\nSetting the policy to Disabled connects Google Cast to Cast devices only on RFC1918/RFC4193.\n\nLeaving the policy unset connects Google Cast to Cast devices only on RFC1918/RFC4193, unless the CastAllowAllIPs feature is turned on.";
MemorySaverModeSavings.pfm_title = "Change Memory Saver Mode Savings";
MemorySaverModeSavings.pfm_description = "0 - Moderate memory savings.\n1 - Balanced memory savings.\n2 - Maximum memory savings.\nThis policy changes the savings level of Memory Saver.\n\nThis only takes effect when Memory Saver is enabled through settings or through the HighEfficiencyModeEnabled policy, and will affect how heuristics are used to determine when to discard tabs. For example, reducing the lifetime of an inactive tab before discarding it can save memory, but it also means that tabs will be reloaded more frequently which can lead to bad user experience and cost more network traffic.\n\nSetting the policy to 0 - Memory Saver will get moderate memory savings. Tabs become inactive after a longer period of time\n\nSetting the policy to 1 - Memory Saver will get balanced memory savings. Tabs become inactive after an optimal period of time.\n\nSetting the policy to 2 - Memory Saver will get maximum memory savings. Tabs become inactive after a shorter period of time.\n\nIf this policy is unset, the end user can control this setting in chrome://settings/performance.";
MetricsReportingEnabled.pfm_title = "Enable reporting of usage and crash-related data";
MetricsReportingEnabled.pfm_description = "When this policy is Enabled, anonymous reporting of usage and crash-related data about Google Chrome to Google is recommended to be enabled by default. Users will still be able to change this setting.\n\nWhen this policy is Disabled, anonymous reporting is disabled and no usage or crash data is sent to Google. Users won't be able to change this setting.\n\nWhen this policy is not set, users can choose the anonymous reporting behavior at installation or first run, and can change this setting later.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\n\n(For Google ChromeOS, see DeviceMetricsReportingEnabled.)";
NTPCardsVisible.pfm_title = "Show cards on the New Tab Page";
NTPCardsVisible.pfm_description = "This policy controls the visibility of cards on the New Tab Page. Cards surface entry points to launch common user journeys based on the user's browsing behavior.\n\nIf the policy is set to Enabled, the New Tab Page will show cards if content is available.\n\nIf the policy is set to Disabled, the New Tab Page won't show cards.\n\nIf the policy is not set, the user can control the card visibility. The default is visible.\n";
NTPCustomBackgroundEnabled.pfm_title = "Allow users to customize the background on the New Tab page";
NTPCustomBackgroundEnabled.pfm_description = "If the policy is set to false, the New Tab page won't allow users to customize the background. Any existing custom background will be permanently removed even if the policy is set to true later.\n\nIf the policy is set to true or unset, users can customize the background on the New Tab page.";
NTPFooterExtensionAttributionEnabled.pfm_title = "Control the visibility of the extension attribution on the New Tab page";
NTPFooterExtensionAttributionEnabled.pfm_description = "This policy determines whether an attribution to the extension modifying the New Tab Page (NTP) is displayed in the NTP's footer.\n\nBy default, if an extension has overridden the standard NTP, a message attributing this change to the specific extension will appear in the footer. This attribution typically includes a link to the relevant extension in the Chrome Web Store.\n\nIf this policy is left unset or set to true, the extension attribution will be visible on the NTP footer when an extension is controlling the NTP.\n\nIf this policy is set to false, the attribution to the extension in the NTP footer will be suppressed.";
NTPMiddleSlotAnnouncementVisible.pfm_title = "Show the middle slot announcement on the New Tab Page";
NTPMiddleSlotAnnouncementVisible.pfm_description = "This policy controls the visibility of the middle slot announcement on the New Tab Page.\n\nIf the policy is set to Enabled, the New Tab Page will show the middle slot announcement if it is available.\n\nIf the policy is set to Disabled, the New Tab Page will not show the middle slot announcement even if it is available.\n";
NTPOutlookCardVisible.pfm_title = "Show Outlook Calendar card on the New Tab Page";
NTPOutlookCardVisible.pfm_description = "This policy controls the visibility of the Outlook Card on the New Tab Page. The card will only be displayed on the New Tab Page if the policy is enabled and your organization authorized the usage of the Outlook Calendar data in the browser.\n\nOutlook data will not be stored by the browser.\n\nThe Outlook card shows the next calendar event, along with a glanceable look at the rest of the day's meetings. It aims to address the issue of context switching and enhance productivity by giving users a shortcut to their next meeting.\n\nThe Microsoft Outlook card will require additional admin configuration. For detailed information on connecting the Chrome New Tab Page Card to Outlook, please see https://support.google.com/chrome/a?p=chrome_ntp_microsoft_cards.\n\nIf the NTPCardsVisible is disabled, the Outlook Card will not be shown. If NTPCardsVisible is enabled, the Outlook card will be shown if this policy is also enabled and there is data to be shown. If NTPCardsVisible is unset, the Outlook card will be shown if this policy is also enabled, the user has the card enabled in Customize Chrome, and there is data to be shown.";
NTPSharepointCardVisible.pfm_title = "Show SharePoint and OneDrive File Card on the New Tab Page";
NTPSharepointCardVisible.pfm_description = "This policy controls the visibility of the SharePoint and OneDrive File Card on the New Tab Page. The card will only be displayed on the New Tab Page if the policy is enabled and your organization authorized the usage of the SharePoint and OneDrive File data in the browser.\n\nSharePoint and OneDrive data will not be stored by the browser.\n\nThe SharePoint and OneDrive Files recommendation card shows a list of recommended files.  It aims to address the issue of context switching and enhance productivity by giving users a shortcut to their most important documents.\n\nThe Microsoft SharePoint and OneDrive card will require additional admin configuration. For detailed information on connecting the Chrome New Tab Page Card to Sharepoint, please see https://support.google.com/chrome/a?p=chrome_ntp_microsoft_cards.\n\nIf the NTPCardsVisible is disabled, the SharePoint and OneDrive Card will not be shown. If NTPCardsVisible is enabled, the SharePoint and OneDrive card will be shown if this policy is also enabled and there is data to be shown. If NTPCardsVisible is unset, the SharePoint and OneDrive card will be shown if this policy is also enabled, the user has the card enabled in Customize Chrome, and there is data to be shown.";
NativeMessagingAllowlist.pfm_title = "Names of the native messaging hosts to exempt from the blocklist";
NativeMessagingAllowlist.pfm_description = "Setting the policy specifies which native messaging hosts aren't subject to the deny list. A deny list value of * means all native messaging hosts are denied, unless they're explicitly allowed.\n\nAll native messaging hosts are allowed by default. But, if all native messaging hosts are denied by policy, the admin can use the allow list to change that policy.";
NativeMessagingBlocklist.pfm_title = "Names of the forbidden native messaging hosts (or * for all)";
NativeMessagingBlocklist.pfm_description = "Setting the policy specifies which native messaging hosts shouldn't be loaded. A deny list value of * means all native messaging hosts are denied, unless they're explicitly allowed.\n\nLeaving the policy unset means Google Chrome loads all installed native messaging hosts.";
NativeMessagingUserLevelHosts.pfm_title = "Allow user-level Native Messaging hosts (installed without admin permissions)";
NativeMessagingUserLevelHosts.pfm_description = "Setting the policy to Enabled or leaving it unset means Google Chrome can use native messaging hosts installed at the user level.\n\nSetting the policy to Disabled means Google Chrome can only use these hosts if installed at the system level.";
NetworkPredictionOptions.pfm_title = "Enable network prediction";
NetworkPredictionOptions.pfm_description = "0 - Predict network actions on any network connection\n1 - Predict network actions on any network that is not cellular.\n(Deprecated in 50, removed in 52. After 52, if value 1 is set, it will be treated as 0 - predict network actions on any network connection.)\n2 - Do not predict network actions on any network connection\nThis policy controls network prediction in Google Chrome. It controls DNS prefetching, TCP, and SSL preconnection and prerendering of webpages.\n\nIf you set the policy, users can't change it. Leaving it unset turns on network prediction, but the user can change it.";
NewTabPageLocation.pfm_title = "New Tab page URL";
NewTabPageLocation.pfm_description = "Setting the policy configures the default New Tab page URL and prevents users from changing it.\n\nThe New Tab page opens with new tabs and windows.\n\nThis policy doesn't decide which pages open on start up. Those are controlled by the RestoreOnStartup policies. This policy does affect the homepage, if that's set to open the New Tab page, as well as the startup page if it's set to open the New Tab page.\n\nIt is a best practice to provide fully canonicalized URL, if the URL is not fully canonicalized Google Chrome will default to https://.\n\nLeaving the policy unset or empty puts the default New Tab page in use.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
NotificationsAllowedForUrls.pfm_title = "Allow notifications on these sites";
NotificationsAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can display notifications.\n\nLeaving the policy unset means DefaultNotificationsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
NotificationsBlockedForUrls.pfm_title = "Block notifications on these sites";
NotificationsBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can't display notifications.\n\nLeaving the policy unset means DefaultNotificationsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
NtlmV2Enabled.pfm_title = "Enable NTLMv2 authentication.";
NtlmV2Enabled.pfm_description = "Setting the policy to Enabled or leaving it unset turns NTLMv2 on.\n\nSetting the policy to Disabled turns NTLMv2 off.\n\nAll recent versions of Samba and Microsoft® Windows® servers support NTLMv2. This should only be turned off for backward compatibility as it reduces the security of authentication.";
OopPrintDriversAllowed.pfm_title = "Out-of-process print drivers allowed";
OopPrintDriversAllowed.pfm_description = "Controls if Google Chrome interacts with printer drivers from a separate service process. Platform printing calls to query available printers, get print driver settings, and submit documents for printing to local printers are made from a service process. Moving such calls out of the browser process helps improve stability and reduce frozen UI behavior in Print Preview.\n\nWhen this policy is set to Enabled or not set, Google Chrome will use a separate service process for platform printing tasks.\n\nWhen this policy is set to Disabled, Google Chrome will use the browser process for platform printing tasks.\n\nThis policy will be removed in the future, after the out-of-process print drivers feature has fully rolled out.";
OriginAgentClusterDefaultEnabled.pfm_title = "Allows origin-keyed agent clustering by default.";
OriginAgentClusterDefaultEnabled.pfm_description = "This policy allows origin-keyed agent clustering by default.\n\nThe Origin-Agent-Cluster HTTP header controls whether a document is\nisolated in an origin-keyed agent cluster, or in a site-keyed agent\ncluster. This has security implications since an origin-keyed agent\ncluster allows isolating documents by origin. The developer-visible\nconsequence of this is that the document.domain accessor can no longer\nbe set.\n\nThe default behaviour - when no Origin-Agent-Cluster header has been set -\nchanges in M111 from site-keyed to origin-keyed.\n\nIf this policy is enabled or not set, the browser will follow this\nnew default from that version on.\n\nIf this policy is disabled this change is reversed and\ndocuments without Origin-Agent-Cluster headers will be assigned to\nsite-keyed agent clusters. As a consequence, the document.domain accessor\nremains settable by default. This matches the legacy behaviour.\n\nSee https://developer.chrome.com/blog/immutable-document-domain/ for\nadditional details.";
OverrideSecurityRestrictionsOnInsecureOrigin.pfm_title = "Origins or hostname patterns for which restrictions on insecure origins should not apply";
OverrideSecurityRestrictionsOnInsecureOrigin.pfm_description = "Setting the policy specifies a list of origins (URLs) or hostname patterns (such as *.example.com) for which security restrictions on insecure origins won't apply. Patterns are only accepted for hostnames; URLs/origins with schemes must be exact strings. Organizations can specify origins for legacy applications that can't deploy TLS or set up a staging server for internal web development, so developers can test out features requiring secure contexts without having to deploy TLS on the staging server. This policy also prevents the origin from being labeled \"Not Secure\" in the address bar.\n\nSetting a list of URLs in this policy amounts to setting the command-line flag --unsafely-treat-insecure-origin-as-secure to a comma-separated list of the same URLs. The policy overrides the command-line flag and UnsafelyTreatInsecureOriginAsSecure, if present.\n\nFor more information on secure contexts, see Secure Contexts ( https://www.w3.org/TR/secure-contexts ).";
PartitionedBlobUrlUsage.pfm_title = "Choose whether Blob URLs are partitioned during fetching and navigations";
PartitionedBlobUrlUsage.pfm_description = "This policy controls whether Blob URLs are partitioned during fetching and navigation.\nIf this policy is set to Enabled or not set, Blob URLs will be partitioned.\nIf this policy is set to Disabled, Blob URLs won't be partitioned.\n\nIf storage partitioning is disabled for a given top-level origin by either\nThirdPartyStoragePartitioningBlockedForOrigins\nor DefaultThirdPartyStoragePartitioningSetting,\nthen Blob URLs will also not be partitioned.\n\nIf you must use the policy, please file a bug at\nGoogle Chrome\nexplaining your use case. The policy is scheduled to be offered through\nGoogle Chrome version 143, after which\nthe old implementation will be removed.\n\nNOTE: Only newly-started renderer processes will reflect changes to this\npolicy while the browser is running.\n\nFor detailed information on third-party storage partitioning, please see\nhttps://developers.google.com/privacy-sandbox/cookies/storage-partitioning.";
PasswordDismissCompromisedAlertEnabled.pfm_title = "Enable dismissing compromised password alerts for entered credentials";
PasswordDismissCompromisedAlertEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset gives the user the option to dismiss/restore compromised password alerts.\n\nIf you disable this setting, users will not be able to dismiss alerts about compromised passwords. If enabled, users will be able to dismiss alerts about compromised passwords.";
PasswordLeakDetectionEnabled.pfm_title = "Enable leak detection for entered credentials";
PasswordLeakDetectionEnabled.pfm_description = "Setting the policy to Enabled lets users have Google Chrome check whether usernames and passwords entered were part of a leak.\n\nSetting the policy to Disabled does not let users have this functionality.\n\nIf the policy is set, users can't change it in Google Chrome. If not set, credential leak checking is allowed, but the user can turn it off.";
PasswordManagerBlocklist.pfm_title = "Configure the list of domains for which the Password Manager (Save and Fill) will be disabled";
PasswordManagerBlocklist.pfm_description = "Configure the list of domains where Google Chrome should disable the Password Manager. This means that Save and Fill workflows will be disabled, ensuring that passwords for those websites can't be saved or auto filled into web forms.\n\nIf a domain is present in the list, the Password Manager will be disabled for it.\n\nIf a domain is not present in the list, the Password Manager will be available for it.\n\nIf the policy is unset, the Password Manager will be available for all domains.";
PasswordManagerEnabled.pfm_title = "Enable saving passwords to the password manager";
PasswordManagerEnabled.pfm_description = "This policy controls the browser's ability to automatically remember passwords on websites and save them in the built-in password manager. It does not limit access or change the contents of passwords saved in the password manager and possibly synchronized to the Google account profile and Android.\n\nSetting the policy to Enabled means users have Google Chrome remember passwords and provide them the next time they sign in to a site.\n\nSetting the policy to Disabled means users can't save new passwords, but previously saved passwords will still work.\n\nIf the policy is set, users can't change it in Google Chrome. If not set, the user can turn off password saving.";
PasswordManagerPasskeysEnabled.pfm_title = "Enable saving passkeys to the password manager";
PasswordManagerPasskeysEnabled.pfm_description = "This policy controls the browser's ability to save passkeys in the built-in password manager. It does not limit access to, or change the contents of, passkeys already saved in the password manager. If the PasswordManagerEnabled policy is set to Disabled then saving in the built-in password manager is disabled in general, including passkeys and passwords, and thus this policy is not applicable.\n\nSetting the policy to Enabled or leaving unset means that users can save passkeys in the built-in password manager if signed into Google Chrome.\n\nSetting the policy to Disabled means users can't save passkeys to the built-in password manager, but previously saved passkeys will still work.";
PasswordProtectionChangePasswordURL.pfm_title = "Configure the change password URL.";
PasswordProtectionChangePasswordURL.pfm_description = "Setting the policy sets the URL for users to change their password after seeing a warning in the browser. The password protection service sends users to the URL (HTTP and HTTPS protocols only) you designate through this policy. For Google Chrome to correctly capture the salted hash of the new password on this change password page, make sure your change password page follows these guidelines ( https://www.chromium.org/developers/design-documents/create-amazing-password-forms ).\n\nTurning the policy off or leaving it unset means the service sends users to https://myaccount.google.com to change their password.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
PasswordProtectionLoginURLs.pfm_title = "Configure the list of enterprise login URLs where password protection service should capture salted hashes of passwords.";
PasswordProtectionLoginURLs.pfm_description = "Setting the policy sets the list of enterprise login URLs (HTTP and HTTPS protocols only). Password protection service will capture salted hashes of passwords on these URLs and use them for password reuse detection. For Google Chrome to correctly capture password salted hashes, ensure your sign-in pages follow these guidelines ( https://www.chromium.org/developers/design-documents/create-amazing-password-forms ).\n\nTurning this setting off or leaving it unset means the password protection service only captures the password salted hashes on https://accounts.google.com.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
PasswordProtectionWarningTrigger.pfm_title = "Password protection warning trigger";
PasswordProtectionWarningTrigger.pfm_description = "0 - Password protection warning is off\n1 - Password protection warning is triggered by password reuse\n2 - Password protection warning is triggered by password reuse on phishing page\nSetting the policy lets you control the triggering of password protection warning. Password protection alerts users when they reuse their protected password on potentially suspicious sites.\n\nUse PasswordProtectionLoginURLs and PasswordProtectionChangePasswordURL to set which password to protect.\n\nIf this policy is set to:\n\n* PasswordProtectionWarningOff, no password protection warning will be shown.\n\n* PasswordProtectionWarningOnPasswordReuse, password protection warning will be shown when the user reuses their protected password on a non-allowed site.\n\n* PasswordProtectionWarningOnPhishingReuse, password protection warning will be shown when the user reuses their protected password on a phishing site.\n\nLeaving the policy unset has the password protection service only protect Google passwords, but users can change this setting.";
PasswordSharingEnabled.pfm_title = "Enable sharing user credentials with other users";
PasswordSharingEnabled.pfm_description = "Setting the policy to Enabled lets users send to and receive from family members (according to Family Service) their passwords.\nWhen the policy is Enabled or not set, there is a button in the Password Manager allowing to send a password.\nThe received passwords are stored into user's account and are available in the Password Manager.\n\nSetting the policy to Disabled means users can't send passwords from Password Manager to other users, and can't receive passwords from other users.\n\nThe feature is not available if synchronization of Passwords is turned off (either via user settings or SyncDisabled policy is Enabled).\n\nManaged accounts aren't eligible to join or create a family group and therefore cannot share passwords.";
PaymentMethodQueryEnabled.pfm_title = "Allow websites to query for available payment methods.";
PaymentMethodQueryEnabled.pfm_description = "Allows you to set whether websites are allowed to check if the user has payment methods saved.\n\nIf this policy is set to disabled, websites that use PaymentRequest.canMakePayment or PaymentRequest.hasEnrolledInstrument API will be informed that no payment methods are available.\n\nIf the setting is enabled or not set then websites are allowed to check if the user has payment methods saved.";
PdfAnnotationsEnabled.pfm_title = "Enable PDF Annotations";
PdfAnnotationsEnabled.pfm_description = "Controls if the PDF viewer in Google Chrome can annotate PDFs.\n\nWhen this policy is not set, or is set to true, then the PDF viewer will be able to annotate PDFs.\n\nWhen this policy is set to false, then the PDF viewer will not be able to annotate PDFs.";
PdfLocalFileAccessAllowedForDomains.pfm_title = "Allow local file access to file:// URLs on these sites in the PDF Viewer";
PdfLocalFileAccessAllowedForDomains.pfm_description = "Setting this policy allows the domains listed to access file:// URLs in the PDF Viewer.\nAdding to the policy allows the domain to access file:// URLs in the PDF Viewer.\nRemoving from the policy disallows the domain from accessing file:// URLs in the PDF Viewer.\nLeaving the policy unset disallows all domains from accessing file:// URLs in the PDF Viewer.";
PdfUseSkiaRendererEnabled.pfm_title = "Use Skia renderer for PDF rendering";
PdfUseSkiaRendererEnabled.pfm_description = "Controls whether the PDF viewer in Google Chrome uses Skia renderer.\n\nWhen this policy is enabled, the PDF viewer uses Skia renderer.\n\nWhen this policy is disabled, the PDF viewer uses its current AGG renderer.\n\nWhen this policy is not set, the PDF renderer will be chosen by the browser.";
PdfViewerOutOfProcessIframeEnabled.pfm_title = "Use out-of-process iframe PDF Viewer";
PdfViewerOutOfProcessIframeEnabled.pfm_description = "Controls whether the PDF viewer in Google Chrome uses an out-of-process iframe (OOPIF). This will be the new PDF viewer architecture in the future, as it is simpler and makes adding new features easier. The existing GuestView PDF viewer is an outdated, complex architecture that is being deprecated.\n\nWhen this policy is set to Enabled or not set, Google Chrome will be able to use the OOPIF PDF viewer architecture. Once Enabled or not set, the default behavior will be decided by Google Chrome.\n\nWhen this policy is set to Disabled, Google Chrome will strictly use the existing GuestView PDF viewer. It embeds a web page with a separate frame tree into another web page.\n\nThis policy will be removed in the future, after the OOPIF PDF viewer feature has fully rolled out.";
PolicyAtomicGroupsEnabled.pfm_title = "Enables the concept of policy atomic groups";
PolicyAtomicGroupsEnabled.pfm_description = "Setting the policy to Enabled means policies coming from an atomic group that don't share the source with the highest priority from that group get ignored.\n\nSetting the policy to Disabled means no policy is ignored because of its source. Policies are ignored only if there's a conflict, and the policy doesn't have the highest priority.\n\nIf this policy is set from a cloud source, it can't target a specific user.";
PolicyDictionaryMultipleSourceMergeList.pfm_title = "Allow merging dictionary policies from different sources";
PolicyDictionaryMultipleSourceMergeList.pfm_description = "ContentPackManualBehaviorURLs - Managed user manual exception URLs\nDeviceLoginScreenPowerManagement - Power management on the login screen\nExtensionSettings - Extension management settings\nKeyPermissions - Key Permissions\nPowerManagementIdleSettings - Power management settings when the user becomes idle\nScreenBrightnessPercent - Screen brightness percent\nScreenLockDelays - Screen lock delays\nSetting the policy allows merging of selected policies when they come from different sources, with the same scopes and level. This merging is in the first level keys of the dictionary from each source. The key coming from the highest priority source takes precedence.\n\nUse the wildcard character '*' to allow merging of all supported dictionary policies.\n\nIf a policy is in the list and there's conflict between sources with:\n\n* The same scopes and level: The values merge into a new policy dictionary.\n\n* Different scopes or level: The policy with the highest priority applies.\n\nIf a policy isn't in the list and there's conflict between sources, scopes, or level, the policy with the highest priority applies.";
PolicyListMultipleSourceMergeList.pfm_title = "Allow merging list policies from different sources";
PolicyListMultipleSourceMergeList.pfm_description = "Setting the policy allows merging of selected policies when they come from different sources, with the same scopes and level.\n\nUse the wildcard character '*' to allow merging of all list policies.\n\nIf a policy is in the list and there's conflict between sources with:\n\n* The same scopes and level: The values merge into a new policy list.\n\n* Different scopes or level: The policy with the highest priority applies.\n\nIf a policy isn't in the list and there's conflict between sources, scopes, or level, the policy with the highest priority applies.";
PolicyRefreshRate.pfm_title = "Refresh rate for user policy";
PolicyRefreshRate.pfm_description = "Setting the policy specifies the period in milliseconds at which the device management service is queried for user policy information. Valid values range from 1,800,000 (30 minutes) to 86,400,000 (1 day). Values outside this range will be clamped to the respective boundary.\n\nLeaving the policy unset uses the default value of 3 hours.\n\nNote: Policy notifications force a refresh when the policy changes, making frequent refreshes unnecessary. So, if the platform supports these notifications, the refresh delay is 24 hours (ignoring defaults and the value of this policy).";
PopupsAllowedForUrls.pfm_title = "Allow pop-ups on these sites";
PopupsAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can open pop-ups.\n\nLeaving the policy unset means DefaultPopupsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
PopupsBlockedForUrls.pfm_title = "Block pop-ups on these sites";
PopupsBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can't open pop-ups.\n\nLeaving the policy unset means DefaultPopupsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
PostQuantumKeyAgreementEnabled.pfm_title = "Enable post-quantum key agreement for TLS";
PostQuantumKeyAgreementEnabled.pfm_description = "This policy configures whether Google Chrome will offer a post-quantum key agreement algorithm in TLS, using the ML-KEM NIST standard. Prior to Google Chrome 131, the algorithm was Kyber, an earlier draft iteration of the standard. This allows supporting servers to protect user traffic from being later decrypted by quantum computers.\n\nIf this policy is Enabled, Google Chrome will offer a post-quantum key agreement in TLS connections. User traffic will then be protected from quantum computers when communicating with compatible servers.\n\nIf this policy is Disabled, Google Chrome will not offer a post-quantum key agreement in TLS connections. User traffic will then be unprotected from quantum computers.\n\nIf this policy is not set, Google Chrome will follow the default rollout process for offering a post-quantum key agreement.\n\nOffering a post-quantum key agreement is backwards-compatible. Existing TLS servers and networking middleware are expected to ignore the new option and continue selecting previous options.\n\nHowever, devices that do not correctly implement TLS may malfunction when offered the new option. For example, they may disconnect in response to unrecognized options or the resulting larger messages. Such devices are not post-quantum-ready and will interfere with an enterprise's post-quantum transition. If encountered, administrators should contact the vendor for a fix.\n\nThis policy is a temporary measure and will be removed sometime after Google Chrome version 141. It may be Enabled to allow you to test for issues, and may be Disabled while issues are being resolved.";
PrefetchWithServiceWorkerEnabled.pfm_title = "Allow SpeculationRules prefetch to ServiceWorker-controlled URLs";
PrefetchWithServiceWorkerEnabled.pfm_description = "SpeculationRules prefetch can be issued to URLs that are controlled by\nServiceWorker. However, legacy code did not allow it and canceled the prefetch\nrequests. This policy enables to control the behavior.\n\nSetting this policy to Enabled or not set allows SpeculationRules prefetch to\nServiceWorker-controlled URLs (if the PrefetchServiceWorker feature flag is\nenabled). This is the current default behavior and is aligned with the\nspecifications.\n\nSetting this policy to Disabled disallows SpeculationRules prefetch to\nServiceWorker-controlled URLs. This is the legacy behavior.\n\nThis policy is intended to be temporary and will be removed in the future.";
PrintHeaderFooter.pfm_title = "Print Headers and Footers";
PrintHeaderFooter.pfm_description = "Setting the policy to Enabled turns headers and footers on in print preview. Setting the policy to Disabled turns them off in print preview.\n\nIf you set the policy, users can't change it. If unset, users decides whether headers and footers appear.";
PrintPdfAsImageAvailability.pfm_title = "Print PDF as Image Available";
PrintPdfAsImageAvailability.pfm_description = "Controls how Google Chrome makes the Print as image option available on Microsoft® Windows® and macOS when printing PDFs.\n\nWhen printing a PDF on Microsoft® Windows® or macOS, sometimes print jobs need to be rasterized to an image for certain printers to get correct looking output.\n\nWhen this policy is set to Enabled, Google Chrome will make the Print as image option available in the Print Preview when printing a PDF.\n\nWhen this policy is set to Disabled or not set Google Chrome the Print as image option will not be available to users in Print Preview and PDFs will be printed as usual without being rasterized to an image before being sent to the destination.";
PrintPdfAsImageDefault.pfm_title = "Print PDF as Image Default";
PrintPdfAsImageDefault.pfm_description = "Controls if Google Chrome makes the Print as image option default to set when printing PDFs.\n\nWhen this policy is set to Enabled, Google Chrome will default to setting the Print as image option in the Print Preview when printing a PDF.\n\nWhen this policy is set to Disabled or not set Google Chrome then the user selection for Print as image option will be initially unset.  The user will be allowed to select it for each individual PDFs print job, if the option is available.\n\nFor Microsoft® Windows® or macOS this policy only has an effect if PrintPdfAsImageAvailability is also enabled.";
PrintPreviewUseSystemDefaultPrinter.pfm_title = "Use System Default Printer as Default";
PrintPreviewUseSystemDefaultPrinter.pfm_description = "Setting the policy to Enabled means Google Chrome uses the OS default printer as the default destination for print preview.\n\nSetting the policy to Disabled or leaving it unset means Google Chrome uses the most recently used printer as the default destination for print preview.";
PrintRasterizePdfDpi.pfm_title = "Print Rasterize PDF DPI";
PrintRasterizePdfDpi.pfm_description = "Controls print image resolution when Google Chrome prints PDFs with rasterization.\n\nWhen printing a PDF using the Print to image option, it can be beneficial to specify a print resolution other than a device's printer setting or the PDF default.  A high resolution will significantly increase the processing and printing time while a low resolution can lead to poor imaging quality.\n\nThis policy allows a particular resolution to be specified for use when rasterizing PDFs for printing.\n\nIf this policy is set to zero or not set at all then the system default resolution will be used during rasterization of page images.";
PrinterTypeDenyList.pfm_title = "Disable printer types on the deny list";
PrinterTypeDenyList.pfm_description = "privet - Zeroconf-based (mDNS + DNS-SD) protocol destinations (Deprecated)\nextension - Extension-based destinations\npdf - The 'Save as PDF' destination, as well as the 'Save to Google Drive' destination on Google ChromeOS devices\nlocal - Local printer destinations\ncloud - Google Cloud Print (Deprecated)\nThe printers of types placed on the deny list will be disabled from being discovered or having their capabilities fetched.\n\nPlacing all printer types on the deny list effectively disables printing, as there would be no available destinations to send a document for printing.\n\nIn versions before 102, including cloud on the deny list has the same effect as setting the CloudPrintSubmitEnabled policy to false. In order to keep Google Cloud Print destinations discoverable, the CloudPrintSubmitEnabled policy must be set to true and cloud must not be on the deny list. Beginning in version 102, Google Cloud Print destinations are not supported and will not appear regardless of policy values.\n\nIf the policy is not set, or is set to an empty list, all printer types will be available for discovery.\n\nExtension printers are also known as print provider destinations, and include any destination that belongs to a Google Chrome extension.\n\nLocal printers are also known as native printing destinations, and include destinations available to the local machine and shared network printers.";
PrintingAllowedBackgroundGraphicsModes.pfm_title = "Restrict background graphics printing mode";
PrintingAllowedBackgroundGraphicsModes.pfm_description = "any - Allow printing both with and without background graphics\nenabled - Allow printing only with background graphics\ndisabled - Allow printing only without background graphics\nRestricts background graphics printing mode. Unset policy is treated as no restriction.";
PrintingBackgroundGraphicsDefault.pfm_title = "Default background graphics printing mode";
PrintingBackgroundGraphicsDefault.pfm_description = "enabled - Enable background graphics printing mode by default\ndisabled - Disable background graphics printing mode by default\nOverrides default background graphics printing mode.";
PrintingEnabled.pfm_title = "Enable printing";
PrintingEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset lets users print in Google Chrome, and users can't change this setting.\n\nSetting the policy to Disabled means users can't print from Google Chrome. Printing is off in the three dots menu, extensions, and JavaScript applications.";
PrintingPaperSizeDefault.pfm_title = "Default printing page size";
PrintingPaperSizeDefault.pfm_description = "Overrides default printing page size.\n\nname should contain one of the listed formats or 'custom' if required paper size is not in the list. If 'custom' value is provided custom_size property should be specified. It describes the desired height and width in micrometers. Otherwise custom_size property shouldn't be specified. Policy that violates these rules is ignored.\n\nIf the page size is unavailable on the printer chosen by the user this policy is ignored.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=PrintingPaperSizeDefault for more information about schema and formatting.";
PrivacySandboxAdMeasurementEnabled.pfm_title = "Choose whether the Privacy Sandbox ad measurement setting can be disabled";
PrivacySandboxAdMeasurementEnabled.pfm_description = "A policy to control whether the Privacy Sandbox Ad measurement setting can be disabled for your users.\n\nIf you set this policy to Disabled, then the Ad measurement setting will be turned off for your users.\nIf you set this policy to Enabled or keep it unset, your users will be able to turn on or off the Privacy Sandbox Ad measurement setting on their device.\n\nSetting this policy requires setting the PrivacySandboxPromptEnabled policy to Disabled.";
PrivacySandboxAdTopicsEnabled.pfm_title = "Choose whether the Privacy Sandbox Ad topics setting can be disabled";
PrivacySandboxAdTopicsEnabled.pfm_description = "A policy to control whether the Privacy Sandbox Ad topics setting can be disabled for your users.\n\nIf you set this policy to Disabled, then the Ad topics setting will be turned off for your users.\nIf you set this policy to Enabled or keep it unset, your users will be able to turn on or off the Privacy Sandbox Ad topics setting on their device.\n\nSetting this policy requires setting the PrivacySandboxPromptEnabled policy to Disabled.";
PrivacySandboxIpProtectionEnabled.pfm_title = "Choose whether the Privacy Sandbox IP Protection feature should be enabled.";
PrivacySandboxIpProtectionEnabled.pfm_description = "A policy to control whether the Privacy Sandbox IP Protection feature should be enabled.\n\nIP Protection is a feature that limits availability of a user's original IP address for certain third-party network requests made while browsing in Incognito mode, enhancing protections against cross-site tracking during Incognito browsing sessions.\n\nIf the policy is set to Disabled, then IP Protection will be disabled and users won't be able to enable the feature via UI settings.\nIf the policy is set to Enabled, then IP Protection will be enabled and users won't be able to disable the feature via UI settings.\nIf the policy is not set, IP Protection will be enabled by default and users will be able to control the feature on their device via UI settings.\n\nSome considerations regarding whether enterprises should disable IP Protection include:\n- DNS lookups won't be performed for requests that are proxied, which may impact DNS-based monitoring or filtering.\n- Enterprise applications may experience breakage when used in Incognito mode if they rely on requests to domains (or subdomains of those domains) on the Masked Domain List (Google Chrome) and require those requests to come from specific IP address ranges.\n- Traffic might not be proxied in Incognito mode under certain conditions, for example when users launch Incognito mode from a Chrome profile they aren't signed in to. In general the feature requires the user to have been signed in to Chrome with a personal Google account when launching Incognito mode.\n- The list of domains on the Masked Domain List may change over time, with new versions being pushed to users automatically. For more information on the Masked Domain List, see: Google Chrome.\n\nFor more information on IP Protection, see: Google Chrome.\n\nIP Protection will be launched no sooner than M139.";
PrivacySandboxPromptEnabled.pfm_title = "Choose whether the Privacy Sandbox prompt can be shown to your users";
PrivacySandboxPromptEnabled.pfm_description = "A policy to control whether your users see the Privacy Sandbox prompt.\nThe prompt is a user-blocking flow which informs your users of the Privacy Sandbox settings. See https://privacysandbox.com for details about Chrome’s effort to deprecate third-party cookies.\n\nIf you set this policy to Disabled, then Google Chrome won’t show the Privacy Sandbox prompt.\nIf you set this policy to Enabled or keep it unset, then Google Chrome determines whether the Privacy Sandbox prompt can be shown or not and then show it if possible.\n\nIf any of the following policies are set, it’s required to set this policy to Disabled:\nPrivacySandboxAdTopicsEnabled\nPrivacySandboxSiteEnabledAdsEnabled\nPrivacySandboxAdMeasurementEnabled";
PrivacySandboxSiteEnabledAdsEnabled.pfm_title = "Choose whether the Privacy Sandbox Site-suggested ads setting can be disabled";
PrivacySandboxSiteEnabledAdsEnabled.pfm_description = "A policy to control whether the Privacy Sandbox Site-suggested ads setting can be disabled for your users.\n\nIf you set this policy to Disabled, then the Site-suggested ads setting will be turned off for your users.\nIf you set this policy to Enabled or keep it unset, your users will be able to turn on or off the Privacy Sandbox Site-suggested ads setting on their device.\n\nSetting this policy requires setting the PrivacySandboxPromptEnabled policy to Disabled.";
ProfilePickerOnStartupAvailability.pfm_title = "Profile picker availability on startup";
ProfilePickerOnStartupAvailability.pfm_description = "0 - Profile picker available at startup\n1 - Profile picker disabled at startup\n2 - Profile picker forced at startup\nSpecifies whether the profile picker is enabled, disabled or forced at the browser startup.\n\nBy default the profile picker is not shown if the browser starts in guest or incognito mode, a profile directory and/or urls are specified by command line, an app is explicitly requested to open, the browser was launched by a native notification, there is only one profile available or the policy ForceBrowserSignin is set to true.\n\nIf 'Enabled' (0) is selected or the policy is left unset, the profile picker will be shown at startup by default, but users will be able to enable/disable it.\n\nIf 'Disabled' (1) is selected, the profile picker will never be shown, and users will not be able to change the setting.\n\nIf 'Forced' (2) is selected, the profile picker cannot be suppressed by the user. The profile picker will be shown even if there is only one profile available.";
ProfileReauthPrompt.pfm_title = "Prompt users to re-authenticate to the profile";
ProfileReauthPrompt.pfm_description = "0 - Do not prompt for reauth\n1 - Prompt for reauth in a tab\nWhen set to DoNotPrompt or left unset, Google Chrome does not automatically prompt the user to re-authenticate to the browser.\n\nWhen set to PromptInTab, when the user's authentication expires, immediately open a new tab with the Google login page. This only happens if using Chrome Sync.";
ProfileSeparationDomainExceptionList.pfm_title = "Enterprise profile separation secondary domain allowlist";
ProfileSeparationDomainExceptionList.pfm_description = "If this policy is unset, account logins will not be required to create a new separate profile.\n\nIf this policy is set, account logins from the listed domains will not be required to create a new separate profile.\n\nThis policy can be set to an empty string so that all account logins are required to create a new separate profile.";
PromotionsEnabled.pfm_title = "Enable showing promotional content";
PromotionsEnabled.pfm_description = "Setting the policy to True or leaving it unset lets Google Chrome show users product promotional content.\n\nSetting the policy to False prevents Google Chrome from showing product promotional content.\n\nSetting the policy controls the presentation of promotional content, including the welcome pages that help users sign in to Google Chrome, set Google Chrome as users' default browser, or otherwise inform them of product features.";
PromptForDownloadLocation.pfm_title = "Ask where to save each file before downloading";
PromptForDownloadLocation.pfm_description = "Setting the policy to Enabled means users are asked where to save each file before downloading. Setting the policy to Disabled has downloads start immediately, and users aren't asked where to save the file.\n\nLeaving the policy unset lets users change this setting.";
PromptOnMultipleMatchingCertificates.pfm_title = "Prompt when multiple certificates match";
PromptOnMultipleMatchingCertificates.pfm_description = "This policy controls whether the user is prompted to select a client certificate when more than one certificate matches AutoSelectCertificateForUrls.\nIf this policy is set to Enabled, the user is prompted to select a client certificate whenever the auto-selection policy matches multiple certificates.\nIf this policy is set to Disabled or not set, the user may only be prompted when no certificate matches the auto-selection.";
ProxySettings.pfm_title = "Proxy settings";
ProxySettings.pfm_description = "Setting the policy configures the proxy settings for Chrome and ARC-apps, which ignore all proxy-related options specified from the command line.\n\nLeaving the policy unset lets users choose their proxy settings.\n\nSetting the ProxySettings policy accepts the following fields:\n\n* ProxyMode, which lets you specify the proxy server Chrome uses and prevents users from changing proxy settings\n\n* ProxyPacUrl, a URL to a proxy .pac file, or a PAC script encoded as a data URL with MIME type application/x-ns-proxy-autoconfig\n\n* ProxyPacMandatory, which prevents the network stack from falling back to direct connections with invalid or unavailable PAC script\n\n* ProxyServer, a URL of the proxy server\n\n* ProxyBypassList, a list of hosts for which the proxy will be bypassed\n\nThe ProxyServerMode field is deprecated in favor of the ProxyMode field. For ProxyMode, if you choose the value:\n\n* direct, a proxy is never used and all other fields are ignored.\n\n* system, the systems's proxy is used and all other fields are ignored.\n\n* auto_detect, all other fields are ignored.\n\n* fixed_servers, the ProxyServer and ProxyBypassList fields are used.\n\n* pac_script, the ProxyPacUrl, ProxyPacMandatory and ProxyBypassList fields are used.\n\nNote: For more detailed examples, visit The Chromium Projects ( https://www.chromium.org/developers/design-documents/network-settings/#command-line-options-for-proxy-settings ).\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=ProxySettings for more information about schema and formatting.";
QRCodeGeneratorEnabled.pfm_title = "Enable QR Code Generator";
QRCodeGeneratorEnabled.pfm_description = "This policy enables the QR Code generator feature in Google Chrome.\n\nIf you enable this policy or don't configure it, the QR Code Generator feature is enabled.\n\nIf you disable this policy, the QR Code Generator feature is disabled.";
QuicAllowed.pfm_title = "Allow QUIC protocol";
QuicAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset allows the use of QUIC protocol in Google Chrome.\n\nSetting the policy to Disabled disallows the use of QUIC protocol.";
ReduceAcceptLanguageEnabled.pfm_title = "Control Accept-Language Reduction";
ReduceAcceptLanguageEnabled.pfm_description = "The Accept-Language HTTP request header and the JavaScript navigator.languages getter are planned for reduction for privacy reasons.\nTo facilitate testing and ensure compatibility, this policy allows you to enable or disable the Accept-Language Reduction feature.\n\nIf this policy is set to enabled or left unset, Accept-Language Reduction will be applied through field trials.\nIf this policy is set to disabled, field trials will not be able to activate Accept-Language Reduction.\n\nFor more information about this feature, please visit: https://github.com/explainers-by-googlers/reduce-accept-language.\n\nNOTE: Only newly-started renderer processes will reflect changes to this policy while the browser is running.";
RegisteredProtocolHandlers.pfm_title = "Register protocol handlers";
RegisteredProtocolHandlers.pfm_description = "Setting the policy (as recommended only) lets you register a list of protocol handlers, which merge with the ones that the user registers, putting both sets in use. Set the property \"protocol\" to the scheme, such as \"mailto\", and set the property \"URL\" to the URL pattern of the application that handles the scheme specified in the \"protocol\" field. The pattern can include a \"%s\" placeholder, which the handled URL replaces.\n\nUsers can't remove a protocol handler registered by policy. However, by installing a new default handler, they can change the protocol handlers installed by policy.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=RegisteredProtocolHandlers for more information about schema and formatting.";
RelatedWebsiteSetsEnabled.pfm_title = "Enable Related Website Sets";
RelatedWebsiteSetsEnabled.pfm_description = "This policy allows to control the Related Website Sets feature enablement.\n\nThis policy overrides the FirstPartySetsEnabled policy.\n\nWhen this policy is unset or set to True, the Related Website Sets feature is enabled.\n\nWhen this policy is set to False, the Related Website Sets feature is disabled.";
RelatedWebsiteSetsOverrides.pfm_title = "Override Related Website Sets.";
RelatedWebsiteSetsOverrides.pfm_description = "This policy provides a way to override the list of sets the browser uses for Related Website Sets features.\n\nThis policy overrides the FirstPartySetsOverrides policy.\n\nEach set in the browser's list of Related Website Sets must meet the requirements of a Related Website Set.\nA Related Website Set must contain a primary site and one or more member sites.\nA set can also contain a list of service sites that it owns, as well as a map from a site to all of its ccTLD variants.\nSee https://github.com/WICG/first-party-sets for more information on how Google Chrome uses Related Website Sets.\n\n\nAll sites in a Related Website Set must be a registrable domain served over HTTPS. Each site in a Related Website Set must also be unique,\nmeaning a site cannot be listed more than once in a Related Website Set.\n\nWhen this policy is given an empty dictionary, the browser uses the public list of Related Website Sets.\n\nFor all sites in a Related Website Set from the replacements list, if a site is also present\non a Related Website Set in the browser's list, then that site will be removed from the browser's Related Website Set.\nAfter this, the policy's Related Website Set will be added to the browser's list of Related Website Sets.\n\nFor all sites in a Related Website Set from the additions list, if a site is also present\non a Related Website Set in the browser's list, then the browser's Related Website Set will be updated so that the\nnew Related Website Set can be added to the browser's list. After the browser's list has been updated,\nthe policy's Related Website Set will be added to the browser's list of Related Website Sets.\n\nThe browser's list of Related Website Sets requires that for all sites in its list, no site is in\nmore than one set. This is also required for both the replacements list\nand the additions list. Similarly, a site cannot be in both the\nreplacements list and the additions list.\n\nWildcards (*) are not supported as a policy value, nor within any Related Website Set in these lists.\n\nAll sets provided by the policy must be valid Related Website Sets, if they aren't then an\nappropriate error will be outputted.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=RelatedWebsiteSetsOverrides for more information about schema and formatting.";
RelaunchFastIfOutdated.pfm_title = "Time period (days)";
RelaunchFastIfOutdated.pfm_description = "Specifies the minimum release age beyond which relaunch notifications are more aggressive. The age is calculated from the time the currently-running version was last served to clients.\n\nIf a browser relaunch or device restart is needed to finalize a pending update and the current version has been outdated for more than the number of days specified by this setting, the RelaunchNotificationPeriod policy is overridden to 2 hours. If the RelaunchNotification policy is set to 1 ('Required'), users will be forced to relaunch or restart at the end of the period.\n\nIf not set, or if the release age cannot be determined, the RelaunchNotificationPeriod policy will be used for all updates.";
RelaunchNotification.pfm_title = "Notify a user that a browser relaunch or device restart is recommended or required";
RelaunchNotification.pfm_description = "1 - Show a recurring prompt to the user indicating that a relaunch is recommended\n2 - Show a recurring prompt to the user indicating that a relaunch is required\nNotify users that Google Chrome must be relaunched or Google ChromeOS must be restarted to apply a pending update.\n\nThis policy setting enables notifications to inform the user that a browser relaunch or device restart is recommended or required. If not set, Google Chrome indicates to the user that a relaunch is needed via subtle changes to its menu, while Google ChromeOS indicates such via a notification in the system tray. If set to 'Recommended', a recurring warning will be shown to the user that a relaunch is recommended. The user can dismiss this warning to defer the relaunch. If set to 'Required', a recurring warning will be shown to the user indicating that a browser relaunch will be forced once the notification period passes. The default period is seven days for Google Chrome and four days for Google ChromeOS, and may be configured via the RelaunchNotificationPeriod policy setting.\n\nThe user's session is restored following the relaunch/restart.";
RelaunchNotificationPeriod.pfm_title = "Time period (milliseconds)";
RelaunchNotificationPeriod.pfm_description = "Allows you to set the time period, in milliseconds, over which users are notified that Google Chrome must be relaunched or that a Google ChromeOS device must be restarted to apply a pending update.\n\nOver this time period, the user will be repeatedly informed of the need for an update. For Google ChromeOS devices, a restart notification appears in the system tray according to the RelaunchHeadsUpPeriod policy. For Google Chrome browsers, the app menu changes to indicate that a relaunch is needed once one third of the notification period passes. This notification changes color once two thirds of the notification period passes, and again once the full notification period has passed. The additional notifications enabled by the RelaunchNotification policy follow this same schedule.\n\nIf not set, the default period of 604800000 milliseconds (one week) is used.";
RelaunchWindow.pfm_title = "Relaunch time window";
RelaunchWindow.pfm_description = "Specify a target time window for the end of the relaunch notification period.\n\nUsers are notified of the need for a browser relaunch or device restart based on the RelaunchNotification and RelaunchNotificationPeriod policy settings. Browsers and devices are forcibly restarted at the end of the notification period when the RelaunchNotification policy is set to 'Required'. This RelaunchWindow policy can be used to defer the end of the notification period so that it falls within a specific time window.\n\nIf this policy is not set, the default target time window for Google ChromeOS is between 2 AM and 4 AM. The default target time window for Google Chrome is the whole day (i.e., the end of the notification period is never deferred).\n\nNote: Though the policy can accept multiple items in entries, all but the first item are ignored.\nWarning: Setting this policy may delay application of software updates.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=RelaunchWindow for more information about schema and formatting.";
RemoteAccessHostAllowClientPairing.pfm_title = "Enable or disable PIN-less authentication for remote access hosts";
RemoteAccessHostAllowClientPairing.pfm_description = "Setting the policy to Enabled or leaving it unset lets users pair clients and hosts at connection time, eliminating the need to enter a PIN every time.\n\nSetting the policy to Disabled makes this feature unavailable.";
RemoteAccessHostAllowFileTransfer.pfm_title = "Allow remote access users to transfer files to/from the host";
RemoteAccessHostAllowFileTransfer.pfm_description = "Setting the policy to Enabled or leaving it unset allows users connected to a remote access host to transfer files between the client and the host. This doesn't apply to remote assistance connections, which don't support file transfer.\n\nSetting the policy to Disabled disallows file transfer.";
RemoteAccessHostAllowPinAuthentication.pfm_title = "Allow PIN and pairing authentication methods for remote access hosts";
RemoteAccessHostAllowPinAuthentication.pfm_description = "Setting the policy to Enabled allows the remote access host to use PIN and pairing authentications when accepting client connections.\n\nSetting the policy to Disabled disallows PIN or pairing authentications.\n\nLeaving it unset lets the host decide whether PIN and/or pairing authentications can be used.\n\nNote: If the setting results in no mutually supported authentication methods by both the host and the client, then the connection will be rejected.";
RemoteAccessHostAllowRelayedConnection.pfm_title = "Enable the use of relay servers by the remote access host";
RemoteAccessHostAllowRelayedConnection.pfm_description = "If RemoteAccessHostFirewallTraversal is set to Enabled, setting RemoteAccessHostAllowRelayedConnection to Enabled or leaving it unset allows the use of remote clients to use relay servers to connect to this machine when a direct connection is not available, for example, because of firewall restrictions.\n\nSetting the policy to Disabled doesn't turn remote access off, but only allows connections from the same network (not NAT traversal or relay).";
RemoteAccessHostAllowRemoteAccessConnections.pfm_title = "Allow remote access connections to this machine";
RemoteAccessHostAllowRemoteAccessConnections.pfm_description = "If this policy is Disabled, the remote access host service cannot be started or configured to accept incoming connections.  This policy does not affect remote support scenarios.\n\nThis policy has no effect if it is set to Enabled, left empty, or is not set.";
RemoteAccessHostAllowRemoteSupportConnections.pfm_title = "Allow remote support connections to this machine";
RemoteAccessHostAllowRemoteSupportConnections.pfm_description = "If this policy is disabled, the remote support host cannot be started or configured to accept incoming connections.\n\nThis policy does not affect remote access scenarios.\n\nThis policy does not prevent enterprise admins from connecting to managed Google ChromeOS devices.\n\nThis policy has no effect if enabled, left empty, or is not set.";
RemoteAccessHostAllowUrlForwarding.pfm_title = "Allow remote access users to open host-side URLs in their local client browser";
RemoteAccessHostAllowUrlForwarding.pfm_description = "Setting the policy to Enabled or leaving it unset may allow users connected to a remote access host to open host-side URLs in their local client browser.\n\nSetting the policy to Disabled will prevent the remote access host from sending URLs to the client.\n\nThis setting doesn't apply to remote assistance connections as the feature is not supported for that connection mode.\n\nNote: This feature is not yet generally available so enabling it does not mean that the feature will be visible in the client UI.";
RemoteAccessHostClientDomainList.pfm_title = "Configure the required domain names for remote access clients";
RemoteAccessHostClientDomainList.pfm_description = "Setting the policy specifies the client domain names that are imposed on remote access clients, and users can't change them. Only clients from one of the specified domains can connect to the host.\n\nSetting the policy to an empty list or leaving it unset applies the default policy for the connection type. For remote assistance, this allows clients from any domain to connect to the host. For anytime remote access, only the host owner can connect.\n\nSee also RemoteAccessHostDomainList.\n\nNote: This setting overrides RemoteAccessHostClientDomain, if present.";
RemoteAccessHostClipboardSizeBytes.pfm_title = "The maximum size, in bytes, that can be transferred between client and host via clipboard synchronization";
RemoteAccessHostClipboardSizeBytes.pfm_description = "If this policy is set, clipboard data sent to and from the host will be truncated to the limit set by this policy.\n\nIf a value of 0 is set, then clipboard sync is disabled.\n\nThis policy affects both remote access and remote support scenarios.\n\nThis policy has no effect if it is not set.\n\nSetting the policy to a value that is not within the min/max range may prevent the host from starting.\n\nPlease note that the actual upper bound for the clipboard size is based on the maximum WebRTC data channel message size which this policy does not control.";
RemoteAccessHostDomainList.pfm_title = "Configure the required domain names for remote access hosts";
RemoteAccessHostDomainList.pfm_description = "Setting the policy specifies the host domain names that are imposed on remote access hosts, and users can't change them. Hosts can be shared only using accounts registered on one of the specified domain names.\n\nSetting the policy to an empty list or leaving it unset means hosts can be shared using any account.\n\nSee also RemoteAccessHostClientDomainList.\n\nNote: This setting will override RemoteAccessHostDomain, if present.";
RemoteAccessHostFirewallTraversal.pfm_title = "Enable firewall traversal from remote access host";
RemoteAccessHostFirewallTraversal.pfm_description = "Setting the policy to Enabled or leaving it unset allows the usage of STUN servers, letting remote clients discover and connect to this machine, even if separated by a firewall.\n\nSetting the policy to Disabled when outgoing UDP connections are filtered by the firewall means the machine only allows connections from client machines within the local network.";
RemoteAccessHostMatchUsername.pfm_title = "Require that the name of the local user and the remote access host owner match";
RemoteAccessHostMatchUsername.pfm_description = "Setting the policy to Enabled has the remote access host compare the name of the local user the host is associated with and the name of the Google Account registered as the host owner (\"johndoe,\" if the host is owned by \"<EMAIL>\"). This host won't start if the host owner's name differs from the name of the local user that the host is associated with. To enforce that the owner's Google Account is associated with a specific domain, use the policy with RemoteAccessHostDomain.\n\nSetting the policy to Disabled or leaving it unset means the remote access host can be associated with any local user.";
RemoteAccessHostMaximumSessionDurationMinutes.pfm_title = "Maximum session duration allowed for remote access connections";
RemoteAccessHostMaximumSessionDurationMinutes.pfm_description = "If this policy is set, remote access connections will automatically disconnect after the number of minutes defined in the policy have elapsed. This does not prevent the client from reconnecting after the maximum session duration has been reached. Setting the policy to a value that is not within the min/max range may prevent the host from starting. This policy does not affect remote support scenarios.\n\nThis policy has no effect if it is not set. In this case, remote access connections will have no maximum duration on this machine.";
RemoteAccessHostRequireCurtain.pfm_title = "Enable curtaining of remote access hosts";
RemoteAccessHostRequireCurtain.pfm_description = "Setting the policy to Enabled turns off remote access hosts' physical input and output devices during a remote connection.\n\nSetting the policy to Disabled or leaving it unset lets both local and remote users interact with the host while it's shared.";
RemoteAccessHostUdpPortRange.pfm_title = "Restrict the UDP port range used by the remote access host";
RemoteAccessHostUdpPortRange.pfm_description = "Setting the policy restricts the UDP port range used by the remote access host in this machine.\n\nLeaving the policy unset or set to an empty string means the remote access host can use any available port.\n\nNote: If RemoteAccessHostFirewallTraversal is Disabled, the remote access host will use UDP ports in the 12400-12409 range.";
RemoteDebuggingAllowed.pfm_title = "Allow remote debugging";
RemoteDebuggingAllowed.pfm_description = "Controls whether users may use remote debugging.\n\nIf this policy is set to Enabled or not set, users may use remote debugging by specifying --remote-debugging-port and --remote-debugging-pipe command line switches.\n\nIf this policy is set to Disabled, users are not allowed to use remote debugging.";
RequireOnlineRevocationChecksForLocalAnchors.pfm_title = "Require online OCSP/CRL checks for local trust anchors";
RequireOnlineRevocationChecksForLocalAnchors.pfm_description = "Setting the policy to True means Google Chrome always performs revocation checking for successfully validated server certificates signed by locally installed CA certificates. If Google Chrome can't get revocation status information, Google Chrome treats these certificates as revoked (hard-fail).\n\nSetting the policy to False or leaving it unset means Google Chrome uses existing online revocation-checking settings.\n\nOn macOS, this policy has no effect if the ChromeRootStoreEnabled policy is set to False.";
RestoreOnStartup.pfm_title = "Action on startup";
RestoreOnStartup.pfm_description = "5 - Open New Tab Page\n1 - Restore the last session\n4 - Open a list of URLs\n6 - Open a list of URLs and restore the last session\nSetting the policy lets you specify system behavior on startup. Turning this setting off amounts to leaving it unset as Google Chrome must have specified start up behavior.\n\nIf you set the policy, users can't change it in Google Chrome. If not set, users can change it.\n\nSetting this policy to RestoreOnStartupIsLastSession or RestoreOnStartupIsLastSessionAndURLs turns off some settings that rely on sessions or that perform actions on exit, such as clearing browsing data on exit or session-only cookies.\n\nIf this policy is set to RestoreOnStartupIsLastSessionAndURLs, browser will restore previous session and open a separate window to show URLs that are set from RestoreOnStartupURLs. Note that users can choose to keep those URLs open and they will also be restored in the future session.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
RestoreOnStartupURLs.pfm_title = "URLs to open on startup";
RestoreOnStartupURLs.pfm_description = "If RestoreOnStartup is set to RestoreOnStartupIsURLs, then setting RestoreOnStartupURLs to a list of URLs specify which URLs open.\n\nIf not set, the New Tab page opens on start up.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.";
RestrictSigninToPattern.pfm_title = "Restrict which Google accounts are allowed to be set as browser primary accounts in Google Chrome";
RestrictSigninToPattern.pfm_description = "Contains a regular expression which is used to determine which Google accounts can be set as browser primary accounts in Google Chrome (i.e. the account that is chosen during the Sync opt-in flow).\n\nAn appropriate error is displayed if a user tries to set a browser primary account with a username that does not match this pattern.\n\nIf this policy is left not set or blank, then the user can set any Google account as a browser primary account in Google Chrome.";
RoamingProfileLocation.pfm_title = "Set the roaming profile directory";
RoamingProfileLocation.pfm_description = "Configures the directory that Google Chrome will use for storing the roaming copy of the profiles.\n\nIf you set this policy, Google Chrome will use the provided directory to store the roaming copy of the profiles if the RoamingProfileSupportEnabled policy has been enabled. If the RoamingProfileSupportEnabled policy is disabled or left unset the value stored in this policy is not used.\n\nSee https://www.chromium.org/administrators/policy-list-3/user-data-directory-variables for a list of variables that can be used.\n\nOn non-Windows platforms, this policy must be set for roaming profiles to work.\n\nOn Windows, if this policy is left unset, the default roaming profile path will be used.";
RoamingProfileSupportEnabled.pfm_title = "Enable the creation of roaming copies for Google Chrome profile data.";
RoamingProfileSupportEnabled.pfm_description = "If you enable this setting, the settings stored in Google Chrome profiles like bookmarks, autofill data, passwords, etc. will also be written to a file stored in the Roaming user profile folder or a location specified by the Administrator through the RoamingProfileLocation policy. Enabling this policy disables cloud sync.\n\nIf this policy is disabled or left not set only the regular local profiles will be used.";
SSLErrorOverrideAllowed.pfm_title = "Allow proceeding from the SSL warning page";
SSLErrorOverrideAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset lets users click through warning pages Google Chrome shows when users navigate to sites that have SSL errors.\n\nSetting the policy to Disabled prevent users from clicking through any warning pages.";
SSLErrorOverrideAllowedForOrigins.pfm_title = "Allow proceeding from the SSL warning page on specific origins";
SSLErrorOverrideAllowedForOrigins.pfm_description = "If SSLErrorOverrideAllowed is Disabled, setting the policy lets you set a list of origin patterns that specify the sites where a user can click through warning pages Google Chrome shows when users navigate to sites that have SSL errors. Users will not be able to click through SSL warning pages on origins that are not on this list.\n\nIf SSLErrorOverrideAllowed is Enabled or unset, this policy does nothing.\n\nLeaving the policy unset means SSLErrorOverrideAllowed applies for all sites.\n\nFor detailed information on valid input patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy. This policy only matches based on origin, so any path in the URL pattern is ignored.";
SafeBrowsingAllowlistDomains.pfm_title = "Configure the list of domains on which Safe Browsing will not trigger warnings.";
SafeBrowsingAllowlistDomains.pfm_description = "Setting the policy to Enabled means Safe Browsing will trust the domains you designate. It won't check them for dangerous resources such as phishing, malware, or unwanted software. Safe Browsing's download protection service won't check downloads hosted on these domains. Its password protection service won't check for password reuse.\n\nLeaving the policy unset means default Safe Browsing protection applies to all resources.\n\nThis policy does not support regular expressions; however, subdomains of a given domain are allowlisted. Fully qualified domain names (FQDNs) are not required.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.";
SafeBrowsingDeepScanningEnabled.pfm_title = "Allow download deep scanning for Safe Browsing-enabled users";
SafeBrowsingDeepScanningEnabled.pfm_description = "When this policy is enabled or left unset, Google Chrome can send suspicious downloads from Safe Browsing-enabled users to Google to scan for malware, or prompt users to provide a password for encrypted archives.\nWhen this policy is disabled, this scanning will not be performed.\nThis policy does not impact download content analysis configured by Chrome Enterprise Connectors.";
SafeBrowsingExtendedReportingEnabled.pfm_title = "Enable Safe Browsing Extended Reporting";
SafeBrowsingExtendedReportingEnabled.pfm_description = "Setting the policy to Enabled turns on Google Chrome's Safe Browsing Extended Reporting, which sends some system information and page content to Google servers to help detect dangerous apps and sites.\n\nSetting the policy to Disabled means reports are never sent.\n\nIf you set this policy, users can't change it. If not set, users can decide whether to send reports or not.\n\nSee more about Safe Browsing ( https://developers.google.com/safe-browsing ).";
SafeBrowsingProtectionLevel.pfm_title = "Safe Browsing Protection Level";
SafeBrowsingProtectionLevel.pfm_description = "0 - Safe Browsing is never active.\n1 - Safe Browsing is active in the standard mode.\n2 - Safe Browsing is active in the enhanced mode. This mode provides better security, but requires sharing more browsing information with Google.\nAllows you to control whether Google Chrome's Safe Browsing feature is enabled and the mode it operates in.\n\nIf this policy is set to 'NoProtection' (value 0), Safe Browsing is never active.\n\nIf this policy is set to 'StandardProtection' (value 1, which is the default), Safe Browsing is always active in the standard mode.\n\nIf this policy is set to 'EnhancedProtection' (value 2), Safe Browsing is always active in the enhanced mode, which provides better security, but requires sharing more browsing information with Google.\n\nIf you set this policy as mandatory, users cannot change or override the Safe Browsing setting in Google Chrome.\n\nIf this policy is left not set, Safe Browsing will operate in Standard Protection mode but users can change this setting.\n\nSee https://support.google.com/chrome?p=safe_browsing_preferences for more info on Safe Browsing.";
SafeBrowsingProxiedRealTimeChecksAllowed.pfm_title = "Allow Safe Browsing Proxied Real Time Checks";
SafeBrowsingProxiedRealTimeChecksAllowed.pfm_description = "This controls whether Safe Browsing's standard protection mode is allowed to\nsend partial hashes of URLs to Google through a proxy via Oblivious HTTP\nin order to determine whether they are safe to visit.\n\nThe proxy allows browsers to upload partial hashes of URLs to Google\nwithout them being linked to the user's IP address. The policy also allows\nbrowsers to upload the partial hashes of URLs with higher frequency for\nbetter Safe Browsing protection quality.\n\nThis policy will be ignored if Safe Browsing is disabled or set to enhanced\nprotection mode.\n\nSetting the policy to Enabled or leaving it unset allows the\nhigher-protection proxied lookups.\n\nSetting the policy to Disabled disallows the higher-protection proxied\nlookups. Partial hashes of URLs will be uploaded to Google directly with much\nlower frequency, which will degrade protection.";
SafeBrowsingSurveysEnabled.pfm_title = "Allow Safe Browsing Surveys";
SafeBrowsingSurveysEnabled.pfm_description = "When this policy is enabled or left unset, the user may receive surveys related to Safe Browsing.\nWhen this policy is disabled, the user will not receive surveys related to Safe Browsing.";
SafeSitesFilterBehavior.pfm_title = "Control SafeSites adult content filtering.";
SafeSitesFilterBehavior.pfm_description = "0 - Do not filter sites for adult content\n1 - Filter sites for adult content\nSetting the policy controls the SafeSites URL filter, which uses the Google Safe Search API to classify URLs as pornographic or not.\n\nWhen this policy is set to:\n\n* Do not filter sites for adult content, or not set, sites aren't filtered\n\n* Filter sites for adult content, pornographic sites are filtered\n\nThe policy applies to both the URL the user navigates to and to iframes. The URLAllowlist policy takes precedence over this policy and can be used to override verdicts from the Google Safe Search API.";
SameOriginTabCaptureAllowedByOrigins.pfm_title = "Allow Same Origin Tab capture by these origins";
SameOriginTabCaptureAllowedByOrigins.pfm_description = "Setting the policy lets you set a list of URL patterns that can capture tabs with their same Origin.\n\nLeaving the policy unset means that sites will not be considered for an override at this level of capture.\n\nNote that windowed Chrome Apps with the same origin as this site will still be allowed to be captured.\n\nIf a site matches a URL pattern in this policy, the following policies will not be considered: TabCaptureAllowedByOrigins, WindowCaptureAllowedByOrigins, ScreenCaptureAllowedByOrigins, ScreenCaptureAllowed.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.  This policy only matches based on origin, so any path in the URL pattern is ignored.";
SandboxExternalProtocolBlocked.pfm_title = "Allow Chrome to block navigations toward external protocols in sandboxed iframes";
SandboxExternalProtocolBlocked.pfm_description = "Chrome will block navigations toward external protocols inside\nsandboxed iframe. See https://chromestatus.com/features/5680742077038592.\n\nWhen True, this lets Chrome blocks those navigations.\n\nWhen False, this prevents Chrome from blocking those navigations.\n\nThis defaults to True: security feature enabled.\n\nThis can be used by administrators who need more time to update their internal website affected by this new restriction. This Enterprise policy is temporary; it's intended to be removed after Google Chrome version 117.\n";
SavingBrowserHistoryDisabled.pfm_title = "Disable saving browser history";
SavingBrowserHistoryDisabled.pfm_description = "Setting the policy to Enabled means browsing history is not saved, tab syncing is off and users can't change this setting.\n\nSetting the policy to Disabled or leaving it unset saves browsing history.";
ScreenCaptureAllowed.pfm_title = "Allow or deny screen capture";
ScreenCaptureAllowed.pfm_description = "If enabled or not configured (default), a Web page can use\nscreen-share APIs (e.g., getDisplayMedia() or the Desktop Capture extension API)\nto prompt the user to select a tab, window or desktop to capture.\n\nWhen this policy is disabled, any calls to screen-share APIs will fail\nwith an error; however this policy is not considered (and a site will be\nallowed to use screen-share APIs) if the site matches an origin pattern in\nany of the following policies:\nScreenCaptureAllowedByOrigins,\nWindowCaptureAllowedByOrigins,\nTabCaptureAllowedByOrigins,\nSameOriginTabCaptureAllowedByOrigins.\n";
ScreenCaptureAllowedByOrigins.pfm_title = "Allow Desktop, Window, and Tab capture by these origins";
ScreenCaptureAllowedByOrigins.pfm_description = "Setting the policy lets you set a list of URL patterns that can use Desktop, Window, and Tab Capture.\n\nLeaving the policy unset means that sites will not be considered for an override at this level of Capture.\n\nThis policy is not considered if a site matches a URL pattern in any of the following policies: WindowCaptureAllowedByOrigins, TabCaptureAllowedByOrigins, SameOriginTabCaptureAllowedByOrigins.\n\nIf a site matches a URL pattern in this policy, the ScreenCaptureAllowed will not be considered.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.  This policy only matches based on origin, so any path in the URL pattern is ignored.";
ScreenCaptureWithoutGestureAllowedForOrigins.pfm_title = "Allow screen capture without prior user gesture";
ScreenCaptureWithoutGestureAllowedForOrigins.pfm_description = "For security reasons, the\ngetDisplayMedia() web API requires\na prior user gesture (\"transient activation\") to be called or will otherwise\nfail.\n\nWith this policy set, admins can specify origins on which this API can be\ncalled without prior user gesture.\n\nFor detailed information on valid url patterns, please see\nhttps://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is\nnot an accepted value for this policy.\n\nIf this policy is unset, all origins will require a prior user gesture to call\nthis API.";
ScrollToTextFragmentEnabled.pfm_title = "Enable scrolling to text specified in URL fragments";
ScrollToTextFragmentEnabled.pfm_description = "This feature allows for hyperlinks and address bar URL navigations to target specific text within a web page, which will be scrolled to once the loading of the web page is complete.\n\nIf you enable or don't configure this policy, web page scrolling to specific text fragments via URL will be enabled.\n\nIf you disable this policy, web page scrolling to specific text fragments via URL will be disabled.";
SearchSuggestEnabled.pfm_title = "Enable search suggestions";
SearchSuggestEnabled.pfm_description = "Setting the policy to True turns on search suggestions in Google Chrome's address bar. Setting the policy to False turns off these search suggestions.\n\nSuggestions based on bookmarks or history are unaffected by the policy.\n\nIf you set the policy, users can't change it. If not set, search suggestions are on at first, but users can turn them off any time.";
SecurityKeyPermitAttestation.pfm_title = "URLs/domains automatically permitted direct Security Key attestation";
SecurityKeyPermitAttestation.pfm_description = "Setting the policy specifies WebAuthn RP IDs for which no prompt appears when attestation certificates from security keys are requested. A signal is also sent to the security key indicating that enterprise attestation may be used. Without this, when sites request attestation of security keys, users are prompted in Google Chrome version 65 and later.";
SensorsAllowedForUrls.pfm_title = "Allow access to sensors on these sites";
SensorsAllowedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can access sensors like motion and light sensors.\n\nLeaving the policy unset means DefaultSensorsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nIf the same URL pattern exists in both this policy and the SensorsBlockedForUrls policy, the latter is prioritized and access to motion or light sensors will be blocked.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
SensorsBlockedForUrls.pfm_title = "Block access to sensors on these sites";
SensorsBlockedForUrls.pfm_description = "Setting the policy lets you set a list of URL patterns that specify the sites that can't access sensors like motion and light sensors.\n\nLeaving the policy unset means DefaultSensorsSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nIf the same URL pattern exists in both this policy and the SensorsAllowedForUrls policy, this policy is prioritized and access to motion or light sensors will be blocked.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed.";
SerialAllowAllPortsForUrls.pfm_title = "Automatically grant permission to sites to connect all serial ports.";
SerialAllowAllPortsForUrls.pfm_description = "Setting the policy allows you to list sites which are automatically granted permission to access all available serial ports.\n\nThe URLs must be valid, otherwise the policy is ignored. Only the origin (scheme, host and port) of the URL is considered.\n\nOn Google ChromeOS, this policy only applies to affiliated users.\n\nThis policy overrides DefaultSerialGuardSetting, SerialAskForUrls, SerialBlockedForUrls and the user's preferences.";
SerialAllowUsbDevicesForUrls.pfm_title = "Automatically grant permission to sites to connect to USB serial devices.";
SerialAllowUsbDevicesForUrls.pfm_description = "Setting the policy allows you to list sites which are automatically granted permission to access USB serial devices with vendor and product IDs matching the vendor_id and product_id fields. Omitting the product_id field allows the given sites permission to access devices with a vendor ID matching the vendor_id field and any product ID.\n\nThe URLs must be valid, otherwise the policy is ignored. Only the origin (scheme, host and port) of the URL is considered.\n\nOn ChromeOS, this policy only applies to affiliated users.\n\nThis policy overrides DefaultSerialGuardSetting, SerialAskForUrls, SerialBlockedForUrls and the user's preferences.\n\nThis policy only affects access to USB devices through the Web Serial API. To grant access to USB devices through the WebUSB API see the WebUsbAllowDevicesForUrls policy.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=SerialAllowUsbDevicesForUrls for more information about schema and formatting.";
SerialAskForUrls.pfm_title = "Allow the Serial API on these sites";
SerialAskForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can ask users to grant them access to a serial port.\n\nLeaving the policy unset means DefaultSerialGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nFor URL patterns which do not match the policy SerialBlockedForUrls (if there is a match), DefaultSerialGuardSetting (if set), or the users' personal settings take precedence, in that order.\n\nIf URL patterns conflict with SerialBlockedForUrls they will be ignored.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
SerialBlockedForUrls.pfm_title = "Block the Serial API on these sites";
SerialBlockedForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can't ask users to grant them access to a serial port.\n\nLeaving the policy unset means DefaultSerialGuardSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nFor URL patterns which do not match the policy SerialAskForUrls (if there is a match), DefaultSerialGuardSetting (if set), or the users' personal settings take precedence, in that order.\n\nIf URL patterns conflict with SerialAskForUrls this policy will take precedence.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
ServiceWorkerToControlSrcdocIframeEnabled.pfm_title = "Allow ServiceWorker to control srcdoc iframes";
ServiceWorkerToControlSrcdocIframeEnabled.pfm_description = "https://github.com/w3c/ServiceWorker/issues/765 asks srcdoc iframe with\nthe \"allow-same-origin\" sandbox attribute to be under ServiceWorker control.\n\nSetting the policy to Enabled or leaving it unset means\nGoogle Chrome makes srcdoc iframes\nwith \"allow-same-origin\" sandbox attributes to be under ServiceWorker control.\n\nSetting the policy to Disabled leaves the srcdoc iframe not controlled by\nServiceWorker.\n\nThis policy is intended to be temporary and will be removed in 2026.";
SharedArrayBufferUnrestrictedAccessAllowed.pfm_title = "Specifies whether SharedArrayBuffers can be used in a non cross-origin-isolated context";
SharedArrayBufferUnrestrictedAccessAllowed.pfm_description = "\nSpecifies whether SharedArrayBuffers can be used in a non cross-origin-isolated context. Google Chrome will require cross-origin isolation when using SharedArrayBuffers from Google Chrome 91 onward (2021-05-25) for Web Compatibility reasons. Additional details can be found on: https://developer.chrome.com/blog/enabling-shared-array-buffer/.\n\nWhen set to Enabled, sites can use SharedArrayBuffer with no restrictions.\n\nWhen set to Disabled or not set, sites can only use SharedArrayBuffers when cross-origin isolated.";
SharedClipboardEnabled.pfm_title = "Enable the Shared Clipboard Feature";
SharedClipboardEnabled.pfm_description = "Enable the Shared Clipboard feature which allows users to send text between Chrome Desktops and an Android device when Sync is enabled and the user is Signed-in.\n\nIf this policy is set to true, the capability of sending text, cross device, for chrome user is enabled.\n\nIf this policy is set to false, the capability of sending text, cross device, for chrome user is disabled.\n\nIf you set this policy, users cannot change or override it.\n\nIf this policy is left unset, the shared clipboard feature is enabled by default.\n\nIt is up to the admins to set policies in all platforms they care about. It's recommended to set this policy to one value in all platforms.";
SharedWorkerBlobURLFixEnabled.pfm_title = "Make SharedWorker blob URL behavior aligned with the specification";
SharedWorkerBlobURLFixEnabled.pfm_description = "Upon https://w3c.github.io/ServiceWorker/#control-and-use-worker-client,\nworkers should inherit controllers for the blob URL.  However, existing code\nallows only DedicatedWorkers to inherit the controller, and SharedWorkers do\nnot inherit the controller.\n\nSetting the policy to Enabled or leaving it unset means\nGoogle Chrome inherit the controller\nif a blob URL is used as a SharedWorker URL.\n\nSetting the policy to Disabled leaves the behavior not aligned with the\nspecification as-is.\n\nThis policy is intended to be temporary and will be removed in the future.";
ShoppingListEnabled.pfm_title = "Allow the shopping list feature to be enabled";
ShoppingListEnabled.pfm_description = "This policy controls the availability of the shopping list feature.\nIf enabled, users will be presented with UI to track the price of the product displayed on the current page. The tracked product will be shown in the bookmarks side panel.\nIf this policy is set to Enabled or not set, the shopping list feature will be available to users.\nIf this policy is set to Disabled, the shopping list feature will be unavailable.\n";
ShowAppsShortcutInBookmarkBar.pfm_title = "Show the apps shortcut in the bookmark bar";
ShowAppsShortcutInBookmarkBar.pfm_description = "Setting the policy to True displays the apps shortcut. Setting the policy to False means this shortcut never appears.\n\nIf you set the policy, users can't change it. If not set, users decide to show or hide the apps shortcut from the bookmark bar context menu.";
ShowCastIconInToolbar.pfm_title = "Show the Google Cast toolbar icon";
ShowCastIconInToolbar.pfm_description = "Setting the policy to Enabled displays the Cast toolbar icon on the toolbar or the overflow menu, and users can't remove it.\n\nSetting the policy to Disabled or leaving it unset lets users pin or remove the icon through its contextual menu.\n\nIf the policy EnableMediaRouter is set to Disabled, then this policy's value has no effect, and the toolbar icon doesn't appear.";
ShowCastSessionsStartedByOtherDevices.pfm_title = "Show media controls for Google Cast sessions started by other devices on the local network";
ShowCastSessionsStartedByOtherDevices.pfm_description = "When this policy is enabled, media playback controls UI is available for Google Cast sessions started by other devices on the local network.\n\nWhen this policy is unset for enterprise users or is disabled, media playback controls UI is unavailable for Google Cast sessions started by other devices on the local network.\n\nIf the policy EnableMediaRouter is disabled, then this policy's value has no effect, as the entire Google Cast functionality is disabled.";
ShowFullUrlsInAddressBar.pfm_title = "Show Full URLs";
ShowFullUrlsInAddressBar.pfm_description = "This feature enables display of the full URL in the address bar.\nIf this policy is set to True, then the full URL will be shown in the address bar, including schemes and subdomains.\nIf this policy is set to False, then the default URL display will apply.\nIf this policy is left unset, then the default URL display will apply and the user will be able to toggle between default and full URL display with a context menu option.\n";
ShowHomeButton.pfm_title = "Show Home button on toolbar";
ShowHomeButton.pfm_description = "Setting the policy to Enabled shows the Home button on Google Chrome's toolbar. Setting the policy to Disabled keeps the Home button from appearing.\n\nIf you set the policy, users can't change it in Google Chrome. If not set, users chooses whether to show the Home button.";
SideSearchEnabled.pfm_title = "Allow showing the most recent default search engine results page in a Browser side panel";
SideSearchEnabled.pfm_description = "Setting the policy to Enabled or leaving the policy unset means that users can bring up their most recent default search engine results page in a side panel via toggling an icon in the toolbar.\n\nSetting the policy to Disabled removes the icon from the toolbar that opens the side panel with the default search engine results page.";
SignedHTTPExchangeEnabled.pfm_title = "Enable Signed HTTP Exchange (SXG) support";
SignedHTTPExchangeEnabled.pfm_description = "Setting the policy to True or leaving it unset means Google Chrome will accept web contents served as Signed HTTP Exchanges.\n\nSetting the policy to False prevents Signed HTTP Exchanges from loading.";
SigninInterceptionEnabled.pfm_title = "Enable signin interception";
SigninInterceptionEnabled.pfm_description = "This settings enables or disables signin interception.\n\nWhen this policy not set or is enabled, the signin interception dialog triggers when a Google account is added on the web, and the user may benefit from moving this account to another (new or existing) profile.\n\nWhen this is disabled, the signin interception dialog does not trigger.\nWhen this is disabled, a dialog will still be shown if managed account profile separation is enforced by ManagedAccountsSigninRestriction.";
SitePerProcess.pfm_title = "Require Site Isolation for every site";
SitePerProcess.pfm_description = "Since Google Chrome 67, site isolation has been enabled by default on all Desktop platforms, causing every site to run in its own process. A site is a scheme plus eTLD+1 (e.g., https://example.com). Setting this policy to Enabled does not change that behavior; it only prevents users from opting out (for example, using Disable site isolation in chrome://flags). Since Google Chrome 76, setting the policy to Disabled or leaving it unset doesn't turn off site isolation, but instead allows users to opt out.\n\nIsolateOrigins might also be useful for isolating specific origins at a finer granularity than site (e.g., https://a.example.com).\n\nOn Google ChromeOS version 76 and earlier, set the DeviceLoginScreenSitePerProcess device policy to the same value. (If the values don't match, a delay can occur when entering a user session.)\n\nNote: For Android, use the SitePerProcessAndroid policy instead.";
SiteSearchSettings.pfm_title = "Site search settings";
SiteSearchSettings.pfm_description = "This policy provides a list of sites that users can quickly search using shortcuts in the address bar. Users can initiate a search by typing the shortcut or @shortcut (e.g. @work), followed by Space or Tab, in the address bar.\n\nThe following fields are required for each site: name, shortcut, url.\n\nThe name field corresponds to the site or search engine name to be shown to the user in the address bar.\n\nThe shortcut can include plain words and characters, but cannot include spaces or start with the @ symbol. Shortcuts must also be unique.\n\nFor each entry, the url field specifies the URL of the search engine used during a search with the corresponding keyword. The URL must include the string '{searchTerms}', replaced in the query by the user's search terms. Invalid entries and entries with duplicate shortcuts are ignored.\n\nSite search entries configured as featured are displayed in the address bar when the user types \"@\". Up to three entries can be selected as featured.\n\nUsers cannot edit or disable site search entries set by policy, but they can add new shortcuts for the same URL. In addition, users cannot create new site search entries with a shortcut previously created via this policy.\n\nIn case of a conflict with a shortcut previously created by the user, the user setting takes precedence. However, users can still trigger the option created by the policy by typing \"@\" in the search bar. For example, if the user already defined \"work\" as a shortcut to URL1 and the policy defines \"work\" as a shortcut to URL2, then typing \"work\" in the search bar will trigger a search to URL1, but typing \"@work\" in the search bar will trigger a search to URL2.\n\nOn Microsoft® Windows®, this policy is only available on instances that are joined to a Microsoft® Active Directory® domain, joined to Microsoft® Azure® Active Directory® or enrolled in Chrome Enterprise Core.\n\nOn macOS, this policy is only available on instances that are managed via MDM, joined to a domain via MCX or enrolled in Chrome Enterprise Core.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=SiteSearchSettings for more information about schema and formatting.";
SpellCheckServiceEnabled.pfm_title = "Enable or disable spell checking web service";
SpellCheckServiceEnabled.pfm_description = "Setting the policy to Enabled puts a Google web service in use to help resolve spelling errors. This policy only controls the use of the online service. Setting the policy to Disabled means this service is never used.\n\nLeaving the policy unset lets users choose whether to use the spellcheck service.\n\nThe spell check can always use a downloaded dictionary locally unless the feature is disabled by SpellcheckEnabled in which case this policy will have no effect.";
SpellcheckEnabled.pfm_title = "Enable spellcheck";
SpellcheckEnabled.pfm_description = "Setting the policy to Enabled turns spellcheck on, and users can't turn it off. On Microsoft® Windows®, Google ChromeOS and Linux®, spellcheck languages can be switched on or off individually, so users can still turn spellcheck off by switching off every spellcheck language. To avoid that, use the SpellcheckLanguage to force-enable specific spellcheck languages.\n\nSetting the policy to Disabled turns off spellcheck from all sources, and users can't turn it on. The SpellCheckServiceEnabled, SpellcheckLanguage and SpellcheckLanguageBlocklist policies have no effect when this policy is set to False.\n\nLeaving the policy unset lets users turn spellcheck on or off in the language settings.";
StandardizedBrowserZoomEnabled.pfm_title = "Enable Standardized Browser Zoom Behavior";
StandardizedBrowserZoomEnabled.pfm_description = "This policy enables conformance to the newly-adopted specification of CSS zoom.\n\nWhen this policy is Enabled or unset, the CSS \"zoom\" property will adhere to the specification:\n\nhttps://drafts.csswg.org/css-viewport/#zoom-property\n\nWhen Disabled, the CSS \"zoom\" property will fall back to its legacy pre-standardized behavior.\n\nThis policy is a temporary reprieve to allow time to migrate web content to the new behavior. There is also an origin trial (\"DisableStandardizedBrowserZoom\") that corresponds to the behavior when this policy is Disabled. This policy will be removed and the \"Enabled\" behavior made permanent in milestone 134.";
StrictMimetypeCheckForWorkerScriptsEnabled.pfm_title = "Enable strict MIME type checking for worker scripts";
StrictMimetypeCheckForWorkerScriptsEnabled.pfm_description = "This policy enables strict MIME type checking for worker scripts.\n\nWhen enabled or unset, then worker scripts will use strict MIME type checking for JavaScript, which is the new default behaviour. Worker scripts with legacy MIME types will be rejected.\n\nWhen disabled, then worker scripts will use lax MIME type checking, so that worker scripts with legacy MIME types, e.g. text/ascii, will continue to be loaded and executed.\n\nBrowsers traditionally used lax MIME type checking, so that resources with a number of legacy MIME types were supported. E.g. for JavaScript resources, text/ascii is a legacy supported MIME type. This may cause security issues, by allowing to load resources as scripts that were never intended to be used as such. Chrome will transition to use strict MIME type checking in the near future. The enabled policy will track the default behaviour. Disabling this policy allows administrators to retain the legacy behaviour, if desired.\n\nSee https://html.spec.whatwg.org/multipage/scripting.html#scriptingLanguage for details about JavaScript / ECMAScript media types.\n";
SuppressDifferentOriginSubframeDialogs.pfm_title = "Suppress JavaScript Dialogs triggered from different origin subframes";
SuppressDifferentOriginSubframeDialogs.pfm_description = "As described in https://www.chromestatus.com/feature/5148698084376576 , JavaScript modal dialogs, triggered by window.alert, window.confirm, and window.prompt, will be blocked in Google Chrome if triggered from a subframe whose origin is different from the main frame origin.\n\nThis policy allows overriding that change.\nIf the policy is set to enabled or unset, JavaScript dialogs triggered from a different origin subframe will be blocked.\nIf the policy is set to disabled, JavaScript dialogs triggered from a different origin subframe will not be blocked.\n\nThis policy will be removed from Google Chrome in the future.";
SuppressUnsupportedOSWarning.pfm_title = "Suppress the unsupported OS warning";
SuppressUnsupportedOSWarning.pfm_description = "Setting the policy to Enabled suppresses the warning that appears when Google Chrome is running on an unsupported computer or operating system.\n\nSetting the policy to Disabled or leaving it unset means the warnings appear on unsupported systems.";
SyncDisabled.pfm_title = "Disable synchronization of data with Google";
SyncDisabled.pfm_description = "Setting the policy to Enabled turns off data synchronization in Google Chrome using Google-hosted synchronization services.\nTo fully turn off Chrome Sync services, we recommend that you turn off the service in the Google Admin console.\n\nIf the policy is set to Disabled or not set, users are allowed to choose whether to use Chrome Sync.\n\nNote: Do not turn on this policy when RoamingProfileSupportEnabled is Enabled, because that feature shares the same client-side functionality. The Google-hosted synchronization is off completely in this case.";
SyncTypesListDisabled.pfm_title = "List of types that should be excluded from synchronization";
SyncTypesListDisabled.pfm_description = "If this policy is set all specified data types will be excluded from synchronization both for Chrome Sync as well as for roaming profile synchronization. This can be beneficial to reduce the size of the roaming profile or limit the type of data uploaded to the Chrome Sync Servers.\n\nThe current data types for this policy are: \"apps\", \"autofill\", \"bookmarks\", \"extensions\", \"preferences\", \"passwords\", \"payments\", \"productComparison\", \"readingList\", \"savedTabGroups\", \"tabs\", \"themes\", \"typedUrls\", \"wifiConfigurations\". Those names are case sensitive!\n\nNotes: Dynamic Policy Refresh is supported only in Google Chrome version 123 and later. Disabling \"autofill\" also disables \"payments\". \"typedUrls\" refers to all browsing history.";
TLS13EarlyDataEnabled.pfm_title = "Enable TLS 1.3 Early Data";
TLS13EarlyDataEnabled.pfm_description = "TLS 1.3 Early Data is an extension to TLS 1.3 to send an HTTP request simultaneously with the TLS handshake.\n\nIf this policy is not configured, Google Chrome will follow the default rollout process for TLS 1.3 Early Data.\n\nIf it is enabled, Google Chrome will enable TLS 1.3 Early Data.\n\nIf it is disabled, Google Chrome will not enable TLS 1.3 Early Data.\n\nWhen the feature is enabled, Google Chrome may or may not use TLS 1.3 Early Data depending on server support.\n\nTLS 1.3 Early Data is an established protocol. Existing TLS servers, middleboxes, and security software are expected to either handle or reject TLS 1.3 Early Data without dropping the connection.\n\nHowever, devices that do not correctly implement TLS may malfunction and disconnect when TLS 1.3 Early Data is in use. If this occurs, administrators should contact the vendor for a fix.\n\nThis policy is a temporary measure to control the feature and will be removed afterwards. The policy may be enabled to allow you to test for issues and disabled while issues are being resolved.";
TabCaptureAllowedByOrigins.pfm_title = "Allow Tab capture by these origins";
TabCaptureAllowedByOrigins.pfm_description = "Setting the policy lets you set a list of URL patterns that can use Tab Capture.\n\nLeaving the policy unset means that sites will not be considered for an override at this level of capture.\n\nNote that windowed Chrome Apps will still be allowed to be captured.\n\nThis policy is not considered if a site matches a URL pattern in the SameOriginTabCaptureAllowedByOrigins policy.\n\nIf a site matches a URL pattern in this policy, the following policies will not be considered: WindowCaptureAllowedByOrigins, ScreenCaptureAllowedByOrigins, ScreenCaptureAllowed.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.  This policy only matches based on origin, so any path in the URL pattern is ignored.";
TabCompareSettings.pfm_title = "Tab Compare settings";
TabCompareSettings.pfm_description = "0 - Allow Tab Compare and improve AI models.\n1 - Allow Tab Compare without improving AI models.\n2 - Do not allow Tab Compare.\nTab Compare is an AI-powered tool for comparing information across a user's tabs. As an example, the feature can be offered to the user when multiple tabs with products in a similar category are open.\n\n0 = Allow the feature to be used, while allowing Google to use relevant data to improve its AI models. Relevant data may include prompts, inputs, outputs, source materials, and written feedback, depending on the feature. It may also be reviewed by humans to improve AI models. 0 is the default value, except when noted below.\n\n1 = Allow the feature to be used, but does not allow Google to improve models using users' content (including prompts, inputs, outputs, source materials, and written feedback). 1 is the default value for Enterprise users managed by Google Admin console and for Education accounts managed by Google Workspace.\n\n2 = Do not allow the feature.\n\nIf the policy is unset, its behavior is determined by the GenAiDefaultSettings policy.\n\nFor more information on data handling for generative AI features, please see https://support.google.com/chrome/a?p=generative_ai_settings.";
TabDiscardingExceptions.pfm_title = "URL pattern Exceptions to tab discarding";
TabDiscardingExceptions.pfm_description = "This policy makes it so that any URL matching one or more of the patterns it specifies (using the URLBlocklist filter format) will never be discarded by the browser.\nThis applies to memory pressure and high efficiency mode discarding.\nA discarded page is unloaded and its resources fully reclaimed. The tab its associated with remains in the tabstrip, but making it visible will trigger a full reload.\n";
TaskManagerEndProcessEnabled.pfm_title = "Enable ending processes in Task Manager";
TaskManagerEndProcessEnabled.pfm_description = "Setting the policy to Disabled prevents users from ending processes in the Task Manager.\n\nSetting the policy to Enabled or leaving it unset lets users end processes in the Task Manager.";
ThirdPartyStoragePartitioningBlockedForOrigins.pfm_title = "Disable third-party storage partitioning for specific top-level origins";
ThirdPartyStoragePartitioningBlockedForOrigins.pfm_description = "This policy allows you to set a list of URL patterns that specify top-level origins for which third-party storage partitioning (partitioning of cross-origin iframe storage) should be disabled.\n\nIf this policy is left unset or if a top-level origin doesn't match one of the URL patterns, DefaultThirdPartyStoragePartitioningSetting will apply.\n\nFor detailed information on valid patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Note that patterns you list here are treated as origins, not URLs, so you should not specify a path.\n\nFor detailed information on third-party storage partitioning, please see https://developers.google.com/privacy-sandbox/cookies/storage-partitioning.";
TotalMemoryLimitMb.pfm_title = "Set memory limit for Chrome instances";
TotalMemoryLimitMb.pfm_description = "Configures the amount of memory that a single Google Chrome instance can use before tabs start being discarded (I.E. the memory used by the tab will be freed and the tab will have to be reloaded when switched to) to save memory.\n\nIf the policy is set, browser will begin to discard tabs to save memory once the limitation is exceeded. However, there is no guarantee that the browser is always running under the limit. Any value under 1024 will be rounded up to 1024.\n\nIf this policy is not set, the browser will only begin attempts to save memory once it has detected that the amount of physical memory on its machine is low.";
TranslateEnabled.pfm_title = "Enable Translate";
TranslateEnabled.pfm_description = "Setting the policy to True provides translation functionality when it's appropriate for users by showing an integrated translate toolbar in Google Chrome and a translate option on the right-click context menu. Setting the policy to False shuts off all built-in translate features.\n\nIf you set the policy, users can't change this function. Leaving it unset lets them change the setting.";
TranslatorAPIAllowed.pfm_title = "Allow Translator API";
TranslatorAPIAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset allows the use of Translator API in Google Chrome.\n\nSetting the policy to Disabled disallows the use of Translator API.";
URLAllowlist.pfm_title = "Allow access to a list of URLs";
URLAllowlist.pfm_description = "Setting the policy provides access to the listed URLs, as exceptions to URLBlocklist. See that policy's description for the format of entries of this list. For example, setting URLBlocklist to * will block all requests, and you can use this policy to allow access to a limited list of URLs. Use it to open exceptions to certain schemes, subdomains of other domains, ports, or specific paths, using the format specified at ( https://support.google.com/chrome/a?p=url_blocklist_filter_format ). The most specific filter determines if a URL is blocked or allowed. The URLAllowlist policy takes precedence over URLBlocklist. This policy is limited to 1,000 entries.\n\nThis policy also allows enabling the automatic invocation by the browser of external application registered as protocol handlers for the listed protocols like \"tel:\" or \"ssh:\".\n\nLeaving the policy unset allows no exceptions to URLBlocklist.\n\nFrom Google Chrome version 92, this policy is also supported in the headless mode.";
URLBlocklist.pfm_title = "Block access to a list of URLs";
URLBlocklist.pfm_description = "Setting the URLBlocklist policy stops web pages with prohibited URLs from loading. Administrators can specify the list of URL patterns to be blocked. If left unset, no URLs are blocked in the browser. Up to 1,000 exceptions can be defined in URLAllowlist. See how to format a URL pattern ( https://support.google.com/chrome/a?p=url_blocklist_filter_format ).\n\nNote: This policy does not apply to in-page JavaScript URLs with dynamically loaded data. If you blocked example.com/abc, then example.com could still load it using XMLHTTPRequest. Additionally, this policy does not prevent web pages from updating the URL shown in the omnibox to a blocked one using the JavaScript History API.\n\nFrom Google Chrome version 73, you can block javascript://* URLs. But, this only affects JavaScript entered in the address bar or, for example, bookmarklets.\n\nFrom Google Chrome version 92, this policy is also supported in the headless mode.\n\nNote: Blocking internal chrome://* and chrome-untrusted://* URLs can lead to unexpected errors or can be circumvented in some cases. Instead of blocking certain internal URLs, see if there are more specific policies available. For example:\n\n- Instead of blocking chrome://settings/certificates, use CACertificateManagementAllowed.\n\n- Instead of blocking chrome-untrusted://crosh, use SystemFeaturesDisableList.";
UrlKeyedAnonymizedDataCollectionEnabled.pfm_title = "Enable URL-keyed anonymized data collection";
UrlKeyedAnonymizedDataCollectionEnabled.pfm_description = "Setting the policy to Enabled means URL-keyed anonymized data collection, which sends URLs of pages the user visits to Google to make searches and browsing better, is always active.\n\nSetting the policy to Disabled results in no URL-keyed anonymized data collection.\n\nIf this policy is left unset, the user will be able to change this setting manually.\n\nIn Google ChromeOS Kiosk, this policy doesn't offer the option to \"Allow the user to decide\". If this policy is unset for Google ChromeOS Kiosk, URL-keyed anonymized data collection is always active.\nWhen set for Google ChromeOS Kiosk, this policy enables URL-keyed metrics collection for kiosk apps.";
UserAgentReduction.pfm_title = "Enable or disable the User-Agent Reduction.";
UserAgentReduction.pfm_description = "0 - User Agent reduction will be controllable via Field-Trials and Origin-Trials.\n1 - User Agent reduction disabled, and not enabled by Field-Trials or Origin-Trials.\n2 - User Agent reduction will be enabled for all origins.\nThe User-Agent HTTP request header is scheduled to be reduced. In order to facilitate testing and compatibility, this policy can enable the reduction feature for all websites, or disable the ability for origin trials or field trials to enable the feature.\n\nTo learn more about the User-Agent Reduction and its timeline, read here:\n\nhttps://blog.chromium.org/2021/09/user-agent-reduction-origin-trial-and-dates.html\n";
UserDataDir.pfm_title = "Set user data directory";
UserDataDir.pfm_description = "Configures the directory that Google Chrome will use for storing user data.\n\nIf you set this policy, Google Chrome will use the provided directory regardless whether the user has specified the '--user-data-dir' flag or not. To avoid data loss or other unexpected errors this policy should not be set to a directory used for other purposes, because Google Chrome manages its contents.\n\nSee https://support.google.com/chrome/a?p=Supported_directory_variables for a list of variables that can be used.\n\nIf this policy is left not set the default profile path will be used and the user will be able to override it with the '--user-data-dir' command line flag.";
UserDataSnapshotRetentionLimit.pfm_title = "Limits the number of user data snapshots retained for use in case of emergency rollback.";
UserDataSnapshotRetentionLimit.pfm_description = "Following each major version update, Chrome will create a snapshot of certain portions of the user's browsing data for use in case of a later emergency version rollback. If an emergency rollback is performed to a version for which a user has a corresponding snapshot, the data in the snapshot is restored. This allows users to retain such settings as bookmarks and autofill data.\n\nIf this policy is not set, the default value of 3 is used\n\nIf the policy is set, old snapshots are deleted as needed to respect the limit. If the policy is set to 0, no snapshots will be taken";
UserFeedbackAllowed.pfm_title = "Allow user feedback";
UserFeedbackAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset lets users send feedback to Google through Menu > Help > Report an Issue or key combination.\n\nSetting the policy to Disabled means users can't send feedback to Google.";
VideoCaptureAllowed.pfm_title = "Allow or deny video capture";
VideoCaptureAllowed.pfm_description = "Setting the policy to Enabled or leaving it unset means that, with the exception of URLs set in the VideoCaptureAllowedUrls list, users get prompted for video capture access.\n\nSetting the policy to Disabled turns off prompts, and video capture is only available to URLs set in the VideoCaptureAllowedUrls list.\n\nNote: The policy affects all video input (not just the built-in camera).";
VideoCaptureAllowedUrls.pfm_title = "URLs that will be granted access to video capture devices without prompt";
VideoCaptureAllowedUrls.pfm_description = "Setting the policy means you specify the URL list whose patterns get matched to the security origin of the requesting URL. A match grants access to video capture devices without prompt\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Note, however, that the pattern \"*\", which matches any URL, is not supported by this policy.";
WPADQuickCheckEnabled.pfm_title = "Enable WPAD optimization";
WPADQuickCheckEnabled.pfm_description = "Setting the policy to Enabled or leaving it unset turns on WPAD (Web Proxy Auto-Discovery) optimization in Google Chrome.\n\nSetting the policy to Disabled turns off WPAD optimization, causing Google Chrome to wait longer for DNS-based WPAD servers.\n\nWhether or not this policy is set, users can't change the WPAD optimization setting.";
WarnBeforeQuittingEnabled.pfm_title = "Show a warning dialog when the user is attempting to quit";
WarnBeforeQuittingEnabled.pfm_description = "Controls \"Warn Before Quitting (⌘Q)\" dialog when the user is attempting to quit browser.\n\nIf this policy is set to Enabled or not set, a warning dialog is shown when the user is attempting to quit.\n\nIf this policy is set to Disabled, a warning dialog is not shown when the user is attempting to quit.";
WebAppInstallForceList.pfm_title = "URLs for Web Apps to be silently installed.";
WebAppInstallForceList.pfm_description = "Setting the policy specifies a list of web apps that install silently, without user interaction, and which users can't uninstall or turn off.\n\nEach list item of the policy is an object with a mandatory member:\nurl (the URL of the web app to install)\n\nand 6 optional members:\n- default_launch_container\n(for how the web app opens—a new tab is the default)\n\n- create_desktop_shortcut\n(True if you want to create Linux and\nMicrosoft® Windows® desktop shortcuts).\n\n- fallback_app_name\n(Starting with Google Chrome version 90,\nallows you to override the app name if it is not a\nProgressive Web App (PWA), or the app name that is temporarily\ninstalled if it is a PWA but authentication is required before the\ninstallation can be completed. If both\ncustom_name and\nfallback_app_name are provided,\nthe latter will be ignored.)\n\n- custom_name\n(Starting with Google ChromeOS\nversion 99, and version 112 on all other desktop operating systems, allows you to\npermanently override the app name for all web apps and PWAs.)\n\n- custom_icon\n(Starting with Google ChromeOS\nversion 99, and version 112 on all other desktop operating systems, allows you to\noverride the app icon of installed apps. The icons have to be square,\nmaximal 1 MB in size, and in one of the following formats: jpeg, png, gif, webp, ico.\nThe hash value has to be the SHA256 hash of the icon file. The url\nshould be accessible without authentication to ensure the icon can be used\nupon app installation.)\n\n- install_as_shortcut\n(Starting with Google Chrome\nversion 107). If enabled the given url\nwill be installed as a shortcut, as if done via the \"Create Shortcut...\"\noption in the desktop browser GUI.\nNote that when installed as a shortcut it won't be updated if the\nmanifest in url changes.\nIf disabled or unset, the web app at the given\nurl will be installed normally.\n\nSee PinnedLauncherApps for pinning apps to the Google ChromeOS shelf.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebAppInstallForceList for more information about schema and formatting.";
WebAppSettings.pfm_title = "Web App management settings";
WebAppSettings.pfm_description = "This policy allows an admin to specify settings for installed web apps. This policy maps a Web App ID to its specific setting. A default configuration can be set using the special ID *, which applies to all web apps without a custom configuration in this policy.\n\nThe manifest_id field is the Manifest ID for the Web App. See https://developer.chrome.com/blog/pwa-manifest-id/ for instructions on how to determine the Manifest ID for an installed web app.\nThe run_on_os_login field specifies if a web app can be run during OS login. If this field is set to blocked, the web app will not run during OS login and the user will not be able to enable this later. If this field is set to run_windowed, the web app will run during OS login and the user will not be able to disable this later. If this field is set to allowed, the user will be able to configure the web app to run at OS login. The default configuration only allows the allowed and blocked values.\n(Since version 117) The prevent_close_after_run_on_os_login field specifies if a web app shall be prevented from closing in any way (e.g. by the user, task manager, web APIs). This behavior can only be enabled if run_on_os_login is set to run_windowed. If the app were already running, this property will only come into effect after the app is restarted. If this field is not defined, apps will be closable by users.\n(Since version 118) The force_unregister_os_integration field specifies if all OS integration for a web app, i.e. shortcuts, file handlers, protocol handlers etc will be removed or not. If an app is already running, this property will come into effect after the app has restarted. This should be used with caution, since this can override any OS integration that is set automatically during the startup of the web applications system. Currently only works on Windows, Mac and Linux platforms.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebAppSettings for more information about schema and formatting.";
WebAudioOutputBufferingEnabled.pfm_title = "Enable adaptive buffering for Web Audio";
WebAudioOutputBufferingEnabled.pfm_description = "This policy controls whether the browser uses adaptive buffering for\nWeb Audio, which may decrease audio glitches but may increase\nlatency by a variable amount.\n\nSetting the policy to Enabled will always use adaptive buffering.\n\nSetting the policy to Disabled or not set will allow the browser\nfeature launch process to decide if adaptive buffering is used.";
WebAuthenticationRemoteDesktopAllowedOrigins.pfm_title = "Allowed Origins for Proxied WebAuthn Requests from Remote Desktop Applications.";
WebAuthenticationRemoteDesktopAllowedOrigins.pfm_description = "A list of origins of remote desktop client apps that may execute WebAuthn API\nrequests that originate from a browsing session on a remote host.\n\nAny origin configured in this policy can make WebAuthn requests for Relying\nParty IDs (RP IDs) that it would normally not allowed to be able to claim.\n\nOnly valid HTTPS origins are allowed. Wildcards are not supported.\nAny invalid entries are ignored.";
WebHidAllowAllDevicesForUrls.pfm_title = "Automatically grant permission to sites to connect to any HID device.";
WebHidAllowAllDevicesForUrls.pfm_description = "Setting the policy allows you to list sites which are automatically granted permission to access all available devices.\n\nThe URLs must be valid, otherwise the policy is ignored. Only the origin (scheme, host and port) of the URL is considered.\n\nOn ChromeOS, this policy only applies to affiliated users.\n\nThis policy overrides DefaultWebHidGuardSetting, WebHidAskForUrls, WebHidBlockedForUrls and the user's preferences.";
WebHidAllowDevicesForUrls.pfm_title = "Automatically grant permission to these sites to connect to HID devices with the given vendor and product IDs.";
WebHidAllowDevicesForUrls.pfm_description = "Setting the policy lets you list the URLs that specify which sites are automatically granted permission to access a HID device with the given vendor and product IDs. Each item in the list requires both devices and urls fields for the item to be valid, otherwise the item is ignored. Each item in the devices field must have a vendor_id and may have a product_id field. Omitting the product_id field will create a policy matching any device with the specified vendor ID. An item which has a product_id field without a vendor_id field is invalid and is ignored.\n\nLeaving the policy unset means DefaultWebHidGuardSetting applies, if it's set. If not, the user's personal setting applies.\n\nURLs in this policy shouldn't conflict with those configured through WebHidBlockedForUrls. If they do, this policy takes precedence over WebHidBlockedForUrls.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebHidAllowDevicesForUrls for more information about schema and formatting.";
WebHidAllowDevicesWithHidUsagesForUrls.pfm_title = "Automatically grant permission to these sites to connect to HID devices containing top-level collections with the given HID usage.";
WebHidAllowDevicesWithHidUsagesForUrls.pfm_description = "Setting the policy lets you list the URLs that specify which sites are automatically granted permission to access a HID device containing a top-level collection with the given HID usage. Each item in the list requires both usages and urls fields for the policy to be valid. Each item in the usages field must have a usage_page and may have a usage field. Omitting the usage field will create a policy matching any device containing a top-level collection with a usage from the specified usage page. An item which has a usage field without a usage_page field is invalid and is ignored.\n\nLeaving the policy unset means DefaultWebHidGuardSetting applies, if it's set. If not, the user's personal setting applies.\n\nURLs in this policy shouldn't conflict with those configured through WebHidBlockedForUrls. If they do, this policy takes precedence over WebHidBlockedForUrls.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebHidAllowDevicesWithHidUsagesForUrls for more information about schema and formatting.";
WebHidAskForUrls.pfm_title = "Allow the WebHID API on these sites";
WebHidAskForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can ask users to grant them access to a HID device.\n\nLeaving the policy unset means DefaultWebHidGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nFor URL patterns which do not match the policy, the following take precedence, in this order:\n\n  * WebHidBlockedForUrls (if there is a match),\n\n  * DefaultWebHidGuardSetting (if set), or\n\n  * Users' personal settings.\n\nURL patterns must not conflict with WebHidBlockedForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
WebHidBlockedForUrls.pfm_title = "Block the WebHID API on these sites";
WebHidBlockedForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can't ask users to grant them access to a HID device.\n\nLeaving the policy unset means DefaultWebHidGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nFor URL patterns which do not match the policy, the following take precedence, in this order:\n\n  * WebHidAskForUrls (if there is a match),\n\n  * DefaultWebHidGuardSetting (if set), or\n\n  * Users' personal settings.\n\nURL patterns can't conflict with WebHidAskForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
WebRtcEventLogCollectionAllowed.pfm_title = "Allow collection of WebRTC event logs from Google services";
WebRtcEventLogCollectionAllowed.pfm_description = "Setting the policy to Enabled means Google Chrome can collect WebRTC event logs from Google services such as Hangouts Meet and upload them to Google. These logs have diagnostic information for debugging issues with audio or video meetings in Google Chrome, such as the time and size of RTP packets, feedback about congestion on the network, and metadata about time and quality of audio and video frames. These logs have no audio or video content from the meeting. To make debugging easier, Google might associate these logs, by means of a session ID, with other logs collected by the Google service itself.\n\nSetting the policy to Disabled results in no collection or uploading of such logs.\n\nLeaving the policy unset on versions up to and including M76 means Google Chrome defaults to not being able to collect and upload these logs. Starting at M77, Google Chrome defaults to being able to collect and upload these logs from most profiles affected by cloud-based, user-level enterprise policies. From M77 up to and including M80, Google Chrome can also collect and upload these logs by default from profiles affected by Google Chrome on-premise management.";
WebRtcIPHandling.pfm_title = "WebRTC IP handling";
WebRtcIPHandling.pfm_description = "default - WebRTC will use all available interfaces when searching for the best path.\ndefault_public_and_private_interfaces - WebRTC will only use the interface connecting to the public Internet, but may connect using private IP addresses.\ndefault_public_interface_only - WebRTC will only use the interface connecting to the public Internet, and will not connect using private IP addresses.\ndisable_non_proxied_udp - WebRTC will use TCP on the public-facing interface, and will only use UDP if supported by a configured proxy.\nThis policy allows restricting which IP addresses and interfaces WebRTC uses when attempting to find the best available connection.\n\nValid values:\n\n* default - WebRTC uses all available network interfaces.\n\n* default_public_and_private_interfaces - WebRTC uses all public and private interfaces.\n\n* default_public_interface_only - WebRTC uses all public interfaces, but not private ones.\n\n* disable_non_proxied_udp - WebRTC uses either UDP SOCKS proxying or will fallback to TCP proxying.\n\nWhen unset, defaults to using all available network interfaces.\n\nSee RFC 8828 section 5.2 (https://tools.ietf.org/html/rfc8828.html#section-5.2) for a detailed description of all the handling values.";
WebRtcIPHandlingUrl.pfm_title = "WebRTC per URL IP Handling";
WebRtcIPHandlingUrl.pfm_description = "This policy allows restricting which IP addresses and interfaces WebRTC uses when attempting to find the best available connection for each specific URL pattern.\n\nIt accepts a list of URL patterns and handling type pairs. The URL patterns are checked in order and the first match will configure which handling is used by WebRTC for the domain. When the URL of the current document is not matched against any entry, it uses the configuration set by the policy WebRtcIPHandling.\n\nFor detailed information on valid input patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed. This policy only matches based on origin, so any path in the URL pattern is ignored.\n\nValid handling values:\n\n* default - WebRTC uses all network interfaces.\n\n* default_public_and_private_interfaces - WebRTC uses all public and private interfaces.\n\n* default_public_interface_only - WebRTC uses all public interfaces, but not private ones.\n\n* disable_non_proxied_udp - WebRTC uses either UDP SOCKS proxying or will fallback to TCP proxying.\n\nSee RFC 8828 section 5.2 (https://tools.ietf.org/html/rfc8828.html#section-5.2) for a detailed description of all the handling values.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebRtcIPHandlingUrl for more information about schema and formatting.";
WebRtcLocalIpsAllowedUrls.pfm_title = "URLs for which local IPs are exposed in WebRTC ICE candidates";
WebRtcLocalIpsAllowedUrls.pfm_description = "Patterns in this list will be matched against the security origin of the requesting URL.\nIf a match is found or chrome://flags/#enable-webrtc-hide-local-ips-with-mdns is Disabled, the local IP addresses are shown in WebRTC ICE candidates.\nOtherwise, local IP addresses are concealed with mDNS hostnames.\nPlease note that this policy weakens the protection of local IPs if needed by administrators.";
WebRtcTextLogCollectionAllowed.pfm_title = "Allow WebRTC text logs collection from Google Services";
WebRtcTextLogCollectionAllowed.pfm_description = "Setting the policy to enabled means Google Chrome can collect WebRTC text logs from Google services such as Google Meet and upload them to Google. These logs have diagnostic information for debugging issues with audio or video meetings in Google Chrome, such as textual metadata describing incoming and outgoing WebRTC streams, WebRTC specific log entries and additional system information. These logs have no audio or video content from the meeting.\nSetting the policy to disabled results in no uploading of such logs to Google. Logs would still accumulate locally on the user's device.\nLeaving the policy unset means Google Chrome defaults to being able to collect and upload these logs.";
WebRtcUdpPortRange.pfm_title = "Restrict the range of local UDP ports used by WebRTC";
WebRtcUdpPortRange.pfm_description = "If the policy is set, the UDP port range used by WebRTC is restricted to the specified port interval (endpoints included).\n\nIf the policy is not set, or if it is set to the empty string or an invalid port range, WebRTC is allowed to use any available local UDP port.";
WebUsbAllowDevicesForUrls.pfm_title = "Automatically grant permission to these sites to connect to USB devices with the given vendor and product IDs.";
WebUsbAllowDevicesForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites are automatically granted permission to access a USB device with the given vendor and product IDs. Each item in the list requires both devices and urls fields for the policy to be valid. Each item in the devices field can have a vendor_id and product_id field. Omitting the vendor_id field will create a policy matching any device. Omitting the product_id field will create a policy matching any device with the given vendor ID. A policy which has a product_id field without a vendor_id field is invalid.\n\nThe USB permission model will grant the specified URL permission to access the USB device as a top-level origin. If embedded frames need to access USB devices, the 'usb' feature-policy header should be used to grant access. The URL must be valid, otherwise the policy is ignored.\n\nDeprecated: The USB permission model used to support specifying both the requesting and embedding URLs. This is deprecated and only supported for backwards compatibility in this manner: if both a requesting and embedding URL is specified, then the embedding URL will be granted the permission as top-level origin and the requesting URL will be ignored entirely.\n\nThis policy overrides DefaultWebUsbGuardSetting, WebUsbAskForUrls, WebUsbBlockedForUrls and the user's preferences.\n\nThis policy only affects access to USB devices through the WebUSB API. To grant access to USB devices through the Web Serial API see the SerialAllowUsbDevicesForUrls policy.\nSee https://cloud.google.com/docs/chrome-enterprise/policies/?policy=WebUsbAllowDevicesForUrls for more information about schema and formatting.";
WebUsbAskForUrls.pfm_title = "Allow WebUSB on these sites";
WebUsbAskForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can ask users to grant them access to a USB device.\n\nLeaving the policy unset means DefaultWebUsbGuardSetting applies for all sites, if it's set. If not, users' personal settings apply.\n\nURL patterns must not conflict with WebUsbAskForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
WebUsbBlockedForUrls.pfm_title = "Block WebUSB on these sites";
WebUsbBlockedForUrls.pfm_description = "Setting the policy lets you list the URL patterns that specify which sites can't ask users to grant them access to a USB device.\n\nLeaving the policy unset means DefaultWebUsbGuardSetting applies for all sites, if it's set. If not, the user's personal setting applies.\n\nURL patterns can't conflict with WebUsbAskForUrls. Neither policy takes precedence if a URL matches with both.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. * is not an accepted value for this policy.";
WindowCaptureAllowedByOrigins.pfm_title = "Allow Window and Tab capture by these origins";
WindowCaptureAllowedByOrigins.pfm_description = "Setting the policy lets you set a list of URL patterns that can use Window and Tab Capture.\n\nLeaving the policy unset means that sites will not be considered for an override at this level of Capture.\n\nThis policy is not considered if a site matches a URL pattern in any of the following policies: TabCaptureAllowedByOrigins, SameOriginTabCaptureAllowedByOrigins.\n\nIf a site matches a URL pattern in this policy, the following policies will not be considered: ScreenCaptureAllowedByOrigins, ScreenCaptureAllowed.\n\nFor detailed information on valid url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns.  This policy only matches based on origin, so any path in the URL pattern is ignored.";
WindowManagementAllowedForUrls.pfm_title = "Allow Window Management permission on these sites";
WindowManagementAllowedForUrls.pfm_description = "Allows you to set a list of site url patterns that specify sites which will automatically grant the window management permission. This will extend the ability of sites to see information about the device's screens and use that information to open and place windows or request fullscreen on specific screens.\n\nFor detailed information on valid site url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed. This policy only matches based on origin, so any path in the URL pattern is ignored.\n\nIf this policy is not set for a site then the policy from DefaultWindowManagementSetting applies to the site, if set, otherwise the permission will follow the browser's defaults and allow users to choose this permission per site.\n\nThis replaces the deprecated WindowPlacementAllowedForUrls policy.";
WindowManagementBlockedForUrls.pfm_title = "Block Window Management permission on these sites";
WindowManagementBlockedForUrls.pfm_description = "Allows you to set a list of site url patterns that specify sites which will automatically deny the window management permission. This will limit the ability of sites to see information about the device's screens and use that information to open and place windows or request fullscreen on specific screens.\n\nFor detailed information on valid site url patterns, please see https://cloud.google.com/docs/chrome-enterprise/policies/url-patterns. Wildcards, *, are allowed. This policy only matches based on origin, so any path in the URL pattern is ignored.\n\nIf this policy is not set for a site then the policy from DefaultWindowManagementSetting applies to the site, if set, otherwise the permission will follow the browser's defaults and allow users to choose this permission per site.\n\nThis replaces the deprecated WindowPlacementBlockedForUrls policy.";