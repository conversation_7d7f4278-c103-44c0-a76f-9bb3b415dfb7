"Chromium Shortcut" = "Comandă rapidă pentru Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, Drept de autor 2025 Google LLC. Toate drepturile rezervate.";
NSAudioCaptureUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSBluetoothAlwaysUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSBluetoothPeripheralUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSCameraUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSHumanReadableCopyright = "Drept de autor 2025 Google LLC. Toate drepturile rezervate.";
NSLocalNetworkUsageDescription = "Ast<PERSON>l, vei putea să selectezi dintre dispozitivele disponibile și să afișezi conținut pe acestea.";
NSLocationUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSMicrophoneUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Odată ce Chromium are acces, site-urile web îți vor putea solicita accesul.";
