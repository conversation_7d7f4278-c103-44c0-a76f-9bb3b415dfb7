"Chromium Shortcut" = "Chromium Kısayolu";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, Telif hakkı 2025 Google LLC. Tüm hakları saklıdır.";
NSAudioCaptureUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSBluetoothAlwaysUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSBluetoothPeripheralUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSCameraUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSHumanReadableCopyright = "Telif hakkı 2025 Google LLC. Tüm hakları saklıdır.";
NSLocalNetworkUsageDescription = "Böylece, kullanılabilir cihazlar arasından seçiminizi yapıp içerikleri bu cihazlarda görüntüleyebilirsiniz.";
NSLocationUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSMicrophoneUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Chromium erişim izni aldıktan sonra, web siteleri de erişim için sizden izin isteyebilecek.";
