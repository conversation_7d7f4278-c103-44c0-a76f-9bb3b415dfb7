"Chromium Shortcut" = "Zkratka prohlížeče Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, Copyright 2025 Google LLC. Všechna práva vyhrazena.";
NSAudioCaptureUsageDescription = "A<PERSON> bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSBluetoothAlwaysUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSBluetoothPeripheralUsageDescription = "<PERSON><PERSON> bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSCameraUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Všechna práva vyhrazena.";
NSLocalNetworkUsageDescription = "Budete moci vybírat z dostupných zařízení a zobrazovat na nich obsah.";
NSLocationUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSMicrophoneUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Až bude mít Chromium přístup, budou vás weby moci požádat o přístup.";
