"Chromium Shortcut" = "‏קיצור דרך ב-Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, ‏Copyright 2025 Google LLC.‎ כל הזכויות שמורות.";
NSAudioCaptureUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSBluetoothAlwaysUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSBluetoothPeripheralUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSCameraUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSHumanReadableCopyright = "‏Copyright 2025 Google LLC.‎ כל הזכויות שמורות.";
NSLocalNetworkUsageDescription = "כך תהיה לך אפשרות לבחור מבין המכשירים הזמינים ולהציג בהם תוכן.";
NSLocationUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSMicrophoneUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
NSWebBrowserPublicKeyCredentialUsageDescription = "‏אחרי מתן גישה ל-Chromium, אתרים יוכלו לבקש ממך גישה.";
