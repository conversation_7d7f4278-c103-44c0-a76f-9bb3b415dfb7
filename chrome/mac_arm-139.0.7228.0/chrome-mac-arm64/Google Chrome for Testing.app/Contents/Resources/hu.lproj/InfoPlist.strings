"Chromium Shortcut" = "Chromium-parancsikon";
CFBundleGetInfoString = "Google Chrome for Testing 139.0.7228.0, Copyright 2025 Google LLC. Minden jog fenntartva.";
NSAudioCaptureUsageDescription = "<PERSON><PERSON><PERSON> hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSBluetoothAlwaysUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSBluetoothPeripheralUsageDescription = "<PERSON><PERSON><PERSON> hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSCameraUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Minden jog fenntartva.";
NSLocalNetworkUsageDescription = "Ez lehetővé teszi, hogy válasszon a rendelkezésre álló eszközök közül, és tartalmakat jelenítsen meg rajtuk.";
NSLocationUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSMicrophoneUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
