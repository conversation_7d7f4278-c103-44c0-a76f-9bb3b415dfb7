DEBUG: Script top-level execution started (log cleared).
DEBUG: main() function started.

[INFO] Using Katana file 'xxoutput.jsonl'. No --target-url provided, context set to: 'Context from file: xxoutput.jsonl'
[INFO] Cleared/initialized vulnerabilities file: vulnerable_endpoints.txt for context: Context from file: xxoutput.jsonl
[INFO] Starting to parse Katana output file: xxoutput.jsonl for context: Context from file: xxoutput.jsonl
  [DEBUG] Line 1: Parsing JSON: {"timestamp":"2025-06-06T22:29:07.352+03:00","request":{"method":"GET","endpoint":"https://ginandjui...
  [INFO] Line 1: Processing GET https://ginandjuice.shop/resources/js/subscribeNow.js
  [DEBUG] Line 2: Parsing JSON: {"timestamp":"2025-06-06T22:29:07.378015+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 2: Processing GET https://ginandjuice.shop/resources/footer/js/scanme.js
  [DEBUG] Line 3: Parsing JSON: {"timestamp":"2025-06-06T22:29:07.382606+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 3: Processing GET https://ginandjuice.shop/resources/js/angular_1-7-7.js
  [DEBUG] Line 4: Parsing JSON: {"timestamp":"2025-06-06T22:29:07.387865+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 4: Processing GET https://ginandjuice.shop/resources/js/react.development.js
  [DEBUG] Line 5: Parsing JSON: {"timestamp":"2025-06-06T22:29:09.415504+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 5: Processing GET https://ginandjuice.shop/image/scanme/productcatalog/products/6
  [DEBUG] Line 6: Parsing JSON: {"timestamp":"2025-06-06T22:29:10.148023+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 6: Processing GET https://ginandjuice.shop/resources/js/react-dom.development.js
  [DEBUG] Line 7: Parsing JSON: {"timestamp":"2025-06-06T22:29:11.731104+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 7: Processing GET https://ginandjuice.shop/resources/js/g
  [DEBUG] Line 8: Parsing JSON: {"timestamp":"2025-06-06T22:29:11.772038+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 8: Processing GET https://ginandjuice.shop/resources/js/binary/
  [DEBUG] Line 9: Parsing JSON: {"timestamp":"2025-06-06T22:29:12.166003+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 9: Processing GET https://ginandjuice.shop/resources/js/ReactElement.js
  [DEBUG] Line 10: Parsing JSON: {"timestamp":"2025-06-06T22:29:12.205227+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 10: Processing GET https://ginandjuice.shop/resources/images/2Yz
  [DEBUG] Line 11: Parsing JSON: {"timestamp":"2025-06-06T22:29:12.208977+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 11: Processing GET https://ginandjuice.shop/resources/js/ChangeEventPlugin.js
  [DEBUG] Line 12: Parsing JSON: {"timestamp":"2025-06-06T22:29:13.361166+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 12: Processing GET https://ginandjuice.shop/
  [DEBUG] Line 13: Parsing JSON: {"timestamp":"2025-06-06T22:29:13.816073+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 13: Processing GET https://ginandjuice.shop/resources/images/Sm
  [DEBUG] Line 14: Parsing JSON: {"timestamp":"2025-06-06T22:29:14.398535+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 14: Processing GET https://ginandjuice.shop/resources/images/d
  [DEBUG] Line 15: Parsing JSON: {"timestamp":"2025-06-06T22:29:14.437344+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 15: Processing GET https://ginandjuice.shop/resources/images/8X
  [DEBUG] Line 16: Parsing JSON: {"timestamp":"2025-06-06T22:29:14.597583+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 16: Processing GET https://ginandjuice.shop/resources/images/6
  [DEBUG] Line 17: Parsing JSON: {"timestamp":"2025-06-06T22:29:14.635441+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 17: Processing GET https://ginandjuice.shop/resources/images//
  [DEBUG] Line 18: Parsing JSON: {"timestamp":"2025-06-06T22:29:14.639333+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 18: Processing GET https://ginandjuice.shop/resources/images/Z
  [DEBUG] Line 19: Parsing JSON: {"timestamp":"2025-06-06T22:29:15.497001+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 19: Processing GET https://ginandjuice.shop/resources/images/%5Cy
  [DEBUG] Line 20: Parsing JSON: {"timestamp":"2025-06-06T22:29:15.763233+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 20: Processing GET https://ginandjuice.shop/resources/images/g
  [DEBUG] Line 21: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.231623+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 21: Processing GET https://ginandjuice.shop/resources/images/u
  [DEBUG] Line 22: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.478052+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 22: Processing GET https://ginandjuice.shop/image/scanme/productcatalog/products/Gd
  [DEBUG] Line 23: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.637097+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 23: Processing GET https://ginandjuice.shop/image/scanme/productcatalog/products/B%5C
  [DEBUG] Line 24: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.673144+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 24: Processing GET https://ginandjuice.shop/catalog/subscribe
  [DEBUG] Line 25: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.705456+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 25: Processing GET https://ginandjuice.shop/image/scanme/productcatalog/products/QG=
  [DEBUG] Line 26: Parsing JSON: {"timestamp":"2025-06-06T22:29:16.862717+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 26: Processing GET https://ginandjuice.shop/resources/images/Ava
  [DEBUG] Line 27: Parsing JSON: {"timestamp":"2025-06-06T22:29:17.877533+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 27: Processing GET https://ginandjuice.shop/resources/images/&
  [DEBUG] Line 28: Parsing JSON: {"timestamp":"2025-06-06T22:29:25.94532+03:00","request":{"method":"GET","endpoint":"https://ginandj...
  [INFO] Line 28: Processing GET https://ginandjuice.shop/resources/fonts/JosefinSans/&1
  [DEBUG] Line 29: Parsing JSON: {"timestamp":"2025-06-06T22:29:26.032288+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 29: Processing GET https://ginandjuice.shop/resources/fonts/JosefinSans/K-
  [DEBUG] Line 30: Parsing JSON: {"timestamp":"2025-06-06T22:29:28.344575+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 30: Processing GET https://ginandjuice.shop/resources/js/MyComponent
  [DEBUG] Line 31: Parsing JSON: {"timestamp":"2025-06-06T22:29:28.664636+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 31: Processing GET https://ginandjuice.shop/resources/js/Node.js
  [DEBUG] Line 32: Parsing JSON: {"timestamp":"2025-06-06T22:29:28.709887+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 32: Processing GET https://ginandjuice.shop/resources/css/labsScanme.css
  [DEBUG] Line 33: Parsing JSON: {"timestamp":"2025-06-06T22:29:28.960203+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 33: Processing GET https://ginandjuice.shop/resources/labheader/css/scanMeHeader.css
  [DEBUG] Line 34: Parsing JSON: {"timestamp":"2025-06-06T22:29:30.878708+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 34: Processing GET https://ginandjuice.shop/blog/post?postId=4
    [INFO] Fuzzing GET parameters for: https://ginandjuice.shop/blog/post?postId=4
      -> Testing QUERY param: 'postId' (Original value: '4')
  [DEBUG] Line 35: Parsing JSON: {"timestamp":"2025-06-06T22:29:30.954895+03:00","request":{"method":"GET","endpoint":"https://ginand...
  [INFO] Line 35: Processing GET https://ginandjuice.shop/blog/post?postId=3
    [INFO] Fuzzing GET parameters for: https://ginandjuice.shop/blog/post?postId=3
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
  [DEBUG] Line 36: Parsing JSON: {"timestamp":"2025-06-06T22:29:31.009627+03:00","request":{"method":"POST","endpoint":"https://ginan...
  [INFO] Line 36: Processing POST https://ginandjuice.shop/catalog/product/stock
    [INFO] Fuzzing POST parameters for: https://ginandjuice.shop/catalog/product/stock
      [DEBUG] Parsed POST body params: {'productId': ['2']}
        -> Testing BODY param: 'productId' (Original value: '2')
DEBUG: Script exiting, closing log file.
