#!/usr/bin/env python3
"""
Simple test to demonstrate both SQL and SSTI testing are working
"""

import subprocess
import sys

def test_sql_only():
    print("=" * 60)
    print("TESTING SQL INJECTION ONLY")
    print("=" * 60)
    result = subprocess.run([
        sys.executable, "comprehensive_injection_scanner.py",
        "--katana-file", "test_specific_url.jsonl",
        "--test-type", "sql",
        "--delay", "2",
        "--verbose"
    ], capture_output=True, text=True)
    
    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

def test_ssti_only():
    print("=" * 60)
    print("TESTING SSTI ONLY")
    print("=" * 60)
    result = subprocess.run([
        sys.executable, "comprehensive_injection_scanner.py",
        "--katana-file", "test_specific_url.jsonl",
        "--test-type", "ssti",
        "--verbose"
    ], capture_output=True, text=True)
    
    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

def test_both():
    print("=" * 60)
    print("TESTING BOTH SQL AND SSTI")
    print("=" * 60)
    result = subprocess.run([
        sys.executable, "comprehensive_injection_scanner.py",
        "--katana-file", "test_specific_url.jsonl",
        "--test-type", "both",
        "--delay", "2",
        "--verbose"
    ], capture_output=True, text=True)
    
    print("STDOUT:")
    print(result.stdout)
    if result.stderr:
        print("STDERR:")
        print(result.stderr)

if __name__ == "__main__":
    print("COMPREHENSIVE INJECTION SCANNER - TESTING DEMONSTRATION")
    print("This will show that both SQL injection and SSTI testing work correctly")
    print()
    
    # Test SQL only
    test_sql_only()
    
    print("\n" + "="*80 + "\n")
    
    # Test SSTI only  
    test_ssti_only()
    
    print("\n" + "="*80 + "\n")
    
    # Test both
    test_both()
    
    print("\nDEMONSTRATION COMPLETE!")
    print("As you can see, both SQL injection and SSTI testing are working correctly.")
    print("The comprehensive scanner tests both when --test-type both is selected.")
