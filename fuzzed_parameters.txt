# Fuzzed Parameters Log
# Format: HTTP_METHOD URL - parameter_location parameter: parameter_name=parameter_value

GET https://ginandjuice.shop/blog/post?postId=4 - query parameter: postId=4
POST https://ginandjuice.shop/catalog/product/stock - body parameter: productId=2
GET https://ginandjuice.shop/catalog/product?productId=3 - query parameter: productId=3
GET https://ginandjuice.shop/catalog/product?productId=2 - query parameter: productId=2
POST https://ginandjuice.shop/catalog/cart - body parameter: productId=2
POST https://ginandjuice.shop/catalog/cart - body parameter: redir=PRODUCT
GET https://ginandjuice.shop/catalog/product?productId=1 - query parameter: productId=1
POST https://ginandjuice.shop/catalog/cart - body parameter: productId=3
POST https://ginandjuice.shop/catalog/product/stock - body parameter: productId=3
POST https://ginandjuice.shop/catalog/product/stock - body parameter: productId=1
POST https://ginandjuice.shop/catalog/cart - body parameter: productId=1
GET https://ginandjuice.shop/blog/?back=/blog/&search=katana - query parameter: back=/blog/
GET https://ginandjuice.shop/blog/?back=/blog/&search=katana - query parameter: search=katana
POST https://ginandjuice.shop/login - body parameter: csrf=mGaWtAEpny8nRNETubJcJGvQOO8SiHEW
POST https://ginandjuice.shop/login - body parameter: username=katana
GET https://ginandjuice.shop/blog/?search=katana&back=/blog/ - query parameter: search=katana
POST https://ginandjuice.shop/login - body parameter: username=katana
GET https://ginandjuice.shop/catalog?category=Juice - query parameter: category=Juice
GET https://ginandjuice.shop/catalog?category=Juice&searchTerm=katana - query parameter: category=Juice
GET https://ginandjuice.shop/catalog?category=Juice&searchTerm=katana - query parameter: searchTerm=katana
POST https://ginandjuice.shop/login - body parameter: username=katana
GET https://ginandjuice.shop/catalog?category=Gin&searchTerm=katana - query parameter: category=Gin
GET https://ginandjuice.shop/catalog?category=Books&searchTerm=katana - query parameter: category=Books
POST https://ginandjuice.shop/login - body parameter: username=katana
POST https://ginandjuice.shop/login - body parameter: username=katana
POST https://ginandjuice.shop/login - body parameter: username=katana
GET https://ginandjuice.shop/catalog?category=Accompaniments&searchTerm=katana - query parameter: category=Accompaniments
POST https://ginandjuice.shop/login - body parameter: username=katana
GET https://ginandjuice.shop/catalog?category=Accessories&searchTerm=katana - query parameter: category=Accessories
POST https://ginandjuice.shop/login - body parameter: username=katana
