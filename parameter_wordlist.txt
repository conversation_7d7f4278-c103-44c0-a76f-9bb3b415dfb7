# Common parameter names found in SMS ID Management API
# Use with tools like ffuf, gobuster, or custom scripts

# ID Parameters
id
userId
senderId
requestId
contractId
providerId
operatorId
bulkId
categoryId
keywordId
attachmentId
complaintId
certificateId
transactionId

# Pagination Parameters
pageIndex
pageSize
page
limit
offset
count
size

# Filter Parameters
name
username
email
clientName
senderName
providerName
operatorName
invoiceNumber
crNumber
unifiedNumber
enterpriseUnifiedNumber
accreditationNumber

# Type Parameters
type
requestType
senderType
customerType
contractType
shortCodeType
connectivityStatus
requestStatus
paymentStatus

# Date Parameters
paymentDateFrom
paymentDateTo
dateFrom
dateTo
startDate
endDate
createdDate
modifiedDate

# Search Parameters
search
query
filter
keyword
term

# Status Parameters
status
isActive
isLocked
isExpired
isBalanceInvoice
isFromDashboard

# Comment Parameters
comment
description
reason
note
message

# File Parameters
file
attachment
document
certificate

# Authentication Parameters
token
apiKey
authorization
bearer
session

# Configuration Parameters
config
settings
options
preferences

# Admin Parameters
isAdmin
role
permission
access

# Mobile Parameters
mobile
mobileNumber
phone
phoneNumber

# Common Injection Test Parameters
test
admin
user
guest
demo
sample
example
default

# Numeric Test Values
1
0
-1
999999
null
undefined

# Boolean Test Values
true
false
1
0
yes
no

# Special Characters for Testing
'
"
<
>
&
%
;
--
/*
*/
||
&&
|
&amp;
%27
%22
%3C
%3E
%26
%25
%3B
