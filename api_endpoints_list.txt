# SMS ID Management API Endpoints and Parameters
# Domain: https://smsidmanagment.sa.zain.com/

## GET Endpoints:
/api/attachmentCategory?name={name}&pageIndex={pageIndex}&pageSize={pageSize}
/api/attachmentCategory/{id}
/api/attachmentCategory/getall
/api/senders/getSuggestedNames?clientName={clientName}&senderType={senderType}
/api/mci-controller/verifyCR?unifiedNumber={unifiedNumber}
/api/kpi-dashboard/get-alert-days-configuration?type={type}
/api/citckeywords?name={name}&pageIndex={pageIndex}&pageSize={pageSize}
/api/citckeywords/{id}
/api/citckeywords/getall
/api/payment-transaction/getallbalanceinvoices?invoiceNumber={invoiceNumber}&providerName={providerName}&paymentDateFrom={paymentDateFrom}&paymentDateTo={paymentDateTo}&pageIndex={pageIndex}&pageSize={pageSize}
/api/payment-transaction/exportgetallbalanceinvoices?invoiceNumber={invoiceNumber}&providerName={providerName}&paymentDateFrom={paymentDateFrom}&paymentDateTo={paymentDateTo}&pageIndex={pageIndex}&pageSize={pageSize}
/api/payment-transaction/downloadinvoice?invoiceIdentifier={transactionReference}&isBalanceInvoice={isBalanceInvoice}
/api/contract-requests/get-contract-options
/api/providers/is-provider-locked
/api/providers/is-provider-expired
/api/contract-requests/contract-dashboard-boxes
/api/contract-requests/{id}
/api/contract-requests/attachments/{id}
/api/contract-requests/get-request-approval-history/{id}
/api/senders/get-sender-for-contract-request?clientName={clientName}&crNumber={crNumber}&customerType={customerType}&enterpriseUnifiedNumber={enterpriseUnifiedNumber}
/api/contract-requests/get-sender-for-contract-renew?clientName={clientName}&crNumber={crNumber}&customerType={customerType}&enterpriseUnifiedNumber={enterpriseUnifiedNumber}
/api/report/connectivity-activation-report?requestId={requestId}&senderName={senderName}&pageIndex={pageIndex}&pageSize={pageSize}
/api/sender-conectivity-detail?senderId={senderId}
/api/senders/{senderId}
/api/providers/is-provider-dissconected
/api/sender-conectivity-detail-log?connectivityDetailsId={id}
/api/sender-conectivity-detail/connectivity-details-configration
/api/payment-transaction/getcreditlimitpaymenttransaction?sendersName={sendersName}&providerName={providerName}&pageIndex={pageIndex}&pageSize={pageSize}
/api/payment-transaction/export-get-credit-limit-payment-transaction?sendersName={sendersName}&providerName={providerName}&pageIndex={pageIndex}&pageSize={pageSize}
/api/change-sender-type-requests?{model_params}
/api/RejectionReason/get-by-request-type?requestType={requestType}
/api/sender-conectivity-detail/sender-conectivity-details-by-info?{params}
/api/sender-conectivity-detail/export-sender-conectivity-support-report?{params}
/api/senders
/api/complaint/get-details/{id}
/api/complaint/attachments/{id}
/api/sender-request-support-page/get-sender-support-details?senderName={senderName}
/api/sender-request-support-page/get-sender-request-approval-history?id={id}&pageIndex={pageIndex}&pageSize={pageSize}
/api/sender-request-support-page/get-payment-transaction-by-requestid?id={id}&pageIndex={pageIndex}&pageSize={pageSize}
/api/change-sender-type-requests/{id}
/api/sender-requests/{id}
/api/sender-requests/attachments/{id}
/api/sender-requests/get-request-approval-history/{id}
/api/sender-requests/get-extend-requests-history/{id}
/api/shortcodes?operatorId={operator}&name={name}&pageIndex={pageIndex}&pageSize={pageSize}
/api/shortcodes/getall
/api/shortcodes/getById?id={id}
/api/short-code-bulk-activation/get-short-code-connectivity-history?operatorId={operator}&name={name}&connectivityStatus={connectivityStatus}&requestStatus={requestStatus}
/api/short-code-bulk-activation/get-skipped-senders?bulkId={bulkId}&pageIndex={pageIndex}&pageSize={pageSize}
/api/short-code-bulk-activation/get-by-id?id={id}
/api/payment-transaction/get-all-for-support?{params}
/api/sender-requests/get-request-tracking-information?id={id}
/api/update-request-data/{id}
/api/users?username={username}&email={email}&mobileNumber={mobile}&pageIndex={pageIndex}&pageSize={pageSize}
/api/users/{id}
/api/certificate/{id}
/api/UserInfo
config.json?v={timestamp}

## POST Endpoints:
/api/providerrequest
/api/sender-requests
/api/kpi-dashboard/alerts-list
/api/kpi-dashboard/export-alerts-list
/api/attachmentCategory
/api/citckeywords
/api/contract-requests/get-contract-requests-sql
/api/contract-requests/check-contract-renewal
/api/contract-requests
/api/contract-requests/renew
/api/sender-conectivity-detail
/api/complaint/edit-complaint
/api/sender-requests/edit-request
/api/sender-requests/add-history
/api/change-sender-type-requests/
/api/shortcodes
/api/short-code-bulk-activation
/api/update-request-data/
/api/users
/api/users/resend-password-admin?userName={userName}
/api/kpi-dashboard/request-list-per-status
/api/kpi-dashboard/export-request-list

## PUT Endpoints:
/api/sender-requests/send-add-request-email
/api/attachmentCategory
/api/contract-requests/approve
/api/senders/deactivate
/api/contract-requests/resend
/api/contract-requests/reject
/api/contract-requests/send-contract-approve-email
/api/sender-requests/delete-request-email
/api/contract-requests/reject-contract-request-email
/api/contract-requests/resend-contract-request-email
/api/contract-requests/send-add-contract-request-email
/api/citckeywords
/api/sender-conectivity-detail/mark-as-pending-addition?senderConectivityDetailId={id}
/api/change-sender-type-requests/approve
/api/change-sender-type-requests/resend
/api/complaint/reactivate
/api/complaint/close-complaint
/api/complaint/deactivate-complaint
/api/sender-conectivity-detail/mark-as-added?senderConectivityDetailId={id}
/api/sender-conectivity-detail/mark-as-deleted?senderConectivityDetailId={id}
/api/sender-requests
/api/senders
/api/shortcodes
/api/update-request-data/
/api/users
/api/users/lock?id={id}
/api/users/unlock?id={id}
/api/users/switch-admin-to-super?id={id}

## DELETE Endpoints:
/api/attachmentCategory?id={id}
/api/citckeywords?id={id}
/api/contract-requests?id={id}&comment={comment}
/api/sender-conectivity-detail?senderConectivityDetailId={senderConectivityDetailId}
/api/sender-requests/delete-history?id={id}
/api/shortcodes?id={id}

## Common Parameters:
- id: Resource identifier
- pageIndex: Page number for pagination
- pageSize: Number of items per page
- name: Name filter
- comment: Comment text
- senderId: Sender identifier
- operatorId: Operator identifier
- requestId: Request identifier
- clientName: Client name
- crNumber: CR number
- customerType: Customer type
- senderName: Sender name
- providerName: Provider name
- username: Username
- email: Email address
- mobile/mobileNumber: Mobile number
- invoiceNumber: Invoice number
- paymentDateFrom: Payment date from
- paymentDateTo: Payment date to
- unifiedNumber: Unified number
- type: Type parameter
- requestType: Request type
- bulkId: Bulk identifier
- connectivityStatus: Connectivity status
- requestStatus: Request status
- transactionReference: Transaction reference
- isBalanceInvoice: Balance invoice flag
- userName: User name
- timestamp: Timestamp value
