#!/bin/bash

# Attack Intelligence Extraction from mm.js
# Extracts valuable information for reconnaissance and attack planning

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

INPUT_FILE="mm.js"
OUTPUT_DIR="attack_intel"

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo -e "${PURPLE}=== Attack Intelligence Extraction from mm.js ===${NC}"
echo -e "${YELLOW}Extracting valuable reconnaissance data for security assessment${NC}"
echo ""

# Function to extract and save data
extract_data() {
    local pattern="$1"
    local description="$2"
    local output_file="$3"
    local context_lines="${4:-0}"
    
    echo -e "${BLUE}Extracting: $description${NC}"
    
    if [[ $context_lines -gt 0 ]]; then
        grep -i -A$context_lines -B$context_lines "$pattern" "$INPUT_FILE" > "$OUTPUT_DIR/$output_file" 2>/dev/null
    else
        grep -i -o "$pattern" "$INPUT_FILE" | sort -u > "$OUTPUT_DIR/$output_file" 2>/dev/null
    fi
    
    local count=$(wc -l < "$OUTPUT_DIR/$output_file" 2>/dev/null || echo 0)
    if [[ $count -gt 0 ]]; then
        echo -e "  ${GREEN}✓ Found $count entries → $OUTPUT_DIR/$output_file${NC}"
    else
        echo -e "  ${YELLOW}- No matches found${NC}"
        rm -f "$OUTPUT_DIR/$output_file"
    fi
}

echo -e "${CYAN}=== 1. API Endpoints & Routes ===${NC}"
extract_data "\/api\/[a-zA-Z0-9\/-]*" "API Endpoints" "api_endpoints.txt"
extract_data "axiosInstance\.[a-zA-Z]*(" "HTTP Methods" "http_methods.txt" 2
extract_data "\.get\(.*\)|\.post\(.*\)|\.put\(.*\)|\.delete\(.*\)" "HTTP Calls" "http_calls.txt" 1

echo -e "${CYAN}=== 2. Authentication & Security ===${NC}"
extract_data "['\"]token['\"]|['\"]jwt['\"]|['\"]bearer['\"]" "Authentication Tokens" "auth_tokens.txt" 2
extract_data "localStorage|sessionStorage" "Storage Methods" "storage_methods.txt" 2
extract_data "Authorization|Bearer|X-API-Key" "Auth Headers" "auth_headers.txt" 2
extract_data "login|authenticate|signin|logout" "Auth Functions" "auth_functions.txt" 2

echo -e "${CYAN}=== 3. URLs & Domains ===${NC}"
extract_data "https\?:\/\/[a-zA-Z0-9\.-]*[a-zA-Z0-9]" "External URLs" "external_urls.txt"
extract_data "[a-zA-Z0-9\.-]*\.com|[a-zA-Z0-9\.-]*\.net|[a-zA-Z0-9\.-]*\.org" "Domains" "domains.txt"
extract_data "localhost|127\.0\.0\.1|192\.168\.|10\.|172\." "Internal IPs" "internal_ips.txt"

echo -e "${CYAN}=== 4. Database & Backend Info ===${NC}"
extract_data "mysql|postgresql|mongodb|redis|sqlite" "Database Types" "database_types.txt" 2
extract_data "SELECT|INSERT|UPDATE|DELETE|FROM|WHERE" "SQL Queries" "sql_queries.txt" 2
extract_data "connection|database|db|sql" "DB References" "db_references.txt" 2

echo -e "${CYAN}=== 5. Sensitive Data Patterns ===${NC}"
extract_data "['\"][a-zA-Z0-9]{20,}['\"]" "Potential API Keys" "potential_api_keys.txt"
extract_data "password|secret|key|token" "Sensitive Keywords" "sensitive_keywords.txt" 2
extract_data "['\"][A-Za-z0-9+/]{40,}={0,2}['\"]" "Base64 Strings" "base64_strings.txt"
extract_data "[0-9a-f]{32}|[0-9a-f]{40}|[0-9a-f]{64}" "Hash Values" "hash_values.txt"

echo -e "${CYAN}=== 6. Configuration & Environment ===${NC}"
extract_data "config|configuration|settings|env" "Config References" "config_references.txt" 2
extract_data "development|staging|production|test" "Environment Names" "environments.txt" 2
extract_data "debug|console\.log|console\.error" "Debug Information" "debug_info.txt" 2

echo -e "${CYAN}=== 7. User Roles & Permissions ===${NC}"
extract_data "admin|user|role|permission|access" "User Roles" "user_roles.txt" 2
extract_data "isAdmin|hasPermission|canAccess|authorize" "Permission Checks" "permission_checks.txt" 2

echo -e "${CYAN}=== 8. File Paths & Resources ===${NC}"
extract_data "\/[a-zA-Z0-9\/_-]*\.(js|css|json|xml|txt|pdf|doc)" "File Paths" "file_paths.txt"
extract_data "upload|download|file|document|attachment" "File Operations" "file_operations.txt" 2

echo -e "${CYAN}=== 9. Error Messages & Debugging ===${NC}"
extract_data "error|exception|fail|invalid|unauthorized" "Error Keywords" "error_keywords.txt" 2
extract_data "console\.[a-zA-Z]*\(" "Console Calls" "console_calls.txt" 2

echo -e "${CYAN}=== 10. Business Logic & Functions ===${NC}"
extract_data "function [a-zA-Z_][a-zA-Z0-9_]*\(" "Function Names" "function_names.txt"
extract_data "class [a-zA-Z_][a-zA-Z0-9_]*" "Class Names" "class_names.txt"
extract_data "const [a-zA-Z_][a-zA-Z0-9_]*|let [a-zA-Z_][a-zA-Z0-9_]*|var [a-zA-Z_][a-zA-Z0-9_]*" "Variables" "variables.txt"

echo ""
echo -e "${PURPLE}=== Advanced Pattern Extraction ===${NC}"

# Extract API endpoint patterns with parameters
echo -e "${BLUE}Extracting API patterns with parameters...${NC}"
grep -o '/api/[^"'\'']*' "$INPUT_FILE" | sed 's/[?&][^?&]*//g' | sort -u > "$OUTPUT_DIR/api_patterns.txt"
echo -e "  ${GREEN}✓ API patterns → $OUTPUT_DIR/api_patterns.txt${NC}"

# Extract potential secrets (longer alphanumeric strings)
echo -e "${BLUE}Extracting potential secrets...${NC}"
grep -o '['\''"][a-zA-Z0-9]{25,}['\''"]' "$INPUT_FILE" | sort -u > "$OUTPUT_DIR/potential_secrets.txt"
echo -e "  ${GREEN}✓ Potential secrets → $OUTPUT_DIR/potential_secrets.txt${NC}"

# Extract JavaScript object properties that might be sensitive
echo -e "${BLUE}Extracting object properties...${NC}"
grep -o '[a-zA-Z_][a-zA-Z0-9_]*:' "$INPUT_FILE" | sed 's/://' | sort -u > "$OUTPUT_DIR/object_properties.txt"
echo -e "  ${GREEN}✓ Object properties → $OUTPUT_DIR/object_properties.txt${NC}"

# Extract route parameters
echo -e "${BLUE}Extracting route parameters...${NC}"
grep -o ':[a-zA-Z_][a-zA-Z0-9_]*' "$INPUT_FILE" | sort -u > "$OUTPUT_DIR/route_parameters.txt"
echo -e "  ${GREEN}✓ Route parameters → $OUTPUT_DIR/route_parameters.txt${NC}"

echo ""
echo -e "${PURPLE}=== Creating Attack Vectors Summary ===${NC}"

# Create a comprehensive attack summary
cat > "$OUTPUT_DIR/ATTACK_SUMMARY.md" << 'EOF'
# Attack Intelligence Summary

## 🎯 High-Value Targets

### 1. Authentication Bypass Opportunities
- Check for hardcoded credentials in potential_secrets.txt
- Look for JWT secrets in base64_strings.txt
- Test authentication endpoints from auth_functions.txt

### 2. API Exploitation Vectors
- Test all endpoints in api_endpoints.txt for IDOR
- Check parameter injection in route_parameters.txt
- Test HTTP methods from http_methods.txt

### 3. Information Disclosure
- Access file paths from file_paths.txt
- Check debug endpoints from debug_info.txt
- Test configuration endpoints from config_references.txt

### 4. Privilege Escalation
- Test role-based access from user_roles.txt
- Check permission bypasses in permission_checks.txt
- Look for admin functions in function_names.txt

### 5. Data Exfiltration
- Test file operations from file_operations.txt
- Check database references in db_references.txt
- Look for sensitive data patterns in sensitive_keywords.txt

## 🔍 Reconnaissance Data
- External services: external_urls.txt
- Internal infrastructure: internal_ips.txt
- Technology stack: database_types.txt
- Environment details: environments.txt

## ⚠️ Security Weaknesses to Test
1. **Hardcoded Secrets**: Check potential_api_keys.txt and potential_secrets.txt
2. **Debug Information**: Test endpoints from debug_info.txt
3. **Error Handling**: Trigger errors using error_keywords.txt patterns
4. **File Access**: Test paths from file_paths.txt
5. **SQL Injection**: Test queries from sql_queries.txt

## 🛠️ Next Steps
1. Validate all extracted endpoints
2. Test authentication mechanisms
3. Check for parameter tampering
4. Test file access controls
5. Verify permission boundaries
EOF

echo -e "${GREEN}✓ Attack summary created → $OUTPUT_DIR/ATTACK_SUMMARY.md${NC}"

echo ""
echo -e "${PURPLE}=== Extraction Complete ===${NC}"
echo -e "${YELLOW}Results saved in: $OUTPUT_DIR/${NC}"
echo ""
echo -e "${BLUE}Quick Analysis:${NC}"

# Count findings
total_files=$(find "$OUTPUT_DIR" -name "*.txt" | wc -l)
total_entries=0
for file in "$OUTPUT_DIR"/*.txt; do
    if [[ -f "$file" ]]; then
        count=$(wc -l < "$file" 2>/dev/null || echo 0)
        total_entries=$((total_entries + count))
    fi
done

echo -e "  📁 Files created: $total_files"
echo -e "  📊 Total entries: $total_entries"
echo -e "  🎯 High-priority files to review:"
echo -e "     • api_endpoints.txt (API attack surface)"
echo -e "     • potential_secrets.txt (hardcoded credentials)"
echo -e "     • auth_functions.txt (authentication bypass)"
echo -e "     • file_paths.txt (information disclosure)"
echo -e "     • user_roles.txt (privilege escalation)"

echo ""
echo -e "${GREEN}🚀 Ready for security assessment!${NC}"
