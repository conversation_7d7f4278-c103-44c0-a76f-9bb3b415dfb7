GET /api/attachmentCategory?name=test&pageIndex=1&pageSize=10 - HTTP 200
GET /api/attachmentCategory/1 - HTTP 200
GET /api/attachmentCategory/getall - HTTP 200
GET /api/senders/getSuggestedNames?clientName=test&senderType=1 - HTTP 200
GET /api/mci-controller/verifyCR?unifiedNumber=********** - HTTP 200
GET /api/kpi-dashboard/get-alert-days-configuration?type=1 - HTTP 200
GET /api/citckeywords?name=test&pageIndex=1&pageSize=10 - HTTP 200
GET /api/citckeywords/1 - HTTP 200
GET /api/citckeywords/getall - HTTP 200
GET /api/payment-transaction/getallbalanceinvoices?invoiceNumber=&providerName=&paymentDateFrom=&paymentDateTo=&pageIndex=1&pageSize=10 - HTTP 200
GET /api/payment-transaction/downloadinvoice?invoiceIdentifier=test&isBalanceInvoice=true - HTTP 200
GET /api/contract-requests/get-contract-options - HTTP 200
GET /api/providers/is-provider-locked - HTTP 200
GET /api/providers/is-provider-expired - HTTP 200
GET /api/contract-requests/contract-dashboard-boxes - HTTP 200
GET /api/contract-requests/1 - HTTP 200
GET /api/contract-requests/attachments/1 - HTTP 200
GET /api/contract-requests/get-request-approval-history/1 - HTTP 200
GET /api/senders/get-sender-for-contract-request?clientName=test&crNumber=123&customerType=1&enterpriseUnifiedNumber=123 - HTTP 200
GET /api/report/connectivity-activation-report?requestId=1&senderName=test&pageIndex=1&pageSize=10 - HTTP 200
GET /api/sender-conectivity-detail?senderId=1 - HTTP 200
GET /api/senders/1 - HTTP 200
GET /api/providers/is-provider-dissconected - HTTP 200
GET /api/sender-conectivity-detail-log?connectivityDetailsId=1 - HTTP 200
GET /api/sender-conectivity-detail/connectivity-details-configration - HTTP 200
GET /api/change-sender-type-requests - HTTP 200
GET /api/RejectionReason/get-by-request-type?requestType=1 - HTTP 200
GET /api/senders - HTTP 200
GET /api/complaint/get-details/1 - HTTP 200
GET /api/complaint/attachments/1 - HTTP 200
GET /api/sender-request-support-page/get-sender-support-details?senderName=test - HTTP 200
GET /api/sender-requests/1 - HTTP 200
GET /api/sender-requests/attachments/1 - HTTP 200
GET /api/shortcodes?operatorId=1&name=test&pageIndex=1&pageSize=10 - HTTP 200
GET /api/shortcodes/getall - HTTP 200
GET /api/shortcodes/getById?id=1 - HTTP 200
GET /api/users?username=test&email=<EMAIL>&mobileNumber=123456789&pageIndex=1&pageSize=10 - HTTP 200
GET /api/users/1 - HTTP 200
