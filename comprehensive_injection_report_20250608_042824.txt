================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-08 04:28:24
Total parameters tested: 2
Total vulnerabilities found: 2
================================================================================


SSTI VULNERABILITIES (2 found):
------------------------------------------------------------

[SSTI #1]
URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_variable}}
Evidence: Template engine error: {{undefined_variable}} → undefined
Confidence: LOW
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=%7B%7Bundefined_variable%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9564
  Baseline Status: 200
  Length Difference: 136

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #2]
URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=katana
Parameter: category (query)
Payload: {{undefined_variable}}
Evidence: Template engine error: {{undefined_variable}} → undefined
Confidence: LOW
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_variable%7D%7D&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9462
  Baseline Status: 200
  Length Difference: 34

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------
