# High-Priority API Endpoints for Security Testing

## 🔴 CRITICAL - Administrative Functions
api/users/switch-admin-to-super
api/users/lock
api/users/unlock
api/senders/deactivate
api/contract-requests/approve
api/sender-requests/approve

## 🟠 HIGH - User & Authentication
api/users
api/UserInfo
api/users/{id}

## 🟠 HIGH - Payment & Financial
api/payment-transaction/getallbalanceinvoices
api/payment-transaction/downloadinvoice
api/payment-transaction/getcreditlimitpaymenttransaction

## 🟡 MEDIUM - Document Access
api/certificate/{id}
api/contract-requests/attachments/{id}
api/sender-requests/attachments/{id}
api/complaint/attachments/{id}

## 🟡 MEDIUM - Information Disclosure
api/contract-requests/get-contract-options
api/providers/is-provider-locked
api/providers/is-provider-expired
api/sender-requests/get-requests-options

## 🔵 LOW - General Operations
api/attachmentCategory
api/citckeywords
api/shortcodes
