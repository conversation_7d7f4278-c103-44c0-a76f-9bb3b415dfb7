#!/bin/bash

# Simple IDOR Detection Demo using Content-Length Comparison
# Domain: https://smsidmanagment.sa.zain.com/

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

BASE_URL="https://smsidmanagment.sa.zain.com"

echo -e "${PURPLE}=== Simple IDOR Detection Demo ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL${NC}"
echo ""

# Function to get response info
get_info() {
    local url="$1"
    curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' "$url" -o /dev/null 2>/dev/null
}

echo -e "${BLUE}=== Demonstrating Content-Length Differences ===${NC}"
echo ""

# Test API endpoints (should all return same HTML page)
echo -e "${YELLOW}Testing API endpoints (should return HTML page):${NC}"
api_endpoints=(
    "/api/users"
    "/api/users/1"
    "/api/users/admin"
    "/api/contract-requests"
    "/api/contract-requests/999"
    "/api/senders/1"
)

for endpoint in "${api_endpoints[@]}"; do
    result=$(get_info "$BASE_URL$endpoint")
    http_code=$(echo "$result" | cut -d'|' -f1 | cut -d':' -f2)
    size=$(echo "$result" | cut -d'|' -f2 | cut -d':' -f2)
    echo -e "  ${GREEN}$endpoint: HTTP $http_code | Size: ${size}b${NC}"
done

echo ""
echo -e "${YELLOW}Testing accessible files (should return different content):${NC}"

# Test accessible files
accessible_files=(
    "/config.json"
    "/manifest.json"
    "/favicon.ico"
)

for file in "${accessible_files[@]}"; do
    result=$(get_info "$BASE_URL$file")
    http_code=$(echo "$result" | cut -d'|' -f1 | cut -d':' -f2)
    size=$(echo "$result" | cut -d'|' -f2 | cut -d':' -f2)
    
    if [[ "$size" != "8035" ]]; then
        echo -e "  ${RED}🚨 $file: HTTP $http_code | Size: ${size}b (DIFFERENT!)${NC}"
    else
        echo -e "  ${GREEN}$file: HTTP $http_code | Size: ${size}b${NC}"
    fi
done

echo ""
echo -e "${BLUE}=== IDOR Testing with ID Enumeration ===${NC}"
echo ""

# Function to test IDOR on a specific endpoint
test_idor_endpoint() {
    local base_endpoint="$1"
    local description="$2"
    
    echo -e "${YELLOW}Testing: $base_endpoint - $description${NC}"
    
    # Get baseline
    baseline_result=$(get_info "$BASE_URL$base_endpoint")
    baseline_size=$(echo "$baseline_result" | cut -d'|' -f2 | cut -d':' -f2)
    baseline_http=$(echo "$baseline_result" | cut -d'|' -f1 | cut -d':' -f2)
    
    echo -e "  Baseline: HTTP $baseline_http | Size: ${baseline_size}b"
    
    # Test different IDs
    test_ids=(1 2 5 10 100 999 admin user test)
    unique_responses=0
    
    for id in "${test_ids[@]}"; do
        test_result=$(get_info "$BASE_URL$base_endpoint/$id")
        test_size=$(echo "$test_result" | cut -d'|' -f2 | cut -d':' -f2)
        test_http=$(echo "$test_result" | cut -d'|' -f1 | cut -d':' -f2)
        
        if [[ "$test_size" != "$baseline_size" ]]; then
            echo -e "  ${RED}🚨 ID $id: HTTP $test_http | Size: ${test_size}b (POTENTIAL IDOR!)${NC}"
            ((unique_responses++))
        else
            echo -e "  ${GREEN}✓ ID $id: HTTP $test_http | Size: ${test_size}b${NC}"
        fi
    done
    
    if [[ $unique_responses -gt 0 ]]; then
        echo -e "  ${RED}⚠ RESULT: $unique_responses potential IDOR vulnerabilities detected${NC}"
    else
        echo -e "  ${GREEN}✓ RESULT: No IDOR vulnerabilities detected${NC}"
    fi
    echo ""
}

# Test key endpoints
test_idor_endpoint "/api/users" "User Management"
test_idor_endpoint "/api/contract-requests" "Contract Requests"
test_idor_endpoint "/api/senders" "SMS Senders"

echo -e "${BLUE}=== Parameter-Based IDOR Testing ===${NC}"
echo ""

# Function to test parameter-based IDOR
test_parameter_idor() {
    local endpoint="$1"
    local param="$2"
    local description="$3"
    
    echo -e "${YELLOW}Testing: $endpoint?$param - $description${NC}"
    
    # Get baseline (no parameter)
    baseline_result=$(get_info "$BASE_URL$endpoint")
    baseline_size=$(echo "$baseline_result" | cut -d'|' -f2 | cut -d':' -f2)
    
    echo -e "  Baseline (no param): Size: ${baseline_size}b"
    
    # Test parameter values
    param_values=(1 2 5 10 100 999 admin user)
    unique_responses=0
    
    for value in "${param_values[@]}"; do
        test_result=$(get_info "$BASE_URL$endpoint?$param=$value")
        test_size=$(echo "$test_result" | cut -d'|' -f2 | cut -d':' -f2)
        test_http=$(echo "$test_result" | cut -d'|' -f1 | cut -d':' -f2)
        
        if [[ "$test_size" != "$baseline_size" ]]; then
            echo -e "  ${RED}🚨 $param=$value: HTTP $test_http | Size: ${test_size}b (POTENTIAL IDOR!)${NC}"
            ((unique_responses++))
        else
            echo -e "  ${GREEN}✓ $param=$value: HTTP $test_http | Size: ${test_size}b${NC}"
        fi
    done
    
    if [[ $unique_responses -gt 0 ]]; then
        echo -e "  ${RED}⚠ RESULT: $unique_responses potential parameter IDOR vulnerabilities${NC}"
    else
        echo -e "  ${GREEN}✓ RESULT: No parameter IDOR vulnerabilities detected${NC}"
    fi
    echo ""
}

# Test parameter-based endpoints
test_parameter_idor "/api/users" "id" "User ID Parameter"
test_parameter_idor "/api/shortcodes/getById" "id" "Short Code ID"

echo -e "${BLUE}=== Information Disclosure Testing ===${NC}"
echo ""

# Test for information disclosure
info_files=(
    "/.env"
    "/web.config"
    "/config.json"
    "/package.json"
    "/composer.json"
    "/swagger.json"
    "/api-docs"
    "/debug"
    "/admin"
    "/test"
)

echo -e "${YELLOW}Testing for information disclosure files:${NC}"
disclosed_files=0

for file in "${info_files[@]}"; do
    result=$(get_info "$BASE_URL$file")
    http_code=$(echo "$result" | cut -d'|' -f1 | cut -d':' -f2)
    size=$(echo "$result" | cut -d'|' -f2 | cut -d':' -f2)
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]] && [[ "$size" != "8035" ]] && [[ "$size" -gt "10" ]]; then
        echo -e "  ${RED}🚨 $file: HTTP $http_code | Size: ${size}b (ACCESSIBLE!)${NC}"
        ((disclosed_files++))
    elif [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        echo -e "  ${YELLOW}? $file: HTTP $http_code | Size: ${size}b (Default page)${NC}"
    else
        echo -e "  ${GREEN}✓ $file: HTTP $http_code (Not accessible)${NC}"
    fi
done

echo ""
echo -e "${PURPLE}=== Summary ===${NC}"
echo -e "${BLUE}Content-Length Analysis Results:${NC}"
echo "• API endpoints tested: All return 8035b (HTML application page)"
echo "• This indicates the API requires authentication"
echo "• Information disclosure files found: $disclosed_files"

if [[ $disclosed_files -gt 0 ]]; then
    echo -e "${RED}• ⚠ FINDING: $disclosed_files accessible files detected${NC}"
    echo -e "${YELLOW}• Recommendation: Review file permissions and access controls${NC}"
else
    echo -e "${GREEN}• ✓ No information disclosure detected${NC}"
fi

echo ""
echo -e "${BLUE}IDOR Testing Methodology:${NC}"
echo "1. Compare baseline response size with modified requests"
echo "2. Different content-length indicates different data returned"
echo "3. Successful responses (2xx) with different sizes = potential IDOR"
echo "4. Same content-length across all IDs = likely no IDOR vulnerability"
echo ""
echo -e "${GREEN}✓ Content-length based IDOR testing completed${NC}"
