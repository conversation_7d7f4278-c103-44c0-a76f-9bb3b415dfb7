================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-08 03:07:54
Total parameters tested: 44
Total vulnerabilities found: 18
================================================================================


SSTI VULNERABILITIES (18 found):
------------------------------------------------------------

[SSTI #1]
URL: https://ginandjuice.shop/blog/?back=/blog/&search=katana
Parameter: search (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/blog/?back=%2Fblog%2F&search=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 8663
  Baseline Status: 200
  Length Difference: 20

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsBlog.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmousedown="window.__x4...
------------------------------------------------------------

[SSTI #2]
URL: https://ginandjuice.shop/blog/?search=katana&back=/blog/
Parameter: search (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/blog/?search=%7B%7Bundefined_test_var_123%7D%7D&back=%2Fblog%2F
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 8663
  Baseline Status: 200
  Length Difference: 20

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsBlog.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmousedown="window.__x4...
------------------------------------------------------------

[SSTI #3]
URL: https://ginandjuice.shop/catalog?category=Juice
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9374
  Baseline Status: 200
  Length Difference: -549

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #4]
URL: https://ginandjuice.shop/catalog?category=Juice&searchTerm=katana
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9470
  Baseline Status: 200
  Length Difference: 42

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #5]
URL: https://ginandjuice.shop/catalog?category=Juice&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Juice&searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9588
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #6]
URL: https://ginandjuice.shop/catalog?category=Gin&searchTerm=katana
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9470
  Baseline Status: 200
  Length Difference: 46

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #7]
URL: https://ginandjuice.shop/catalog?category=Gin&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Gin&searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9584
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #8]
URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=katana
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9470
  Baseline Status: 200
  Length Difference: 42

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #9]
URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Books&searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9588
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #10]
URL: https://ginandjuice.shop/catalog?category=Accompaniments&searchTerm=katana
Parameter: category (query)
Payload: <%=7*7%>
Evidence: Mathematical/String expression executed: <%=7*7%> → 49
Confidence: HIGH
Template Engine: ERB/JSP

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%3C%25%3D7%2A7%25%3E&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9440
  Baseline Status: 200
  Length Difference: -6

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #11]
URL: https://ginandjuice.shop/catalog?category=Accompaniments&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Accompaniments&searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9606
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #12]
URL: https://ginandjuice.shop/catalog?category=Accessories&searchTerm=katana
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D&searchTerm=katana
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9470
  Baseline Status: 200
  Length Difference: 30

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #13]
URL: https://ginandjuice.shop/catalog?category=Accessories&searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=Accessories&searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9600
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #14]
URL: https://ginandjuice.shop/catalog?category=Books
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9374
  Baseline Status: 200
  Length Difference: -125

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #15]
URL: https://ginandjuice.shop/catalog?searchTerm=katana
Parameter: searchTerm (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?searchTerm=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9502
  Baseline Status: 200
  Length Difference: 160

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #16]
URL: https://ginandjuice.shop/catalog?category=Accompaniments
Parameter: category (query)
Payload: {{8*8}}
Evidence: Mathematical/String expression executed: {{8*8}} → 64
Confidence: HIGH
Template Engine: Jinja2/Twig

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7B8%2A8%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9336
  Baseline Status: 200
  Length Difference: -1058

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #17]
URL: https://ginandjuice.shop/catalog?category=Accessories
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9374
  Baseline Status: 200
  Length Difference: -1018

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------

[SSTI #18]
URL: https://ginandjuice.shop/catalog?category=Gin
Parameter: category (query)
Payload: #{7*7}
Evidence: Mathematical/String expression executed: #{7*7} → 49
Confidence: HIGH
Template Engine: Freemarker

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%23%7B7%2A7%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9334
  Baseline Status: 200
  Length Difference: -3733

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------
