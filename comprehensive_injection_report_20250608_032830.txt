================================================================================
COMPREHENSIVE INJECTION VULNERABILITY REPORT
================================================================================
Scan completed: 2025-06-08 03:28:30
Total parameters tested: 44
Total vulnerabilities found: 3
================================================================================


SSTI VULNERABILITIES (3 found):
------------------------------------------------------------

[SSTI #1]
URL: https://ginandjuice.shop/blog/?back=/blog/&search=katana
Parameter: search (query)
Payload: ${8*8}
Evidence: Mathematical/String expression executed: ${8*8} → 64
Confidence: HIGH
Template Engine: Freemarker/Velocity

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/blog/?back=%2Fblog%2F&search=%24%7B8%2A8%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 8643
  Baseline Status: 200
  Length Difference: 0

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsBlog.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmousedown="window.__x4...
------------------------------------------------------------

[SSTI #2]
URL: https://ginandjuice.shop/blog/?search=katana&back=/blog/
Parameter: search (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/blog/?search=%7B%7Bundefined_test_var_123%7D%7D&back=%2Fblog%2F
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 8663
  Baseline Status: 200
  Length Difference: 20

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsBlog.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmousedown="window.__x4...
------------------------------------------------------------

[SSTI #3]
URL: https://ginandjuice.shop/catalog?category=Juice
Parameter: category (query)
Payload: {{undefined_test_var_123}}
Evidence: Template engine error: {{undefined_test_var_123}} → undefined
Confidence: MEDIUM
Template Engine: Template Engine

REQUEST DETAILS:
  Method: GET
  URL: https://ginandjuice.shop/catalog?category=%7B%7Bundefined_test_var_123%7D%7D
  Headers: {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive"
}

RESPONSE DETAILS:
  Status Code: 200
  Content Length: 9374
  Baseline Status: 200
  Length Difference: -549

RESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:
<!DOCTYPE html>
<html>
    <head>
        <link href=/resources/labheader/css/scanMeHeader.css rel=stylesheet>
        <link href=/resources/css/labsEcommerce.css rel=stylesheet>
        <link href=/resources/css/labsScanme.css rel=stylesheet>
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <script src="/resources/js/react.development.js"></script>
        <script src="/resources/js/react-dom.development.js"></script>
        <script type="text/javascript" src="/resources/js/angular_1-7-7.js"></script>
        <title>Products - Gin &amp; Juice Shop</title>
    </head>
    <body ng-app>
        <div id="scanMeHeader">
            <section class="header-description">
                <p>
                    This is a deliberately vulnerable web application designed for testing web&nbsp;vulnerability&nbsp;scanners.
                    <span class="link" onmouseenter="window.__x1 = 1" onmouseover="window.__x2 = 1" onmousemove="window.__x3 = 1"  onmoused...
------------------------------------------------------------
