=== Testing: /api/users - User Management ===
Baseline: https://smsidmanagment.sa.zain.com/api/users
  HTTP: 200 | Size: 8035b | Time: 0.651298s
Test: https://smsidmanagment.sa.zain.com/api/users/1
  HTTP: 200 | Size: 8035b | Time: 0.618756s
Test: https://smsidmanagment.sa.zain.com/api/users/2
  HTTP: 200 | Size: 8035b | Time: 0.614680s
Test: https://smsidmanagment.sa.zain.com/api/users/3
  HTTP: 200 | Size: 8035b | Time: 0.660842s
Test: https://smsidmanagment.sa.zain.com/api/users/5
  HTTP: 200 | Size: 8035b | Time: 0.691656s
Test: https://smsidmanagment.sa.zain.com/api/users/10
  HTTP: 200 | Size: 8035b | Time: 0.725994s
Test: https://smsidmanagment.sa.zain.com/api/users/100
  HTTP: 200 | Size: 8035b | Time: 0.675387s
Test: https://smsidmanagment.sa.zain.com/api/users/999
  HTTP: 200 | Size: 8035b | Time: 0.675363s
Test: https://smsidmanagment.sa.zain.com/api/users/1000
  HTTP: 200 | Size: 8035b | Time: 0.628749s
Test: https://smsidmanagment.sa.zain.com/api/users/9999
  HTTP: 200 | Size: 8035b | Time: 0.719080s
Test: https://smsidmanagment.sa.zain.com/api/users/-1
  HTTP: 200 | Size: 8035b | Time: 0.611944s
Test: https://smsidmanagment.sa.zain.com/api/users/0
  HTTP: 200 | Size: 8035b | Time: 0.636402s
Test: https://smsidmanagment.sa.zain.com/api/users/admin
  HTTP: 200 | Size: 8035b | Time: 0.626171s
Test: https://smsidmanagment.sa.zain.com/api/users/user
  HTTP: 200 | Size: 8035b | Time: 0.665244s
Test: https://smsidmanagment.sa.zain.com/api/users/test
  HTTP: 200 | Size: 8035b | Time: 0.689975s
Test: https://smsidmanagment.sa.zain.com/api/users/null
  HTTP: 200 | Size: 8035b | Time: 0.634612s
Test: https://smsidmanagment.sa.zain.com/api/users/undefined
  HTTP: 200 | Size: 8035b | Time: 0.625583s
Test: https://smsidmanagment.sa.zain.com/api/users?id=1
  HTTP: 200 | Size: 8035b | Time: 0.796502s
Test: https://smsidmanagment.sa.zain.com/api/users?id=2
  HTTP: 200 | Size: 8035b | Time: 0.641948s
Test: https://smsidmanagment.sa.zain.com/api/users?id=3
  HTTP: 200 | Size: 8035b | Time: 0.630845s
Test: https://smsidmanagment.sa.zain.com/api/users?id=5
  HTTP: 200 | Size: 8035b | Time: 0.693803s
Test: https://smsidmanagment.sa.zain.com/api/users?id=10
  HTTP: 200 | Size: 8035b | Time: 1.032288s
Test: https://smsidmanagment.sa.zain.com/api/users?id=100
  HTTP: 200 | Size: 8035b | Time: 0.896062s
Test: https://smsidmanagment.sa.zain.com/api/users?id=999
  HTTP: 200 | Size: 8035b | Time: 0.699495s
Test: https://smsidmanagment.sa.zain.com/api/users?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.747624s
Test: https://smsidmanagment.sa.zain.com/api/users?id=0
  HTTP: 200 | Size: 8035b | Time: 1.586534s
Test: https://smsidmanagment.sa.zain.com/api/users?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.694812s
Test: https://smsidmanagment.sa.zain.com/api/users?id=user
  HTTP: 200 | Size: 8035b | Time: 0.985290s
Test: https://smsidmanagment.sa.zain.com/api/users?id=test
  HTTP: 200 | Size: 8035b | Time: 0.929931s
Test: https://smsidmanagment.sa.zain.com/api/users/1.json
  HTTP: 200 | Size: 8035b | Time: 1.136428s
Test: https://smsidmanagment.sa.zain.com/api/users/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.626327s
Test: https://smsidmanagment.sa.zain.com/api/users/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.763227s
Test: https://smsidmanagment.sa.zain.com/api/users/1.php
  HTTP: 200 | Size: 8035b | Time: 0.725742s
Test: https://smsidmanagment.sa.zain.com/api/users/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.670151s
Test: https://smsidmanagment.sa.zain.com/api/users/../1
  HTTP: 200 | Size: 8035b | Time: 0.741173s
Test: https://smsidmanagment.sa.zain.com/api/users/../../2
  HTTP: 200 | Size: 8035b | Time: 0.641972s
Test: https://smsidmanagment.sa.zain.com/api/users/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.941405s
Test: https://smsidmanagment.sa.zain.com/api/users?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.603451s
Test: https://smsidmanagment.sa.zain.com/api/users?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.617074s
Test: https://smsidmanagment.sa.zain.com/api/users?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.638007s
Test: https://smsidmanagment.sa.zain.com/api/users?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.645211s

Summary for /api/users:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/attachmentCategory - Attachment Categories ===
Baseline: https://smsidmanagment.sa.zain.com/api/attachmentCategory
  HTTP: 200 | Size: 8035b | Time: 0.609325s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1
  HTTP: 200 | Size: 8035b | Time: 0.620591s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/2
  HTTP: 200 | Size: 8035b | Time: 0.619352s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/3
  HTTP: 200 | Size: 8035b | Time: 0.639668s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/5
  HTTP: 200 | Size: 8035b | Time: 0.646285s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/10
  HTTP: 200 | Size: 8035b | Time: 0.631483s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/100
  HTTP: 200 | Size: 8035b | Time: 0.839524s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/999
  HTTP: 200 | Size: 8035b | Time: 0.652371s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1000
  HTTP: 200 | Size: 8035b | Time: 0.615388s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/9999
  HTTP: 200 | Size: 8035b | Time: 0.655821s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/-1
  HTTP: 200 | Size: 8035b | Time: 0.702948s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/0
  HTTP: 200 | Size: 8035b | Time: 0.675233s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/admin
  HTTP: 200 | Size: 8035b | Time: 0.612820s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/user
  HTTP: 200 | Size: 8035b | Time: 0.619285s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/test
  HTTP: 200 | Size: 8035b | Time: 0.615929s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/null
  HTTP: 200 | Size: 8035b | Time: 0.670851s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/undefined
  HTTP: 200 | Size: 8035b | Time: 0.621969s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=1
  HTTP: 200 | Size: 8035b | Time: 0.608415s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=2
  HTTP: 200 | Size: 8035b | Time: 0.811095s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=3
  HTTP: 200 | Size: 8035b | Time: 0.640712s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=5
  HTTP: 200 | Size: 8035b | Time: 0.613976s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=10
  HTTP: 200 | Size: 8035b | Time: 0.622547s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=100
  HTTP: 200 | Size: 8035b | Time: 0.695275s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=999
  HTTP: 200 | Size: 8035b | Time: 0.623656s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.625363s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=0
  HTTP: 200 | Size: 8035b | Time: 0.617295s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.717884s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=user
  HTTP: 200 | Size: 8035b | Time: 0.639009s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?id=test
  HTTP: 200 | Size: 8035b | Time: 0.616183s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1.json
  HTTP: 200 | Size: 8035b | Time: 0.614623s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.679048s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.775708s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1.php
  HTTP: 200 | Size: 8035b | Time: 0.641518s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.815051s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/../1
  HTTP: 200 | Size: 8035b | Time: 0.738845s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/../../2
  HTTP: 200 | Size: 8035b | Time: 0.729641s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.621269s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.642101s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.637672s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.619456s
Test: https://smsidmanagment.sa.zain.com/api/attachmentCategory?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.609617s

Summary for /api/attachmentCategory:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/citckeywords - CITC Keywords ===
Baseline: https://smsidmanagment.sa.zain.com/api/citckeywords
  HTTP: 200 | Size: 8035b | Time: 0.690066s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1
  HTTP: 200 | Size: 8035b | Time: 0.669594s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/2
  HTTP: 200 | Size: 8035b | Time: 0.619567s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/3
  HTTP: 200 | Size: 8035b | Time: 0.616722s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/5
  HTTP: 200 | Size: 8035b | Time: 0.657348s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/10
  HTTP: 200 | Size: 8035b | Time: 0.634670s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/100
  HTTP: 200 | Size: 8035b | Time: 0.634820s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/999
  HTTP: 200 | Size: 8035b | Time: 0.705626s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1000
  HTTP: 200 | Size: 8035b | Time: 0.624455s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/9999
  HTTP: 200 | Size: 8035b | Time: 0.714321s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/-1
  HTTP: 200 | Size: 8035b | Time: 0.659937s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/0
  HTTP: 200 | Size: 8035b | Time: 0.629863s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/admin
  HTTP: 200 | Size: 8035b | Time: 0.601085s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/user
  HTTP: 200 | Size: 8035b | Time: 0.623727s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/test
  HTTP: 200 | Size: 8035b | Time: 0.597291s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/null
  HTTP: 200 | Size: 8035b | Time: 0.619080s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/undefined
  HTTP: 200 | Size: 8035b | Time: 0.661962s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=1
  HTTP: 200 | Size: 8035b | Time: 0.617265s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=2
  HTTP: 200 | Size: 8035b | Time: 0.689703s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=3
  HTTP: 200 | Size: 8035b | Time: 0.644252s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=5
  HTTP: 200 | Size: 8035b | Time: 0.638615s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=10
  HTTP: 200 | Size: 8035b | Time: 0.689931s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=100
  HTTP: 200 | Size: 8035b | Time: 0.693275s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=999
  HTTP: 200 | Size: 8035b | Time: 0.792469s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.668492s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=0
  HTTP: 200 | Size: 8035b | Time: 0.614143s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.669769s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=user
  HTTP: 200 | Size: 8035b | Time: 0.675521s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?id=test
  HTTP: 200 | Size: 8035b | Time: 0.631560s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1.json
  HTTP: 200 | Size: 8035b | Time: 0.682666s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.668517s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.622394s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1.php
  HTTP: 200 | Size: 8035b | Time: 0.629972s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.629143s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/../1
  HTTP: 200 | Size: 8035b | Time: 0.631271s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/../../2
  HTTP: 200 | Size: 8035b | Time: 0.613081s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.646535s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.686646s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.623501s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.605850s
Test: https://smsidmanagment.sa.zain.com/api/citckeywords?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.655165s

Summary for /api/citckeywords:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/contract-requests - Contract Requests ===
Baseline: https://smsidmanagment.sa.zain.com/api/contract-requests
  HTTP: 200 | Size: 8035b | Time: 0.697839s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1
  HTTP: 200 | Size: 8035b | Time: 0.695327s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/2
  HTTP: 200 | Size: 8035b | Time: 0.655178s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/3
  HTTP: 200 | Size: 8035b | Time: 0.625408s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/5
  HTTP: 200 | Size: 8035b | Time: 0.615019s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/10
  HTTP: 200 | Size: 8035b | Time: 0.646707s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/100
  HTTP: 200 | Size: 8035b | Time: 0.615334s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/999
  HTTP: 200 | Size: 8035b | Time: 0.612018s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1000
  HTTP: 200 | Size: 8035b | Time: 0.611638s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/9999
  HTTP: 200 | Size: 8035b | Time: 0.603660s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/-1
  HTTP: 200 | Size: 8035b | Time: 0.660243s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/0
  HTTP: 200 | Size: 8035b | Time: 0.666566s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/admin
  HTTP: 200 | Size: 8035b | Time: 0.633945s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/user
  HTTP: 200 | Size: 8035b | Time: 0.605344s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/test
  HTTP: 200 | Size: 8035b | Time: 0.619803s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/null
  HTTP: 200 | Size: 8035b | Time: 0.642778s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/undefined
  HTTP: 200 | Size: 8035b | Time: 0.766145s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=1
  HTTP: 200 | Size: 8035b | Time: 0.651765s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=2
  HTTP: 200 | Size: 8035b | Time: 0.664289s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=3
  HTTP: 200 | Size: 8035b | Time: 0.697361s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=5
  HTTP: 200 | Size: 8035b | Time: 0.614085s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=10
  HTTP: 200 | Size: 8035b | Time: 0.612752s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=100
  HTTP: 200 | Size: 8035b | Time: 0.632787s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=999
  HTTP: 200 | Size: 8035b | Time: 0.613038s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.620315s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=0
  HTTP: 200 | Size: 8035b | Time: 0.615410s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.641412s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=user
  HTTP: 200 | Size: 8035b | Time: 0.634590s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?id=test
  HTTP: 200 | Size: 8035b | Time: 0.746223s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1.json
  HTTP: 200 | Size: 8035b | Time: 0.663152s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.612684s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.638130s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1.php
  HTTP: 200 | Size: 8035b | Time: 0.636821s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.680063s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/../1
  HTTP: 200 | Size: 8035b | Time: 0.628242s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/../../2
  HTTP: 200 | Size: 8035b | Time: 0.632710s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.607687s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.682464s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.677373s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.601973s
Test: https://smsidmanagment.sa.zain.com/api/contract-requests?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.609824s

Summary for /api/contract-requests:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/sender-requests - Sender Requests ===
Baseline: https://smsidmanagment.sa.zain.com/api/sender-requests
  HTTP: 200 | Size: 8035b | Time: 0.896500s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1
  HTTP: 200 | Size: 8035b | Time: 0.603604s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/2
  HTTP: 200 | Size: 8035b | Time: 0.645221s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/3
  HTTP: 200 | Size: 8035b | Time: 0.644472s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/5
  HTTP: 200 | Size: 8035b | Time: 0.623187s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/10
  HTTP: 200 | Size: 8035b | Time: 0.626936s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/100
  HTTP: 200 | Size: 8035b | Time: 0.601960s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/999
  HTTP: 200 | Size: 8035b | Time: 0.624046s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1000
  HTTP: 200 | Size: 8035b | Time: 0.647450s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/9999
  HTTP: 200 | Size: 8035b | Time: 0.636983s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/-1
  HTTP: 200 | Size: 8035b | Time: 0.672014s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/0
  HTTP: 200 | Size: 8035b | Time: 0.695474s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/admin
  HTTP: 200 | Size: 8035b | Time: 0.619445s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/user
  HTTP: 200 | Size: 8035b | Time: 0.638572s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/test
  HTTP: 200 | Size: 8035b | Time: 0.608447s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/null
  HTTP: 200 | Size: 8035b | Time: 0.641461s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/undefined
  HTTP: 200 | Size: 8035b | Time: 0.682921s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=1
  HTTP: 200 | Size: 8035b | Time: 0.663885s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=2
  HTTP: 200 | Size: 8035b | Time: 0.668360s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=3
  HTTP: 200 | Size: 8035b | Time: 0.678673s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=5
  HTTP: 200 | Size: 8035b | Time: 0.622680s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=10
  HTTP: 200 | Size: 8035b | Time: 0.628382s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=100
  HTTP: 200 | Size: 8035b | Time: 0.614946s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=999
  HTTP: 200 | Size: 8035b | Time: 0.703964s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.638690s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=0
  HTTP: 200 | Size: 8035b | Time: 0.612924s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.615306s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=user
  HTTP: 200 | Size: 8035b | Time: 0.711552s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?id=test
  HTTP: 200 | Size: 8035b | Time: 0.777492s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1.json
  HTTP: 200 | Size: 8035b | Time: 0.632777s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.608981s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.634270s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1.php
  HTTP: 200 | Size: 8035b | Time: 0.634719s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.723120s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/../1
  HTTP: 200 | Size: 8035b | Time: 0.614093s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/../../2
  HTTP: 200 | Size: 8035b | Time: 0.654673s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.623537s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.723591s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.669066s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.612937s
Test: https://smsidmanagment.sa.zain.com/api/sender-requests?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.635649s

Summary for /api/sender-requests:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/senders - Senders ===
Baseline: https://smsidmanagment.sa.zain.com/api/senders
  HTTP: 200 | Size: 8035b | Time: 0.640465s
Test: https://smsidmanagment.sa.zain.com/api/senders/1
  HTTP: 200 | Size: 8035b | Time: 0.667871s
Test: https://smsidmanagment.sa.zain.com/api/senders/2
  HTTP: 200 | Size: 8035b | Time: 0.714290s
Test: https://smsidmanagment.sa.zain.com/api/senders/3
  HTTP: 200 | Size: 8035b | Time: 0.641430s
Test: https://smsidmanagment.sa.zain.com/api/senders/5
  HTTP: 200 | Size: 8035b | Time: 0.610000s
Test: https://smsidmanagment.sa.zain.com/api/senders/10
  HTTP: 200 | Size: 8035b | Time: 0.657593s
Test: https://smsidmanagment.sa.zain.com/api/senders/100
  HTTP: 200 | Size: 8035b | Time: 0.630128s
Test: https://smsidmanagment.sa.zain.com/api/senders/999
  HTTP: 200 | Size: 8035b | Time: 0.712700s
Test: https://smsidmanagment.sa.zain.com/api/senders/1000
  HTTP: 200 | Size: 8035b | Time: 0.612659s
Test: https://smsidmanagment.sa.zain.com/api/senders/9999
  HTTP: 200 | Size: 8035b | Time: 0.735006s
Test: https://smsidmanagment.sa.zain.com/api/senders/-1
  HTTP: 200 | Size: 8035b | Time: 0.615480s
Test: https://smsidmanagment.sa.zain.com/api/senders/0
  HTTP: 200 | Size: 8035b | Time: 0.658445s
Test: https://smsidmanagment.sa.zain.com/api/senders/admin
  HTTP: 200 | Size: 8035b | Time: 0.648551s
Test: https://smsidmanagment.sa.zain.com/api/senders/user
  HTTP: 200 | Size: 8035b | Time: 0.645156s
Test: https://smsidmanagment.sa.zain.com/api/senders/test
  HTTP: 200 | Size: 8035b | Time: 0.629902s
Test: https://smsidmanagment.sa.zain.com/api/senders/null
  HTTP: 200 | Size: 8035b | Time: 0.633826s
Test: https://smsidmanagment.sa.zain.com/api/senders/undefined
  HTTP: 200 | Size: 8035b | Time: 0.611801s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=1
  HTTP: 200 | Size: 8035b | Time: 0.608803s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=2
  HTTP: 200 | Size: 8035b | Time: 0.754699s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=3
  HTTP: 200 | Size: 8035b | Time: 0.634994s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=5
  HTTP: 200 | Size: 8035b | Time: 0.644564s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=10
  HTTP: 200 | Size: 8035b | Time: 0.705998s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=100
  HTTP: 200 | Size: 8035b | Time: 0.658685s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=999
  HTTP: 200 | Size: 8035b | Time: 0.639642s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=-1
  HTTP: 200 | Size: 8035b | Time: 0.614136s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=0
  HTTP: 200 | Size: 8035b | Time: 0.667228s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=admin
  HTTP: 200 | Size: 8035b | Time: 0.650653s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=user
  HTTP: 200 | Size: 8035b | Time: 0.652676s
Test: https://smsidmanagment.sa.zain.com/api/senders?id=test
  HTTP: 200 | Size: 8035b | Time: 0.676544s
Test: https://smsidmanagment.sa.zain.com/api/senders/1.json
  HTTP: 200 | Size: 8035b | Time: 0.615408s
Test: https://smsidmanagment.sa.zain.com/api/senders/1.xml
  HTTP: 200 | Size: 8035b | Time: 0.630717s
Test: https://smsidmanagment.sa.zain.com/api/senders/1.txt
  HTTP: 200 | Size: 8035b | Time: 0.695126s
Test: https://smsidmanagment.sa.zain.com/api/senders/1.php
  HTTP: 200 | Size: 8035b | Time: 0.874627s
Test: https://smsidmanagment.sa.zain.com/api/senders/1.asp
  HTTP: 200 | Size: 8035b | Time: 0.747739s
Test: https://smsidmanagment.sa.zain.com/api/senders/../1
  HTTP: 200 | Size: 8035b | Time: 0.658917s
Test: https://smsidmanagment.sa.zain.com/api/senders/../../2
  HTTP: 200 | Size: 8035b | Time: 0.672394s
Test: https://smsidmanagment.sa.zain.com/api/senders/%2e%2e%2f1
  HTTP: 200 | Size: 8035b | Time: 0.754903s
Test: https://smsidmanagment.sa.zain.com/api/senders?userId=1
  HTTP: 200 | Size: 8035b | Time: 0.791248s
Test: https://smsidmanagment.sa.zain.com/api/senders?senderId=1
  HTTP: 200 | Size: 8035b | Time: 0.627310s
Test: https://smsidmanagment.sa.zain.com/api/senders?requestId=1
  HTTP: 200 | Size: 8035b | Time: 0.755262s
Test: https://smsidmanagment.sa.zain.com/api/senders?contractId=1
  HTTP: 200 | Size: 8035b | Time: 0.731087s

Summary for /api/senders:
  Baseline size: 8035b
  Unique response sizes found: 1
  Potential IDOR vulnerabilities: 0
  Unique sizes: 8035

=== Testing: /api/shortcodes - Short Codes ===
Baseline: https://smsidmanagment.sa.zain.com/api/shortcodes
  HTTP: 200 | Size: 8035b | Time: 3.521913s
Test: https://smsidmanagment.sa.zain.com/api/shortcodes/1
  HTTP: 200 | Size: 8035b | Time: 0.835662s
Test: https://smsidmanagment.sa.zain.com/api/shortcodes/2
  HTTP: 200 | Size: 8035b | Time: 0.601768s
Test: https://smsidmanagment.sa.zain.com/api/shortcodes/3
  HTTP: 200 | Size: 8035b | Time: 0.616592s
