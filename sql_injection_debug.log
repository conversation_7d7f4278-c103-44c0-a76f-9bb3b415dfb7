DEBUG: Script top-level execution started (log cleared).
DEBUG: main() function started.

[INFO] Using Katana file 'xxoutput.jsonl'. No --target-url provided, context set to: 'Context from file: xxoutput.jsonl'
[INFO] Cleared/initialized vulnerabilities file: vulnerable_endpoints.txt for context: Context from file: xxoutput.jsonl
[INFO] Starting to parse Katana output file: xxoutput.jsonl for context: Context from file: xxoutput.jsonl
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested QUERY param: 'back' on path '/blog/'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested QUERY param: 'searchTerm' on path '/catalog'
      -> Skipping already tested QUERY param: 'searchTerm' on path '/catalog'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested QUERY param: 'searchTerm' on path '/catalog'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested QUERY param: 'searchTerm' on path '/catalog'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested QUERY param: 'searchTerm' on path '/catalog'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'csrf' on path '/login'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested POST param: 'redir' on path '/catalog/cart'
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
      -> Skipping already tested QUERY param: 'postId' on path '/blog/post'
Target Context Context from file: xxoutput.jsonl: Processed 13 requests. Found 100 potential vulnerabilities.


[SUMMARY FOR Context from file: xxoutput.jsonl] Processing complete. Total potential vulnerabilities found: 100
DEBUG: Script exiting, closing log file.
