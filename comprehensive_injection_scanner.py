#!/usr/bin/env python3
"""
Comprehensive Injection Scanner - SQL Injection + SSTI Detection
Accurate detection with full request/response capture and evidence highlighting
"""

import argparse
import json
import requests
import time
import urllib3
import re
import hashlib
import yaml
import os
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from tqdm import tqdm
from datetime import datetime

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Default payload files
DEFAULT_SQL_PAYLOADS_FILE = "sql_payloads.yaml"
DEFAULT_SSTI_PAYLOADS_FILE = "ssti_payloads.yaml"

# Fallback SQL payloads if YAML file not found
FALLBACK_SQL_PAYLOADS = [
    "{original_value}' OR pg_sleep({delay})--",
    "{original_value}' AND pg_sleep({delay})--",
    "{original_value}' OR SLEEP({delay})--",
    "{original_value}' AND SLEEP({delay})--",
    "{original_value}' AND SLEEP({delay}) -- ",
    "{original_value}' AND pg_sleep({delay}) -- ",
    "{original_value}' WAITFOR DELAY '0:0:{delay}' -- ",
    "{original_value}') AND SLEEP({delay}) -- ",
    "{original_value}' AND IF(1=1,SLEEP({delay}),0) -- ",
    "{original_value}' OR SLEEP({delay}) -- ",
    "{original_value}\" AND SLEEP({delay}) -- ",
]

# Fallback SSTI payloads if YAML file not found
FALLBACK_SSTI_PAYLOADS = [
    {"payload": "{{7*7}}", "expected": "49", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${7*7}", "expected": "49", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "<%=7*7%>", "expected": "49", "type": "math", "engine": "ERB/JSP"},
    {"payload": "{{7*'7'}}", "expected": "7777777", "type": "string", "engine": "Jinja2"},
    {"payload": "{{8*8}}", "expected": "64", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${8*8}", "expected": "64", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "{{config}}", "expected": ["<Config", "SECRET_KEY", "DEBUG"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{request}}", "expected": ["<Request", "werkzeug", "flask"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{undefined_test_var_123}}", "expected": ["undefined", "UndefinedError", "NameError"], "type": "error", "engine": "Template Engine"},
    {"payload": "${undefined_test_var_123}", "expected": ["undefined", "not found", "does not exist"], "type": "error", "engine": "Template Engine"},
]

def load_sql_payloads(payloads_file):
    """Load SQL injection payloads from YAML file - ALL categories"""
    try:
        if os.path.exists(payloads_file):
            with open(payloads_file, 'r') as f:
                payloads_data = yaml.safe_load(f)

                # If it's a list, return as-is
                if isinstance(payloads_data, list):
                    print(f"✅ Loaded {len(payloads_data)} SQL payloads from {payloads_file}")
                    return payloads_data

                # If it's a dict (organized by categories), flatten ALL categories
                if isinstance(payloads_data, dict):
                    all_payloads = []
                    categories_loaded = []

                    for category, payloads in payloads_data.items():
                        if isinstance(payloads, list):
                            all_payloads.extend(payloads)
                            categories_loaded.append(f"{category}({len(payloads)})")
                        elif isinstance(payloads, str):
                            all_payloads.append(payloads)
                            categories_loaded.append(f"{category}(1)")

                    if all_payloads:
                        print(f"✅ Loaded {len(all_payloads)} SQL payloads from {payloads_file}")
                        print(f"   Categories: {', '.join(categories_loaded)}")
                        return all_payloads

                print(f"⚠️  Invalid format in {payloads_file}, using fallback payloads")
                return FALLBACK_SQL_PAYLOADS
        else:
            print(f"⚠️  SQL payloads file {payloads_file} not found, using fallback payloads")
            return FALLBACK_SQL_PAYLOADS
    except Exception as e:
        print(f"❌ Error loading SQL payloads from {payloads_file}: {e}")
        print("Using fallback payloads")
        return FALLBACK_SQL_PAYLOADS

def load_ssti_payloads(payloads_file):
    """Load SSTI payloads from YAML file - ALL categories"""
    try:
        if os.path.exists(payloads_file):
            with open(payloads_file, 'r') as f:
                payloads_data = yaml.safe_load(f)

                # If it's a dict (organized by categories), convert ALL to our format
                if isinstance(payloads_data, dict):
                    formatted_payloads = []
                    categories_loaded = []

                    for category, payloads in payloads_data.items():
                        if isinstance(payloads, list):
                            category_count = 0
                            for payload in payloads:
                                # Convert simple string payloads to our format
                                if isinstance(payload, str):
                                    # Determine payload type and expected result
                                    payload_info = categorize_ssti_payload(payload, category)
                                    formatted_payloads.append(payload_info)
                                    category_count += 1
                                elif isinstance(payload, dict):
                                    # Already in our format
                                    formatted_payloads.append(payload)
                                    category_count += 1

                            if category_count > 0:
                                categories_loaded.append(f"{category}({category_count})")

                    if formatted_payloads:
                        print(f"✅ Loaded {len(formatted_payloads)} SSTI payloads from {payloads_file}")
                        print(f"   Categories: {', '.join(categories_loaded)}")
                        return formatted_payloads

                # If it's a list, assume it's already in our format
                elif isinstance(payloads_data, list):
                    print(f"✅ Loaded {len(payloads_data)} SSTI payloads from {payloads_file}")
                    return payloads_data

                print(f"⚠️  Invalid format in {payloads_file}, using fallback payloads")
                return FALLBACK_SSTI_PAYLOADS
        else:
            print(f"⚠️  SSTI payloads file {payloads_file} not found, using fallback payloads")
            return FALLBACK_SSTI_PAYLOADS
    except Exception as e:
        print(f"❌ Error loading SSTI payloads from {payloads_file}: {e}")
        print("Using fallback payloads")
        return FALLBACK_SSTI_PAYLOADS

def categorize_ssti_payload(payload, category):
    """Convert a simple payload string to our structured format"""

    # Mathematical expressions
    if any(expr in payload for expr in ["7*7", "8*8", "9*9"]):
        if "7*7" in payload:
            expected = "49"
        elif "8*8" in payload:
            expected = "64"
        elif "9*9" in payload:
            expected = "81"
        else:
            expected = "calculated_result"

        return {
            "payload": payload,
            "expected": expected,
            "type": "math",
            "engine": determine_engine_from_payload(payload)
        }

    # String operations
    elif "7*'7'" in payload or '7*"7"' in payload:
        return {
            "payload": payload,
            "expected": "7777777",
            "type": "string",
            "engine": "Jinja2"
        }

    # Object access
    elif any(obj in payload for obj in ["config", "request", "_self", "smarty"]):
        if "config" in payload:
            expected = ["<Config", "SECRET_KEY", "DEBUG"]
            engine = "Flask/Jinja2"
        elif "request" in payload:
            expected = ["<Request", "werkzeug", "flask"]
            engine = "Flask/Jinja2"
        elif "_self" in payload:
            expected = ["Twig_Template", "__TwigTemplate"]
            engine = "Twig"
        elif "smarty" in payload:
            expected = ["Smarty-", "smarty"]
            engine = "Smarty"
        else:
            expected = ["object", "Object"]
            engine = "Template Engine"

        return {
            "payload": payload,
            "expected": expected,
            "type": "object",
            "engine": engine
        }

    # Error-inducing payloads
    elif "undefined" in payload.lower() or "error" in category.lower():
        return {
            "payload": payload,
            "expected": ["undefined", "UndefinedError", "NameError", "not found"],
            "type": "error",
            "engine": "Template Engine"
        }

    # Default case
    else:
        return {
            "payload": payload,
            "expected": ["template", "Template", "error", "Error"],
            "type": "generic",
            "engine": determine_engine_from_payload(payload)
        }

def determine_engine_from_payload(payload):
    """Determine likely template engine from payload syntax"""
    if payload.startswith("{{") and payload.endswith("}}"):
        return "Jinja2/Twig/Handlebars"
    elif payload.startswith("${") and payload.endswith("}"):
        return "Freemarker/Velocity"
    elif payload.startswith("<%=") and payload.endswith("%>"):
        return "ERB/JSP"
    elif payload.startswith("{") and payload.endswith("}"):
        return "Smarty"
    elif payload.startswith("#{") and payload.endswith("}"):
        return "Freemarker"
    else:
        return "Template Engine"

class VulnerabilityScanner:
    def __init__(self, delay=5, verbose=False, sql_payloads_file=None, ssti_payloads_file=None, threads=10):
        self.delay = delay
        self.verbose = verbose
        self.threads = threads
        self.vulnerabilities = []
        self.tested_params = []
        self.lock = threading.Lock()  # Thread safety for shared data
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        }

        # Load payloads from YAML files (ALL categories)
        self.sql_payloads = load_sql_payloads(sql_payloads_file or DEFAULT_SQL_PAYLOADS_FILE)
        self.ssti_payloads = load_ssti_payloads(ssti_payloads_file or DEFAULT_SSTI_PAYLOADS_FILE)
    
    def highlight_evidence(self, content, evidence):
        """Highlight evidence in content for display"""
        if not evidence or not content:
            return content
        
        # Escape special regex characters in evidence
        escaped_evidence = re.escape(str(evidence))
        # Highlight with ANSI colors for terminal display
        highlighted = re.sub(f'({escaped_evidence})', r'>>> \1 <<<', content, flags=re.IGNORECASE)
        return highlighted
    
    def test_sql_injection(self, url, param_name, param_value, param_location):
        """Test for time-based SQL injection with accurate detection"""

        # Use ALL SQL payloads from YAML file for comprehensive testing
        if self.verbose:
            print(f"    Testing {len(self.sql_payloads)} SQL payloads...")

        for payload_template in self.sql_payloads:
            try:
                # Prepare payload
                effective_original_val = param_value if param_value else '1'
                payload = payload_template.replace("{delay}", str(self.delay))
                payload = payload.replace("{original_value}", effective_original_val)
                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    # Measure response time
                    start_time = time.time()
                    try:
                        response = requests.get(test_url, timeout=self.delay+2, verify=False, headers=self.headers)
                        end_time = time.time()
                        response_time = end_time - start_time
                        
                        # Debug output
                        if self.verbose:
                            print(f"    Testing payload: {payload}")
                            print(f"    Response time: {response_time:.2f}s (threshold: {self.delay}s)")

                        # Strict validation: response time must be >= delay (avoid false positives)
                        if response_time >= self.delay:
                            evidence = f"Response time: {response_time:.2f}s (expected: ≥{self.delay}s)"
                            
                            vulnerability = {
                                'type': 'SQL Injection (Time-based)',
                                'url': url,
                                'parameter': param_name,
                                'location': param_location,
                                'payload': payload,
                                'evidence': evidence,
                                'confidence': 'HIGH',
                                'request': {
                                    'method': 'GET',
                                    'url': test_url,
                                    'headers': dict(self.headers)
                                },
                                'response': {
                                    'status_code': response.status_code,
                                    'response_time': f"{response_time:.2f}s",
                                    'content_length': len(response.text),
                                    'headers': dict(response.headers),
                                    'content_preview': response.text[:500] + "..." if len(response.text) > 500 else response.text
                                }
                            }
                            
                            with self.lock:
                                self.vulnerabilities.append(vulnerability)
                            if self.verbose:
                                print(f"    ✅ SQL injection found! Continuing with remaining tests...")
                            return True
                            
                    except requests.exceptions.Timeout:
                        evidence = f"Request timeout after {self.delay+2}s - indicates SLEEP() execution"
                        
                        vulnerability = {
                            'type': 'SQL Injection (Time-based)',
                            'url': url,
                            'parameter': param_name,
                            'location': param_location,
                            'payload': payload,
                            'evidence': evidence,
                            'confidence': 'HIGH',
                            'request': {
                                'method': 'GET',
                                'url': test_url,
                                'headers': dict(self.headers)
                            },
                            'response': {
                                'status_code': 'TIMEOUT',
                                'response_time': f">{self.delay+2}s",
                                'error': 'Request timeout - likely SQL injection'
                            }
                        }
                        
                        with self.lock:
                            self.vulnerabilities.append(vulnerability)
                        if self.verbose:
                            print(f"    ✅ SQL injection timeout found! Continuing with remaining tests...")
                        return True
                        
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SQL payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SQL injection test: {e}")
                continue
        
        return False
    
    def test_ssti_injection(self, url, param_name, param_value, param_location):
        """Test for SSTI with accurate pattern-based detection"""

        # Get baseline response for comparison
        try:
            baseline_response = requests.get(url, timeout=3, verify=False, headers=self.headers)
            baseline_content = baseline_response.text
            baseline_status = baseline_response.status_code
        except:
            return False

        # Use ALL SSTI payloads from YAML file for comprehensive testing
        if self.verbose:
            print(f"    Testing {len(self.ssti_payloads)} SSTI payloads...")

        for payload_info in self.ssti_payloads:
            try:
                payload = payload_info["payload"]
                expected = payload_info["expected"]
                detection_type = payload_info["type"]
                engine = payload_info["engine"]


                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    try:
                        response = requests.get(test_url, timeout=3, verify=False, headers=self.headers)
                        response_content = response.text
                        response_status = response.status_code
                        
                        # STRICT and ACCURATE detection based on payload type
                        vulnerability_detected = False
                        evidence = ""
                        confidence = "LOW"

                        if detection_type == "math" or detection_type == "string":
                            # ULTRA-STRICT mathematical expression validation
                            if isinstance(expected, str) and expected.isdigit():
                                # Look for the exact result in a mathematical context
                                # Must be surrounded by non-alphanumeric characters or at word boundaries
                                import re

                                # Create VERY strict patterns to avoid false positives
                                patterns = [
                                    rf'\b{re.escape(expected)}\b',  # Word boundary
                                    rf'>{re.escape(expected)}<',   # HTML context
                                    rf'"{re.escape(expected)}"',   # Quoted context
                                    rf"'{re.escape(expected)}'",   # Single quoted
                                    rf'\s{re.escape(expected)}\s', # Whitespace surrounded
                                    rf'^{re.escape(expected)}$',   # Exact match
                                    rf'={re.escape(expected)}',    # Assignment context
                                    rf':{re.escape(expected)}',    # JSON value context
                                ]

                                found_in_response = False
                                found_in_baseline = False

                                for pattern in patterns:
                                    if re.search(pattern, response_content):
                                        found_in_response = True
                                    if re.search(pattern, baseline_content):
                                        found_in_baseline = True

                                # Only flag if found in response but NOT in baseline
                                if found_in_response and not found_in_baseline:
                                    # STRICT validation: check multiple conditions
                                    length_diff = abs(len(response_content) - len(baseline_content))

                                    # Must have significant content change AND the result must appear in a meaningful context
                                    meaningful_contexts = [
                                        f'<td>{expected}</td>',     # Table cell
                                        f'<span>{expected}</span>', # Span element
                                        f'<div>{expected}</div>',   # Div element
                                        f'<p>{expected}</p>',       # Paragraph
                                        f': {expected}',            # JSON-like value
                                        f'= {expected}',            # Assignment
                                        f'"{expected}"',            # Quoted value
                                        f"'{expected}'",            # Single quoted
                                    ]

                                    has_meaningful_context = any(ctx in response_content for ctx in meaningful_contexts)

                                    if length_diff > 5 and has_meaningful_context:  # Significant change + meaningful context
                                        vulnerability_detected = True
                                        evidence = f"Mathematical expression executed: {payload} → {expected}"
                                        confidence = "HIGH"

                        elif detection_type == "object":
                            # STRICT object exposure validation
                            if isinstance(expected, list):
                                for indicator in expected:
                                    # Case-sensitive matching for object names to avoid false positives
                                    if len(indicator) > 3:  # Ignore very short indicators
                                        # Look for exact case-sensitive matches in specific contexts
                                        if (indicator in response_content and
                                            indicator not in baseline_content):

                                            # Additional validation: check if it appears in a meaningful context
                                            context_patterns = [
                                                f'>{indicator}<',      # HTML tag content
                                                f'"{indicator}"',      # JSON/attribute value
                                                f"'{indicator}'",      # Single quoted value
                                                f': {indicator}',      # Object property
                                                f'={indicator}',       # Assignment
                                            ]

                                            context_found = any(pattern in response_content for pattern in context_patterns)
                                            if context_found:
                                                vulnerability_detected = True
                                                evidence = f"Template object exposed: {payload} → {indicator}"
                                                confidence = "MEDIUM"
                                                break

                        elif detection_type == "error":
                            # ULTRA-STRICT error detection - require specific template engine error patterns
                            if isinstance(expected, list):
                                for error_indicator in expected:
                                    if len(error_indicator) > 4:  # Ignore very short error indicators
                                        # Case-sensitive matching for error messages
                                        if (error_indicator in response_content and
                                            error_indicator not in baseline_content):

                                            # Must be accompanied by SPECIFIC template engine error context
                                            specific_template_errors = [
                                                "TemplateSyntaxError", "UndefinedError", "TemplateNotFound",
                                                "TemplateRuntimeError", "TemplateAssertionError",
                                                "Jinja2.exceptions", "twig.Error", "SmartyException",
                                                "FreemarkerException", "VelocityException",
                                                "template syntax error", "Template syntax error",
                                                "undefined variable", "Undefined variable",
                                                "variable is not defined", "Variable is not defined"
                                            ]

                                            # Require BOTH the error indicator AND a specific template error context
                                            has_template_error = any(ctx in response_content for ctx in specific_template_errors)

                                            # Additional check: response must be significantly different
                                            length_diff = abs(len(response_content) - len(baseline_content))

                                            if has_template_error and length_diff > 50:
                                                vulnerability_detected = True
                                                evidence = f"Template engine error: {payload} → {error_indicator}"
                                                confidence = "MEDIUM"
                                                break

                        # STRICT status code validation
                        if (not vulnerability_detected and
                            response_status != baseline_status and
                            response_status in [500, 400, 422, 403]):

                            # Must contain actual template-related error messages
                            strict_template_errors = [
                                "TemplateSyntaxError", "UndefinedError", "TemplateNotFound",
                                "Jinja2", "Twig", "Smarty", "Freemarker", "Velocity",
                                "template syntax", "Template syntax", "TEMPLATE SYNTAX"
                            ]

                            if any(error in response_content for error in strict_template_errors):
                                vulnerability_detected = True
                                evidence = f"Template processing error: {payload} → HTTP {response_status}"
                                confidence = "HIGH"
                        
                        if vulnerability_detected:
                            # DOUBLE VERIFICATION: Test with a different payload to confirm
                            verification_passed = False

                            if detection_type == "math":
                                # Try a different mathematical expression
                                verification_payloads = ["{{8*8}}", "${8*8}", "<%=8*8%>", "#{8*8}", "{8*8}"]
                                verification_expected = "64"

                                for verify_payload in verification_payloads:
                                    if verify_payload != payload:  # Don't use the same payload
                                        try:
                                            verify_params = query_params.copy()
                                            verify_params[param_name] = [verify_payload]
                                            verify_query = urlencode(verify_params, doseq=True)
                                            verify_url = urlunparse((
                                                parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                                                parsed_url.params, verify_query, parsed_url.fragment
                                            ))

                                            verify_response = requests.get(verify_url, timeout=3, verify=False, headers=self.headers)
                                            verify_content = verify_response.text

                                            # Check if verification payload also works
                                            import re
                                            verify_patterns = [
                                                rf'\b{re.escape(verification_expected)}\b',
                                                rf'>{re.escape(verification_expected)}<',
                                                rf'"{re.escape(verification_expected)}"',
                                                rf"'{re.escape(verification_expected)}'",
                                            ]

                                            verify_found = any(re.search(pattern, verify_content) for pattern in verify_patterns)
                                            verify_baseline = any(re.search(pattern, baseline_content) for pattern in verify_patterns)

                                            if verify_found and not verify_baseline:
                                                verification_passed = True
                                                evidence += f" [Verified with {verify_payload} → {verification_expected}]"
                                                break

                                        except:
                                            continue
                            else:
                                # For non-math payloads, accept single confirmation but lower confidence
                                verification_passed = True
                                if confidence == "HIGH":
                                    confidence = "MEDIUM"
                                elif confidence == "MEDIUM":
                                    confidence = "LOW"

                            # Only report if verification passed
                            if verification_passed:
                                # Highlight evidence in response content
                                highlighted_content = self.highlight_evidence(response_content, evidence.split(" → ")[-1].split(" [")[0] if " → " in evidence else "")

                                vulnerability = {
                                    'type': 'Server-Side Template Injection (SSTI)',
                                    'url': url,
                                    'parameter': param_name,
                                    'location': param_location,
                                    'payload': payload,
                                    'evidence': evidence,
                                    'confidence': confidence,
                                    'template_engine': engine,
                                    'request': {
                                        'method': 'GET',
                                        'url': test_url,
                                        'headers': dict(self.headers)
                                    },
                                    'response': {
                                        'status_code': response_status,
                                        'content_length': len(response_content),
                                        'headers': dict(response.headers),
                                        'content_preview': response_content[:1000] + "..." if len(response_content) > 1000 else response_content,
                                        'highlighted_content': highlighted_content[:1000] + "..." if len(highlighted_content) > 1000 else highlighted_content
                                    },
                                    'baseline_comparison': {
                                        'baseline_status': baseline_status,
                                        'baseline_length': len(baseline_content),
                                        'length_difference': len(response_content) - len(baseline_content)
                                    }
                                }

                                with self.lock:
                                    self.vulnerabilities.append(vulnerability)
                                if self.verbose:
                                    print(f"    ✅ SSTI found and verified! Continuing with remaining tests...")
                                return True
                            
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SSTI payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SSTI test: {e}")
                continue
        
        return False

    def scan_parameter(self, url, param_name, param_value, param_location, test_types):
        """Scan a single parameter for both SQL injection and SSTI"""

        # Log tested parameter (thread-safe)
        with self.lock:
            self.tested_params.append({
                'url': url,
                'parameter': param_name,
                'value': param_value,
                'location': param_location,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

        vulnerabilities_found = 0

        # Debug output to show what's being tested
        if self.verbose:
            test_types_str = ', '.join(test_types)
            print(f"    Testing {param_name} for: {test_types_str.upper()}")

        # Test SQL injection
        if 'sql' in test_types:
            if self.verbose:
                print(f"    → Testing SQL injection on {param_name}")
            else:
                print(f"    → Testing SQL injection on {param_name}")
            if self.test_sql_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SQL INJECTION - {param_name} in {url}")
            else:
                if self.verbose:
                    print(f"    ✓ SQL injection testing completed for {param_name} - no vulnerabilities found")

        # Test SSTI
        if 'ssti' in test_types:
            if self.verbose:
                print(f"    → Testing SSTI on {param_name}")
            else:
                print(f"    → Testing SSTI on {param_name}")
            if self.test_ssti_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SSTI - {param_name} in {url}")
            else:
                if self.verbose:
                    print(f"    ✓ SSTI testing completed for {param_name} - no vulnerabilities found")

        return vulnerabilities_found > 0

    def process_katana_file(self, katana_file, test_types):
        """Process Katana output file and scan for vulnerabilities"""

        unique_parameters = []
        processed_requests = 0

        print(f"📊 Collecting parameters from {katana_file}...")

        try:
            with open(katana_file, 'r') as f:
                for line in f:
                    try:
                        if not line.strip():
                            continue

                        katana_entry = json.loads(line.strip())
                        endpoint = katana_entry.get('request', {}).get('endpoint', '') or katana_entry.get('request', {}).get('url', '')

                        if not endpoint:
                            continue

                        processed_requests += 1

                        # Parse URL for query parameters
                        parsed_url = urlparse(endpoint)
                        query_params = parse_qs(parsed_url.query)

                        # Collect unique parameters
                        for param_name, param_values in query_params.items():
                            param_value = param_values[0] if param_values else ""
                            param_key = f"{endpoint}#{param_name}"

                            # Avoid duplicates
                            if not any(p['key'] == param_key for p in unique_parameters):
                                unique_parameters.append({
                                    'key': param_key,
                                    'url': endpoint,
                                    'param_name': param_name,
                                    'param_value': param_value,
                                    'param_location': 'query'
                                })

                    except json.JSONDecodeError:
                        if self.verbose:
                            print(f"Warning: Invalid JSON in line")
                        continue
                    except Exception as e:
                        if self.verbose:
                            print(f"Error processing line: {e}")
                        continue

        except Exception as e:
            print(f"Error reading Katana file: {e}")
            return 0, 0

        print(f"🧪 Testing {len(unique_parameters)} unique parameters with {self.threads} threads...")

        # Scan parameters with multithreading and progress bar
        def scan_single_param(param_info):
            """Wrapper function for threading"""
            try:
                return self.scan_parameter(
                    param_info['url'],
                    param_info['param_name'],
                    param_info['param_value'],
                    param_info['param_location'],
                    test_types
                )
            except Exception as e:
                if self.verbose:
                    print(f"Error scanning parameter {param_info['param_name']}: {e}")
                return False

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            with tqdm(total=len(unique_parameters), desc="Scanning parameters", unit="param") as pbar:
                # Submit all tasks
                future_to_param = {
                    executor.submit(scan_single_param, param_info): param_info
                    for param_info in unique_parameters
                }

                # Process completed tasks
                for future in as_completed(future_to_param):
                    param_info = future_to_param[future]
                    try:
                        result = future.result()
                    except Exception as e:
                        if self.verbose:
                            print(f"Exception in thread for {param_info['param_name']}: {e}")
                    finally:
                        pbar.update(1)

        return processed_requests, len(unique_parameters)

    def save_detailed_report(self):
        """Save comprehensive vulnerability report with full request/response details"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"comprehensive_injection_report_{timestamp}.txt"

        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("COMPREHENSIVE INJECTION VULNERABILITY REPORT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total parameters tested: {len(self.tested_params)}\n")
            f.write(f"Total vulnerabilities found: {len(self.vulnerabilities)}\n")
            f.write("=" * 80 + "\n\n")

            # Group vulnerabilities by type
            sql_vulns = [v for v in self.vulnerabilities if 'SQL' in v['type']]
            ssti_vulns = [v for v in self.vulnerabilities if 'SSTI' in v['type']]

            if sql_vulns:
                f.write(f"SQL INJECTION VULNERABILITIES ({len(sql_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(sql_vulns, 1):
                    f.write(f"\n[SQL INJECTION #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")
                    f.write(f"  Response Time: {vuln['response']['response_time']}\n")

                    # Handle optional fields that may not exist in SQL injection vulnerabilities
                    if 'content_length' in vuln['response']:
                        f.write(f"  Content Length: {vuln['response']['content_length']}\n")
                    if 'content_preview' in vuln['response']:
                        f.write(f"  Content Preview:\n{vuln['response']['content_preview']}\n")

                    f.write("-" * 60 + "\n")

            if ssti_vulns:
                f.write(f"\nSSTI VULNERABILITIES ({len(ssti_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(ssti_vulns, 1):
                    f.write(f"\n[SSTI #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")
                    f.write(f"Template Engine: {vuln['template_engine']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")

                    # Handle optional fields
                    if 'content_length' in vuln['response']:
                        f.write(f"  Content Length: {vuln['response']['content_length']}\n")

                    if 'baseline_comparison' in vuln:
                        f.write(f"  Baseline Status: {vuln['baseline_comparison']['baseline_status']}\n")
                        f.write(f"  Length Difference: {vuln['baseline_comparison']['length_difference']}\n")

                    f.write(f"\nRESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:\n")
                    if 'highlighted_content' in vuln['response']:
                        f.write(f"{vuln['response']['highlighted_content']}\n")
                    elif 'content_preview' in vuln['response']:
                        f.write(f"{vuln['response']['content_preview']}\n")
                    else:
                        f.write("No content preview available\n")

                    f.write("-" * 60 + "\n")

        # Save tested parameters log
        params_file = f"tested_parameters_{timestamp}.txt"
        with open(params_file, 'w') as f:
            f.write("# Comprehensive Injection Scanner - Tested Parameters\n")
            f.write(f"# Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total parameters tested: {len(self.tested_params)}\n\n")

            for param in self.tested_params:
                f.write(f"{param['timestamp']} - {param['url']} - {param['location']} parameter: {param['parameter']}={param['value']}\n")

        return report_file, params_file

def main():
    parser = argparse.ArgumentParser(description="Comprehensive Injection Scanner (SQL + SSTI) - FAST AS HELL!")
    parser.add_argument("--katana-file", "-k", required=True, help="Path to Katana output file (JSONL format)")
    parser.add_argument("--delay", "-d", type=int, default=2, help="Delay in seconds for SQL injection testing (default: 2)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--sql-payloads", "-sp", default=DEFAULT_SQL_PAYLOADS_FILE, help=f"Path to SQL payloads YAML file (default: {DEFAULT_SQL_PAYLOADS_FILE})")
    parser.add_argument("--ssti-payloads", "-ssp", default=DEFAULT_SSTI_PAYLOADS_FILE, help=f"Path to SSTI payloads YAML file (default: {DEFAULT_SSTI_PAYLOADS_FILE})")
    parser.add_argument("--threads", "-th", type=int, default=20, help="Number of threads for parallel testing (default: 20)")

    args = parser.parse_args()

    # ALWAYS test BOTH SQL injection and SSTI - no argument selection needed
    test_types = ["sql", "ssti"]

    print(f"\n" + "=" * 70)
    print(f"COMPREHENSIVE INJECTION SCANNER - FAST AS HELL! 🚀")
    print(f"=" * 70)
    print(f"Katana file: {args.katana_file}")
    print(f"Test types: {', '.join(test_types).upper()}")
    print(f"SQL delay: {args.delay} seconds")
    print(f"Threads: {args.threads}")
    print(f"Verbose mode: {'Enabled' if args.verbose else 'Disabled'}")
    print(f"Payload source: ALL categories from YAML files")
    print("=" * 70)

    # Initialize scanner with payload files and threading
    scanner = VulnerabilityScanner(
        delay=args.delay,
        verbose=args.verbose,
        sql_payloads_file=args.sql_payloads,
        ssti_payloads_file=args.ssti_payloads,
        threads=args.threads
    )

    # Process Katana file and scan
    processed_requests, tested_params = scanner.process_katana_file(args.katana_file, test_types)

    # Generate detailed report
    report_file, params_file = scanner.save_detailed_report()

    # Print summary
    print(f"\n" + "=" * 70)
    print(f"SCAN RESULTS SUMMARY:")
    print(f"=" * 70)
    print(f"  Requests processed: {processed_requests}")
    print(f"  Parameters tested: {tested_params}")
    print(f"  Total vulnerabilities found: {len(scanner.vulnerabilities)}")

    # Break down by vulnerability type
    sql_count = len([v for v in scanner.vulnerabilities if 'SQL' in v['type']])
    ssti_count = len([v for v in scanner.vulnerabilities if 'SSTI' in v['type']])

    if sql_count > 0:
        print(f"  SQL Injection vulnerabilities: {sql_count}")
    if ssti_count > 0:
        print(f"  SSTI vulnerabilities: {ssti_count}")

    # Show vulnerability details
    if scanner.vulnerabilities:
        print(f"\nVULNERABILITIES FOUND:")
        print("-" * 50)

        for i, vuln in enumerate(scanner.vulnerabilities, 1):
            print(f"{i}. {vuln['type']} - {vuln['confidence']} confidence")
            print(f"   Parameter: {vuln['parameter']} in {vuln['url']}")
            print(f"   Evidence: {vuln['evidence']}")
            if 'template_engine' in vuln:
                print(f"   Template Engine: {vuln['template_engine']}")
            print()

        print(f"📄 Detailed report saved to: {report_file}")
        print(f"📋 Tested parameters logged to: {params_file}")
    else:
        print(f"\n✅ No vulnerabilities detected.")
        print(f"📋 Tested parameters logged to: {params_file}")

    print("=" * 70)

    # Show some examples of evidence highlighting if SSTI found
    ssti_vulns = [v for v in scanner.vulnerabilities if 'SSTI' in v['type']]
    if ssti_vulns and args.verbose:
        print(f"\nEVIDENCE HIGHLIGHTING EXAMPLES:")
        print("-" * 40)
        for vuln in ssti_vulns[:2]:  # Show first 2 examples
            print(f"\nPayload: {vuln['payload']}")
            print(f"Evidence: {vuln['evidence']}")
            print(f"Response excerpt with highlighting:")
            print(vuln['response']['highlighted_content'][:200] + "...")
            print("-" * 40)

if __name__ == "__main__":
    main()
